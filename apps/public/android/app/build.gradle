plugins {
    id "com.android.application"
    id 'com.google.gms.google-services'
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "ae.gov.scad.bayaan.open"
    compileSdkVersion project.compileSdkVersion
    ndkVersion project.ndkVersion

    def keystoreProperties = new Properties()
    def keystorePropertiesFile = rootProject.file(project.keyStorePath)
    if (keystorePropertiesFile.exists()) {
        keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    }

    compileOptions {
        coreLibraryDesugaringEnabled = true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "ae.gov.scad.bayaan.open"
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        multiDexEnabled true

        ndk {
            moduleName "hello-android-jni"
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release

            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            signingConfig signingConfigs.release

            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        dataBinding true
    }

    packagingOptions {
        resources {
            excludes += 'META-INF/DEPENDENCIES'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'

    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'

    implementation platform('com.google.firebase:firebase-bom:33.7.0')
    implementation 'com.google.firebase:firebase-analytics'

    configurations.configureEach {
        exclude group: 'com.google.firebase', module: 'firebase-installations'
        exclude group: 'com.google.firebase', module: 'firebase-installations-interop'
    }

    implementation project(':firebase-installations')
    implementation project(':firebase-installations-interop')
}

subprojects {
    project.configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'com.android.support'
                    && !details.requested.name.contains('multidex') ) {
                details.useVersion "27.1.1"
            }
        }
    }

    resolutionStrategy.dependencySubstitution {
        substitute module('com.google.firebase:firebase-installations') with project(':firebase-installations')
        substitute module('com.google.firebase:firebase-installations-interop') with project(':firebase-installations-interop')
    }
}

// Exclude ArcGIS dependencies completely for this app
configurations.all {
    exclude group: 'com.esri'
}
