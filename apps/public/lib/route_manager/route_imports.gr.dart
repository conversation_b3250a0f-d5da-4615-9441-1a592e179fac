// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i26;
import 'package:collection/collection.dart' as _i37;
import 'package:flutter/foundation.dart' as _i32;
import 'package:flutter/material.dart' as _i28;
import 'package:scad_mobile_public/src/common/types.dart' as _i35;
import 'package:scad_mobile_public/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart'
    as _i29;
import 'package:scad_mobile_public/src/common/widgets/indicator_card/data/models/indicator_details_response.dart'
    as _i30;
import 'package:scad_mobile_public/src/features/authentication/presentation/pages/splash/splash.dart'
    as _i21;
import 'package:scad_mobile_public/src/features/chat_with_sme/presentation/pages/faq_page.dart'
    as _i9;
import 'package:scad_mobile_public/src/features/details_page/compare/presentation/pages/compare_details_screen.dart'
    as _i3;
import 'package:scad_mobile_public/src/features/details_page/compare/presentation/pages/select_comparable_screen.dart'
    as _i18;
import 'package:scad_mobile_public/src/features/details_page/compute/data/enums/compute_operations.dart'
    as _i36;
import 'package:scad_mobile_public/src/features/details_page/compute/presentation/pages/compute_details_screen.dart'
    as _i4;
import 'package:scad_mobile_public/src/features/details_page/compute/presentation/pages/select_computable_screen.dart'
    as _i19;
import 'package:scad_mobile_public/src/features/details_page/official_experimental/presentation/pages/official_experimental_details_screen.dart'
    as _i14;
import 'package:scad_mobile_public/src/features/domains/data/models/domain_classification_model.dart'
    as _i39;
import 'package:scad_mobile_public/src/features/domains/data/models/domain_model/domain_model.dart'
    as _i38;
import 'package:scad_mobile_public/src/features/domains/data/models/theme_subtheme_response.dart'
    as _i40;
import 'package:scad_mobile_public/src/features/domains/presentation/pages/theme_indicators_screen.dart'
    as _i23;
import 'package:scad_mobile_public/src/features/domains/presentation/pages/themes_page.dart'
    as _i24;
import 'package:scad_mobile_public/src/features/drawer_items/about_this_app/presentation/pages/about_this_app_screen.dart'
    as _i1;
import 'package:scad_mobile_public/src/features/drawer_items/contact_us/presentation/pages/contact_us_screen.dart'
    as _i5;
import 'package:scad_mobile_public/src/features/drawer_items/download_history/download_history_screen.dart'
    as _i7;
import 'package:scad_mobile_public/src/features/drawer_items/glossary/presentation/pages/glossary_screen.dart'
    as _i10;
import 'package:scad_mobile_public/src/features/drawer_items/terms_and_conditions/presentation/pages/terms_and_conditions_screen.dart'
    as _i22;
import 'package:scad_mobile_public/src/features/drawer_items/user_guide/user_guide_screen.dart'
    as _i25;
import 'package:scad_mobile_public/src/features/home/<USER>/models/update_response_model.dart'
    as _i27;
import 'package:scad_mobile_public/src/features/home/<USER>/pages/home/<USER>'
    as _i12;
import 'package:scad_mobile_public/src/features/home/<USER>/pages/home_navigation.dart'
    as _i11;
import 'package:scad_mobile_public/src/features/misc_screens/excel_preview_screen.dart'
    as _i8;
import 'package:scad_mobile_public/src/features/misc_screens/maintenance_screen.dart'
    as _i13;
import 'package:scad_mobile_public/src/features/misc_screens/pdf_preview_screen.dart'
    as _i15;
import 'package:scad_mobile_public/src/features/products/data/models/product_dashboard.dart'
    as _i31;
import 'package:scad_mobile_public/src/features/products/presentation/bloc/products_bloc.dart'
    as _i33;
import 'package:scad_mobile_public/src/features/products/presentation/pages/products_screen.dart'
    as _i16;
import 'package:scad_mobile_public/src/features/products/presentation/pages/tab_pages/dashboard_webpage_screen.dart'
    as _i6;
import 'package:scad_mobile_public/src/features/search/presentation/pages/search/search_screen.dart'
    as _i17;
import 'package:scad_mobile_public/src/features/settings/presentation/pages/app_update_screen.dart'
    as _i2;
import 'package:scad_mobile_public/src/features/settings/presentation/pages/settings_page.dart'
    as _i20;
import 'package:scad_mobile_public/src/utils/remote_config_utils/maintenance_status.dart'
    as _i34;

/// generated route for
/// [_i1.AboutThisAppScreen]
class AboutThisAppScreenRoute extends _i26.PageRouteInfo<void> {
  const AboutThisAppScreenRoute({List<_i26.PageRouteInfo>? children})
      : super(AboutThisAppScreenRoute.name, initialChildren: children);

  static const String name = 'AboutThisAppScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i1.AboutThisAppScreen();
    },
  );
}

/// generated route for
/// [_i2.AppUpdateScreen]
class AppUpdateScreenRoute
    extends _i26.PageRouteInfo<AppUpdateScreenRouteArgs> {
  AppUpdateScreenRoute({
    required bool isFromSettings,
    required _i27.UpdateResponse? appUpdateResponse,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          AppUpdateScreenRoute.name,
          args: AppUpdateScreenRouteArgs(
            isFromSettings: isFromSettings,
            appUpdateResponse: appUpdateResponse,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'AppUpdateScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AppUpdateScreenRouteArgs>();
      return _i2.AppUpdateScreen(
        isFromSettings: args.isFromSettings,
        appUpdateResponse: args.appUpdateResponse,
        key: args.key,
      );
    },
  );
}

class AppUpdateScreenRouteArgs {
  const AppUpdateScreenRouteArgs({
    required this.isFromSettings,
    required this.appUpdateResponse,
    this.key,
  });

  final bool isFromSettings;

  final _i27.UpdateResponse? appUpdateResponse;

  final _i28.Key? key;

  @override
  String toString() {
    return 'AppUpdateScreenRouteArgs{isFromSettings: $isFromSettings, appUpdateResponse: $appUpdateResponse, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AppUpdateScreenRouteArgs) return false;
    return isFromSettings == other.isFromSettings &&
        appUpdateResponse == other.appUpdateResponse &&
        key == other.key;
  }

  @override
  int get hashCode =>
      isFromSettings.hashCode ^ appUpdateResponse.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i3.CompareDetailsScreen]
class CompareDetailsScreenRoute
    extends _i26.PageRouteInfo<CompareDetailsScreenRouteArgs> {
  CompareDetailsScreenRoute({
    required String combinedId,
    _i28.Key? key,
    String? comparedIndicatorName,
    _i29.IndicatorType? indicatorType,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          CompareDetailsScreenRoute.name,
          args: CompareDetailsScreenRouteArgs(
            combinedId: combinedId,
            key: key,
            comparedIndicatorName: comparedIndicatorName,
            indicatorType: indicatorType,
          ),
          initialChildren: children,
        );

  static const String name = 'CompareDetailsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CompareDetailsScreenRouteArgs>();
      return _i3.CompareDetailsScreen(
        combinedId: args.combinedId,
        key: args.key,
        comparedIndicatorName: args.comparedIndicatorName,
        indicatorType: args.indicatorType,
      );
    },
  );
}

class CompareDetailsScreenRouteArgs {
  const CompareDetailsScreenRouteArgs({
    required this.combinedId,
    this.key,
    this.comparedIndicatorName,
    this.indicatorType,
  });

  final String combinedId;

  final _i28.Key? key;

  final String? comparedIndicatorName;

  final _i29.IndicatorType? indicatorType;

  @override
  String toString() {
    return 'CompareDetailsScreenRouteArgs{combinedId: $combinedId, key: $key, comparedIndicatorName: $comparedIndicatorName, indicatorType: $indicatorType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompareDetailsScreenRouteArgs) return false;
    return combinedId == other.combinedId &&
        key == other.key &&
        comparedIndicatorName == other.comparedIndicatorName &&
        indicatorType == other.indicatorType;
  }

  @override
  int get hashCode =>
      combinedId.hashCode ^
      key.hashCode ^
      comparedIndicatorName.hashCode ^
      indicatorType.hashCode;
}

/// generated route for
/// [_i4.ComputeDetailsScreen]
class ComputeDetailsScreenRoute
    extends _i26.PageRouteInfo<ComputeDetailsScreenRouteArgs> {
  ComputeDetailsScreenRoute({
    required String title,
    required _i30.IndicatorDetailsResponse indicatorDetails,
    required _i30.Security? security,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ComputeDetailsScreenRoute.name,
          args: ComputeDetailsScreenRouteArgs(
            title: title,
            indicatorDetails: indicatorDetails,
            security: security,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ComputeDetailsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ComputeDetailsScreenRouteArgs>();
      return _i4.ComputeDetailsScreen(
        title: args.title,
        indicatorDetails: args.indicatorDetails,
        security: args.security,
        key: args.key,
      );
    },
  );
}

class ComputeDetailsScreenRouteArgs {
  const ComputeDetailsScreenRouteArgs({
    required this.title,
    required this.indicatorDetails,
    required this.security,
    this.key,
  });

  final String title;

  final _i30.IndicatorDetailsResponse indicatorDetails;

  final _i30.Security? security;

  final _i28.Key? key;

  @override
  String toString() {
    return 'ComputeDetailsScreenRouteArgs{title: $title, indicatorDetails: $indicatorDetails, security: $security, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ComputeDetailsScreenRouteArgs) return false;
    return title == other.title &&
        indicatorDetails == other.indicatorDetails &&
        security == other.security &&
        key == other.key;
  }

  @override
  int get hashCode =>
      title.hashCode ^
      indicatorDetails.hashCode ^
      security.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i5.ContactUsScreen]
class ContactUsScreenRoute extends _i26.PageRouteInfo<void> {
  const ContactUsScreenRoute({List<_i26.PageRouteInfo>? children})
      : super(ContactUsScreenRoute.name, initialChildren: children);

  static const String name = 'ContactUsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i5.ContactUsScreen();
    },
  );
}

/// generated route for
/// [_i6.DashboardWebViewPage]
class DashboardWebViewPageRoute
    extends _i26.PageRouteInfo<DashboardWebViewPageRouteArgs> {
  DashboardWebViewPageRoute({
    required String title,
    _i31.TableauDashboardResponseItem? dashboard,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          DashboardWebViewPageRoute.name,
          args: DashboardWebViewPageRouteArgs(
            title: title,
            dashboard: dashboard,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'DashboardWebViewPageRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<DashboardWebViewPageRouteArgs>();
      return _i6.DashboardWebViewPage(
        title: args.title,
        dashboard: args.dashboard,
        key: args.key,
      );
    },
  );
}

class DashboardWebViewPageRouteArgs {
  const DashboardWebViewPageRouteArgs({
    required this.title,
    this.dashboard,
    this.key,
  });

  final String title;

  final _i31.TableauDashboardResponseItem? dashboard;

  final _i28.Key? key;

  @override
  String toString() {
    return 'DashboardWebViewPageRouteArgs{title: $title, dashboard: $dashboard, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DashboardWebViewPageRouteArgs) return false;
    return title == other.title &&
        dashboard == other.dashboard &&
        key == other.key;
  }

  @override
  int get hashCode => title.hashCode ^ dashboard.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i7.DownloadHistoryScreen]
class DownloadHistoryScreenRoute extends _i26.PageRouteInfo<void> {
  const DownloadHistoryScreenRoute({List<_i26.PageRouteInfo>? children})
      : super(DownloadHistoryScreenRoute.name, initialChildren: children);

  static const String name = 'DownloadHistoryScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i7.DownloadHistoryScreen();
    },
  );
}

/// generated route for
/// [_i8.ExcelPreviewScreen]
class ExcelPreviewScreenRoute
    extends _i26.PageRouteInfo<ExcelPreviewScreenRouteArgs> {
  ExcelPreviewScreenRoute({
    required String url,
    required String title,
    _i32.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ExcelPreviewScreenRoute.name,
          args: ExcelPreviewScreenRouteArgs(url: url, title: title, key: key),
          initialChildren: children,
        );

  static const String name = 'ExcelPreviewScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ExcelPreviewScreenRouteArgs>();
      return _i8.ExcelPreviewScreen(
        url: args.url,
        title: args.title,
        key: args.key,
      );
    },
  );
}

class ExcelPreviewScreenRouteArgs {
  const ExcelPreviewScreenRouteArgs({
    required this.url,
    required this.title,
    this.key,
  });

  final String url;

  final String title;

  final _i32.Key? key;

  @override
  String toString() {
    return 'ExcelPreviewScreenRouteArgs{url: $url, title: $title, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ExcelPreviewScreenRouteArgs) return false;
    return url == other.url && title == other.title && key == other.key;
  }

  @override
  int get hashCode => url.hashCode ^ title.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i9.FaqPage]
class FaqPageRoute extends _i26.PageRouteInfo<void> {
  const FaqPageRoute({List<_i26.PageRouteInfo>? children})
      : super(FaqPageRoute.name, initialChildren: children);

  static const String name = 'FaqPageRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i9.FaqPage();
    },
  );
}

/// generated route for
/// [_i10.GlossaryScreen]
class GlossaryScreenRoute extends _i26.PageRouteInfo<GlossaryScreenRouteArgs> {
  GlossaryScreenRoute({
    String initialSearchTerm = '',
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          GlossaryScreenRoute.name,
          args: GlossaryScreenRouteArgs(
            initialSearchTerm: initialSearchTerm,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'GlossaryScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GlossaryScreenRouteArgs>(
        orElse: () => const GlossaryScreenRouteArgs(),
      );
      return _i10.GlossaryScreen(
        initialSearchTerm: args.initialSearchTerm,
        key: args.key,
      );
    },
  );
}

class GlossaryScreenRouteArgs {
  const GlossaryScreenRouteArgs({this.initialSearchTerm = '', this.key});

  final String initialSearchTerm;

  final _i28.Key? key;

  @override
  String toString() {
    return 'GlossaryScreenRouteArgs{initialSearchTerm: $initialSearchTerm, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! GlossaryScreenRouteArgs) return false;
    return initialSearchTerm == other.initialSearchTerm && key == other.key;
  }

  @override
  int get hashCode => initialSearchTerm.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i11.HomeNavigation]
class HomeNavigationRoute extends _i26.PageRouteInfo<HomeNavigationRouteArgs> {
  HomeNavigationRoute({
    _i32.Key? key,
    int? screenTabIndex,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          HomeNavigationRoute.name,
          args: HomeNavigationRouteArgs(
            key: key,
            screenTabIndex: screenTabIndex,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeNavigationRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomeNavigationRouteArgs>(
        orElse: () => const HomeNavigationRouteArgs(),
      );
      return _i11.HomeNavigation(
        key: args.key,
        screenTabIndex: args.screenTabIndex,
      );
    },
  );
}

class HomeNavigationRouteArgs {
  const HomeNavigationRouteArgs({this.key, this.screenTabIndex});

  final _i32.Key? key;

  final int? screenTabIndex;

  @override
  String toString() {
    return 'HomeNavigationRouteArgs{key: $key, screenTabIndex: $screenTabIndex}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomeNavigationRouteArgs) return false;
    return key == other.key && screenTabIndex == other.screenTabIndex;
  }

  @override
  int get hashCode => key.hashCode ^ screenTabIndex.hashCode;
}

/// generated route for
/// [_i12.HomePage]
class HomePageRoute extends _i26.PageRouteInfo<HomePageRouteArgs> {
  HomePageRoute({
    required _i28.VoidCallback navToDomainsPage,
    required void Function(_i33.ProductTabType) navToProductsPage,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          HomePageRoute.name,
          args: HomePageRouteArgs(
            navToDomainsPage: navToDomainsPage,
            navToProductsPage: navToProductsPage,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'HomePageRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomePageRouteArgs>();
      return _i12.HomePage(
        navToDomainsPage: args.navToDomainsPage,
        navToProductsPage: args.navToProductsPage,
        key: args.key,
      );
    },
  );
}

class HomePageRouteArgs {
  const HomePageRouteArgs({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    this.key,
  });

  final _i28.VoidCallback navToDomainsPage;

  final void Function(_i33.ProductTabType) navToProductsPage;

  final _i28.Key? key;

  @override
  String toString() {
    return 'HomePageRouteArgs{navToDomainsPage: $navToDomainsPage, navToProductsPage: $navToProductsPage, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomePageRouteArgs) return false;
    return navToDomainsPage == other.navToDomainsPage && key == other.key;
  }

  @override
  int get hashCode => navToDomainsPage.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i13.MaintenanceScreen]
class MaintenanceScreenRoute
    extends _i26.PageRouteInfo<MaintenanceScreenRouteArgs> {
  MaintenanceScreenRoute({
    required _i34.MaintenanceStatus data,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          MaintenanceScreenRoute.name,
          args: MaintenanceScreenRouteArgs(data: data, key: key),
          initialChildren: children,
        );

  static const String name = 'MaintenanceScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MaintenanceScreenRouteArgs>();
      return _i13.MaintenanceScreen(data: args.data, key: args.key);
    },
  );
}

class MaintenanceScreenRouteArgs {
  const MaintenanceScreenRouteArgs({required this.data, this.key});

  final _i34.MaintenanceStatus data;

  final _i28.Key? key;

  @override
  String toString() {
    return 'MaintenanceScreenRouteArgs{data: $data, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MaintenanceScreenRouteArgs) return false;
    return data == other.data && key == other.key;
  }

  @override
  int get hashCode => data.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i14.OfficialExperimentalDetailsScreen]
class OfficialExperimentalDetailsScreenRoute
    extends _i26.PageRouteInfo<OfficialExperimentalDetailsScreenRouteArgs> {
  OfficialExperimentalDetailsScreenRoute({
    required String id,
    required String contentType,
    required String title,
    _i35.JSONObject? screenerPayload,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          OfficialExperimentalDetailsScreenRoute.name,
          args: OfficialExperimentalDetailsScreenRouteArgs(
            id: id,
            contentType: contentType,
            title: title,
            screenerPayload: screenerPayload,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'OfficialExperimentalDetailsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OfficialExperimentalDetailsScreenRouteArgs>();
      return _i14.OfficialExperimentalDetailsScreen(
        id: args.id,
        contentType: args.contentType,
        title: args.title,
        screenerPayload: args.screenerPayload,
        key: args.key,
      );
    },
  );
}

class OfficialExperimentalDetailsScreenRouteArgs {
  const OfficialExperimentalDetailsScreenRouteArgs({
    required this.id,
    required this.contentType,
    required this.title,
    this.screenerPayload,
    this.key,
  });

  final String id;

  final String contentType;

  final String title;

  final _i35.JSONObject? screenerPayload;

  final _i28.Key? key;

  @override
  String toString() {
    return 'OfficialExperimentalDetailsScreenRouteArgs{id: $id, contentType: $contentType, title: $title, screenerPayload: $screenerPayload, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OfficialExperimentalDetailsScreenRouteArgs) return false;
    return id == other.id &&
        contentType == other.contentType &&
        title == other.title &&
        screenerPayload == other.screenerPayload &&
        key == other.key;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      contentType.hashCode ^
      title.hashCode ^
      screenerPayload.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i15.PdfPreviewScreen]
class PdfPreviewScreenRoute
    extends _i26.PageRouteInfo<PdfPreviewScreenRouteArgs> {
  PdfPreviewScreenRoute({
    required String title,
    required String url,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          PdfPreviewScreenRoute.name,
          args: PdfPreviewScreenRouteArgs(title: title, url: url, key: key),
          initialChildren: children,
        );

  static const String name = 'PdfPreviewScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PdfPreviewScreenRouteArgs>();
      return _i15.PdfPreviewScreen(
        title: args.title,
        url: args.url,
        key: args.key,
      );
    },
  );
}

class PdfPreviewScreenRouteArgs {
  const PdfPreviewScreenRouteArgs({
    required this.title,
    required this.url,
    this.key,
  });

  final String title;

  final String url;

  final _i28.Key? key;

  @override
  String toString() {
    return 'PdfPreviewScreenRouteArgs{title: $title, url: $url, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PdfPreviewScreenRouteArgs) return false;
    return title == other.title && url == other.url && key == other.key;
  }

  @override
  int get hashCode => title.hashCode ^ url.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i16.Products]
class ProductsRoute extends _i26.PageRouteInfo<ProductsRouteArgs> {
  ProductsRoute({
    _i28.Key? key,
    bool isFromDetailsScreen = false,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ProductsRoute.name,
          args: ProductsRouteArgs(
            key: key,
            isFromDetailsScreen: isFromDetailsScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ProductsRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProductsRouteArgs>(
        orElse: () => const ProductsRouteArgs(),
      );
      return _i16.Products(
        key: args.key,
        isFromDetailsScreen: args.isFromDetailsScreen,
      );
    },
  );
}

class ProductsRouteArgs {
  const ProductsRouteArgs({this.key, this.isFromDetailsScreen = false});

  final _i28.Key? key;

  final bool isFromDetailsScreen;

  @override
  String toString() {
    return 'ProductsRouteArgs{key: $key, isFromDetailsScreen: $isFromDetailsScreen}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductsRouteArgs) return false;
    return key == other.key && isFromDetailsScreen == other.isFromDetailsScreen;
  }

  @override
  int get hashCode => key.hashCode ^ isFromDetailsScreen.hashCode;
}

/// generated route for
/// [_i17.SearchScreen]
class SearchScreenRoute extends _i26.PageRouteInfo<SearchScreenRouteArgs> {
  SearchScreenRoute({
    _i28.Key? key,
    _i17.SearchTypes? type,
    String? contentType,
    String? initialNodeIdForComparison,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          SearchScreenRoute.name,
          args: SearchScreenRouteArgs(
            key: key,
            type: type,
            contentType: contentType,
            initialNodeIdForComparison: initialNodeIdForComparison,
          ),
          initialChildren: children,
        );

  static const String name = 'SearchScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SearchScreenRouteArgs>(
        orElse: () => const SearchScreenRouteArgs(),
      );
      return _i17.SearchScreen(
        key: args.key,
        type: args.type,
        contentType: args.contentType,
        initialNodeIdForComparison: args.initialNodeIdForComparison,
      );
    },
  );
}

class SearchScreenRouteArgs {
  const SearchScreenRouteArgs({
    this.key,
    this.type,
    this.contentType,
    this.initialNodeIdForComparison,
  });

  final _i28.Key? key;

  final _i17.SearchTypes? type;

  final String? contentType;

  final String? initialNodeIdForComparison;

  @override
  String toString() {
    return 'SearchScreenRouteArgs{key: $key, type: $type, contentType: $contentType, initialNodeIdForComparison: $initialNodeIdForComparison}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SearchScreenRouteArgs) return false;
    return key == other.key &&
        type == other.type &&
        contentType == other.contentType &&
        initialNodeIdForComparison == other.initialNodeIdForComparison;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      type.hashCode ^
      contentType.hashCode ^
      initialNodeIdForComparison.hashCode;
}

/// generated route for
/// [_i18.SelectComparableScreen]
class SelectComparableScreenRoute
    extends _i26.PageRouteInfo<SelectComparableScreenRouteArgs> {
  SelectComparableScreenRoute({
    required _i30.IndicatorDetailsResponse indicatorDetails,
    _i35.JSONObject? screener,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          SelectComparableScreenRoute.name,
          args: SelectComparableScreenRouteArgs(
            indicatorDetails: indicatorDetails,
            screener: screener,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectComparableScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectComparableScreenRouteArgs>();
      return _i18.SelectComparableScreen(
        indicatorDetails: args.indicatorDetails,
        screener: args.screener,
        key: args.key,
      );
    },
  );
}

class SelectComparableScreenRouteArgs {
  const SelectComparableScreenRouteArgs({
    required this.indicatorDetails,
    this.screener,
    this.key,
  });

  final _i30.IndicatorDetailsResponse indicatorDetails;

  final _i35.JSONObject? screener;

  final _i28.Key? key;

  @override
  String toString() {
    return 'SelectComparableScreenRouteArgs{indicatorDetails: $indicatorDetails, screener: $screener, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SelectComparableScreenRouteArgs) return false;
    return indicatorDetails == other.indicatorDetails &&
        screener == other.screener &&
        key == other.key;
  }

  @override
  int get hashCode =>
      indicatorDetails.hashCode ^ screener.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i19.SelectComputableScreen]
class SelectComputableScreenRoute
    extends _i26.PageRouteInfo<SelectComputableScreenRouteArgs> {
  SelectComputableScreenRoute({
    required String indicatorId,
    required _i36.ComputeOperations operation,
    required List<_i30.Properties> propertiesList,
    required _i30.Security? security,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          SelectComputableScreenRoute.name,
          args: SelectComputableScreenRouteArgs(
            indicatorId: indicatorId,
            operation: operation,
            propertiesList: propertiesList,
            security: security,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectComputableScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectComputableScreenRouteArgs>();
      return _i19.SelectComputableScreen(
        indicatorId: args.indicatorId,
        operation: args.operation,
        propertiesList: args.propertiesList,
        security: args.security,
        key: args.key,
      );
    },
  );
}

class SelectComputableScreenRouteArgs {
  const SelectComputableScreenRouteArgs({
    required this.indicatorId,
    required this.operation,
    required this.propertiesList,
    required this.security,
    this.key,
  });

  final String indicatorId;

  final _i36.ComputeOperations operation;

  final List<_i30.Properties> propertiesList;

  final _i30.Security? security;

  final _i28.Key? key;

  @override
  String toString() {
    return 'SelectComputableScreenRouteArgs{indicatorId: $indicatorId, operation: $operation, propertiesList: $propertiesList, security: $security, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SelectComputableScreenRouteArgs) return false;
    return indicatorId == other.indicatorId &&
        operation == other.operation &&
        const _i37.ListEquality().equals(
          propertiesList,
          other.propertiesList,
        ) &&
        security == other.security &&
        key == other.key;
  }

  @override
  int get hashCode =>
      indicatorId.hashCode ^
      operation.hashCode ^
      const _i37.ListEquality().hash(propertiesList) ^
      security.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i20.SettingsScreen]
class SettingsScreenRoute extends _i26.PageRouteInfo<void> {
  const SettingsScreenRoute({List<_i26.PageRouteInfo>? children})
      : super(SettingsScreenRoute.name, initialChildren: children);

  static const String name = 'SettingsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i20.SettingsScreen();
    },
  );
}

/// generated route for
/// [_i21.SplashPage]
class SplashPageRoute extends _i26.PageRouteInfo<SplashPageRouteArgs> {
  SplashPageRoute({
    bool toAnimateLogo = true,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          SplashPageRoute.name,
          args: SplashPageRouteArgs(toAnimateLogo: toAnimateLogo, key: key),
          initialChildren: children,
        );

  static const String name = 'SplashPageRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SplashPageRouteArgs>(
        orElse: () => const SplashPageRouteArgs(),
      );
      return _i21.SplashPage(toAnimateLogo: args.toAnimateLogo, key: args.key);
    },
  );
}

class SplashPageRouteArgs {
  const SplashPageRouteArgs({this.toAnimateLogo = true, this.key});

  final bool toAnimateLogo;

  final _i28.Key? key;

  @override
  String toString() {
    return 'SplashPageRouteArgs{toAnimateLogo: $toAnimateLogo, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SplashPageRouteArgs) return false;
    return toAnimateLogo == other.toAnimateLogo && key == other.key;
  }

  @override
  int get hashCode => toAnimateLogo.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i22.TermsAndConditionsScreen]
class TermsAndConditionsScreenRoute
    extends _i26.PageRouteInfo<TermsAndConditionsScreenRouteArgs> {
  TermsAndConditionsScreenRoute({
    bool showAppDrawer = false,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          TermsAndConditionsScreenRoute.name,
          args: TermsAndConditionsScreenRouteArgs(
            showAppDrawer: showAppDrawer,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'TermsAndConditionsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TermsAndConditionsScreenRouteArgs>(
        orElse: () => const TermsAndConditionsScreenRouteArgs(),
      );
      return _i22.TermsAndConditionsScreen(
        showAppDrawer: args.showAppDrawer,
        key: args.key,
      );
    },
  );
}

class TermsAndConditionsScreenRouteArgs {
  const TermsAndConditionsScreenRouteArgs({
    this.showAppDrawer = false,
    this.key,
  });

  final bool showAppDrawer;

  final _i28.Key? key;

  @override
  String toString() {
    return 'TermsAndConditionsScreenRouteArgs{showAppDrawer: $showAppDrawer, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TermsAndConditionsScreenRouteArgs) return false;
    return showAppDrawer == other.showAppDrawer && key == other.key;
  }

  @override
  int get hashCode => showAppDrawer.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i23.ThemeIndicatorsScreen]
class ThemeIndicatorsScreenRoute
    extends _i26.PageRouteInfo<ThemeIndicatorsScreenRouteArgs> {
  ThemeIndicatorsScreenRoute({
    required String title,
    required _i38.DomainModel domain,
    required _i39.DomainClassificationModel classification,
    required _i40.SubTheme subTheme,
    required _i40.ThemeSubThemeResponse subDomain,
    _i28.Key? key,
    _i40.ScreenerConfiguration? screenerConfiguration,
    bool isFromMainScreen = false,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ThemeIndicatorsScreenRoute.name,
          args: ThemeIndicatorsScreenRouteArgs(
            title: title,
            domain: domain,
            classification: classification,
            subTheme: subTheme,
            subDomain: subDomain,
            key: key,
            screenerConfiguration: screenerConfiguration,
            isFromMainScreen: isFromMainScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ThemeIndicatorsScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ThemeIndicatorsScreenRouteArgs>();
      return _i23.ThemeIndicatorsScreen(
        title: args.title,
        domain: args.domain,
        classification: args.classification,
        subTheme: args.subTheme,
        subDomain: args.subDomain,
        key: args.key,
        screenerConfiguration: args.screenerConfiguration,
        isFromMainScreen: args.isFromMainScreen,
      );
    },
  );
}

class ThemeIndicatorsScreenRouteArgs {
  const ThemeIndicatorsScreenRouteArgs({
    required this.title,
    required this.domain,
    required this.classification,
    required this.subTheme,
    required this.subDomain,
    this.key,
    this.screenerConfiguration,
    this.isFromMainScreen = false,
  });

  final String title;

  final _i38.DomainModel domain;

  final _i39.DomainClassificationModel classification;

  final _i40.SubTheme subTheme;

  final _i40.ThemeSubThemeResponse subDomain;

  final _i28.Key? key;

  final _i40.ScreenerConfiguration? screenerConfiguration;

  final bool isFromMainScreen;

  @override
  String toString() {
    return 'ThemeIndicatorsScreenRouteArgs{title: $title, domain: $domain, classification: $classification, subTheme: $subTheme, subDomain: $subDomain, key: $key, screenerConfiguration: $screenerConfiguration, isFromMainScreen: $isFromMainScreen}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ThemeIndicatorsScreenRouteArgs) return false;
    return title == other.title &&
        domain == other.domain &&
        classification == other.classification &&
        subTheme == other.subTheme &&
        subDomain == other.subDomain &&
        key == other.key &&
        screenerConfiguration == other.screenerConfiguration &&
        isFromMainScreen == other.isFromMainScreen;
  }

  @override
  int get hashCode =>
      title.hashCode ^
      domain.hashCode ^
      classification.hashCode ^
      subTheme.hashCode ^
      subDomain.hashCode ^
      key.hashCode ^
      screenerConfiguration.hashCode ^
      isFromMainScreen.hashCode;
}

/// generated route for
/// [_i24.ThemesPage]
class ThemesPageRoute extends _i26.PageRouteInfo<ThemesPageRouteArgs> {
  ThemesPageRoute({
    String? domainId,
    _i28.Key? key,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ThemesPageRoute.name,
          args: ThemesPageRouteArgs(domainId: domainId, key: key),
          initialChildren: children,
        );

  static const String name = 'ThemesPageRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ThemesPageRouteArgs>(
        orElse: () => const ThemesPageRouteArgs(),
      );
      return _i24.ThemesPage(domainId: args.domainId, key: args.key);
    },
  );
}

class ThemesPageRouteArgs {
  const ThemesPageRouteArgs({this.domainId, this.key});

  final String? domainId;

  final _i28.Key? key;

  @override
  String toString() {
    return 'ThemesPageRouteArgs{domainId: $domainId, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ThemesPageRouteArgs) return false;
    return domainId == other.domainId && key == other.key;
  }

  @override
  int get hashCode => domainId.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i25.UserGuideScreen]
class UserGuideScreenRoute extends _i26.PageRouteInfo<void> {
  const UserGuideScreenRoute({List<_i26.PageRouteInfo>? children})
      : super(UserGuideScreenRoute.name, initialChildren: children);

  static const String name = 'UserGuideScreenRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i25.UserGuideScreen();
    },
  );
}
