import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../src/config/firebase_config/firebase_config.dart';
import '../src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import '../src/utils/app_utils/app_log.dart';
import 'route_imports.gr.dart';

class AnalyticsRouteObserver extends AutoRouteObserver {
  factory AnalyticsRouteObserver() => _mInstance;

  AnalyticsRouteObserver._();

  static final _mInstance = AnalyticsRouteObserver._();

  final _analyticsMap = <String, String>{
    //  Home routes
    HomePageRoute.name: 'Home',
    ThemesPageRoute.name: 'Domains',
    ProductsRoute.name: 'Products',

    //  Details Page Routes
    OfficialExperimentalDetailsScreenRoute.name: 'Details - Official Experimental',
    SelectComparableScreenRoute.name: 'Details - Select Comparable Indicators',
    CompareDetailsScreenRoute.name: 'Details - Compare',
    SelectComputableScreenRoute.name: 'Details - Select Computable Indicators',
    ComputeDetailsScreenRoute.name: 'Details - Compute',

    //  Drawer Item Routes
    GlossaryScreenRoute.name: 'Glossary',
    UserGuideScreenRoute.name: 'User Guide',
    DownloadHistoryScreenRoute.name: 'Download History',
    FaqPageRoute.name: 'FAQ',
    AboutThisAppScreenRoute.name: 'About This App',
    ContactUsScreenRoute.name: 'Contact Us',
    TermsAndConditionsScreenRoute.name: 'Terms and Conditions',
    SettingsScreenRoute.name: 'Settings',

    //  Misc Routes
    DashboardWebViewPageRoute.name: 'Tableau Dashboard',
    SearchScreenRoute.name: 'Search',
    AppUpdateScreenRoute.name: 'App Update',
  };

  static void homeNavigationListener(PersistentTabController controller) =>
      _mInstance._onHomeNavigationChanged(controller);

  void _logRoute(Route<dynamic>? route) {
    final screenName = _analyticsMap[route?.settings.name];
    if (screenName == null) return;
    final screenClass = route?.settings.name;

    final extra = <String, Object>{};
    final args = route?.settings.arguments;

    FirebaseConfig.instance.logScreenToAnalytics(
      screenName,
      screenClass ?? screenName,
      extra: extra,
    );
  }

  void _onHomeNavigationChanged(PersistentTabController controller) {
    final homePages = [
      HomePageRoute.name,
      ThemesPageRoute.name,
      ProductsRoute.name,
    ];

    final screenClass = homePages.elementAt(controller.index);
    final screenName = _analyticsMap[screenClass]!;
    AppLog.info('AnalyticsRouteObserver: BOTTOM NAV CHANGE: $screenName');
    FirebaseConfig.instance.logScreenToAnalytics(screenName, screenClass);
  }

  //  - if [previous] route is null, then likely the app is started
  //  - if [route] is null, then a new dialog or bottom sheet was opened, dialogs and bottom sheets are not
  //    handled via auto_router. this is why the issue
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE PUSHED: '
        '${previousRoute?.settings.name} -> ${route.settings.name}');
    _logRoute(route);
  }

  //  - if [route] was null then a dialog or bottom sheet was popped. the right to handle is by configuring the
  //    the bottom sheet or dialogs in auto_router
  //  - if [previousRoute] is null then probably the route popped to another dialog, i.e., 2 dialogs were open, now 1 is open
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE POPPED: '
        '${route.settings.name} -> ${previousRoute?.settings.name}');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE REPLACED: '
        '${oldRoute?.settings.name} -> ${newRoute?.settings.name}');
    _logRoute(newRoute);
  }
}
