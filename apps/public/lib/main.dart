import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'firebase_options.dart';
import 'my_app.dart';
import 'route_manager/route_imports.dart';
import 'src/config/app_config/hive_config.dart';
import 'src/config/dependancy_injection/injection_container.dart';
import 'src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'src/utils/app_utils/file_utils.dart';
import 'src/utils/hive_utils/api_cache/api_cache.dart';
import 'translations/codegen_loader.g.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await EasyLocalization.ensureInitialized();

  //initialise dependency injection
  await getInit();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.ensureInitialized();
  await remoteConfig.setConfigSettings(
    RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(minutes: 30),
    ),
  );

  remoteConfig.onConfigUpdated.listen((event) async {
    debugPrint('Firebase Remote Config: Incoming Changes');
    try {
      await remoteConfig.activate();
      await HiveApiCacheBox.instance.checkAndClearCache();
      final context = servicelocator<AppRouter>().navigatorKey.currentContext;
      context?.read<HomeBloc>().add(const CheckMaintenanceEvent(shouldFetch: false));
    } catch (e, st) {
      Completer<void>().completeError(e, st);
    }
  });

  //initialise Hive local storage
  await HiveConfig.initConfig();

  await FileUtils.init();

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(
      EasyLocalization(
        supportedLocales: const [Locale('en'), Locale('ar')],
        path: 'assets/translations',
        fallbackLocale: const Locale('en'),
        assetLoader: const CodegenLoader(),
        child: const MyApp(),
      ),
    );
  });
}
