import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:overlay_support/overlay_support.dart';
import 'route_manager/analytics_route.observer.dart';
import 'route_manager/route_imports.dart';
import 'src/common/widgets/debug/debug_overlay_button.dart';
import 'src/config/bloc_config/bloc_providers.dart';
import 'src/config/dependancy_injection/injection_container.dart';
import 'src/config/theme_config/theme_constants.dart';
import 'src/utils/app_utils/app_message.dart';
import 'src/utils/app_utils/device_type.dart';
import 'src/utils/hive_utils/hive_utils_settings.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late FlutterView _view;
  final _appRoutes = servicelocator<AppRouter>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    final display = _view.display;

    if (DeviceType.isTab(display)) {
      SystemChrome.setPreferredOrientations(<DeviceOrientation>[]);
    } else {
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _view = View.of(context);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: providers,
      child: OverlaySupport.global(
        child: ValueListenableBuilder(
          valueListenable: HiveUtilsSettings.box.listenable(),
          builder: (context, box, child) {
            final textSizeFactor = HiveUtilsSettings.textSizeFactor;

            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                viewInsets: MediaQuery.maybeViewInsetsOf(context),
                textScaler: TextScaler.linear(textSizeFactor),
              ),
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: Material(
                        child: Column(
                          children: [
                            Expanded(
                              child: MaterialApp.router(
                                localizationsDelegates: context.localizationDelegates,
                                supportedLocales: context.supportedLocales,
                                locale: context.locale,
                                routerConfig: _appRoutes.config(
                                  navigatorObservers: () => [
                                    AnalyticsRouteObserver(),
                                  ],
                                ),
                                debugShowCheckedModeBanner: false,
                                scaffoldMessengerKey: snackBarKey,
                                theme: AppThemeData.lightTheme,
                                darkTheme: AppThemeData.darkTheme,
                                themeMode: HiveUtilsSettings.getThemeMode,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (kDebugMode) ...[
                      const DebugOverlayButton(),
                    ],
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
