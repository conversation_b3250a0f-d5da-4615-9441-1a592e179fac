import 'package:flutter/material.dart';
import '../../hive_utils/hive_utils_settings.dart';

class AppColors {
  AppColors._();

  static const red = Color(0xFFEB0029);
  static const redShade1 = Color(0xFFF15642);

  static const white = Color(0xFFFFFFFF);
  static const whiteShade1 = Color(0x1AFFFFFF);
  static const whiteShade2 = Color(0x99FFFFFF);
  static const whiteShade3 = Color(0xFFEFF6FF);
  static const whiteShade4 = Color(0xFFE8F3FF);
  static const whiteShade5 = Color(0xffF8F8F8);
  static const whiteShade6 = Color(0x77FFFFFF);
  static const whiteShade7 = Color(0xFFF6F9FF);
  static const whiteShade8 = Color(0xFFF1F5FF);
  static const whiteShade9 = Color(0xFFF4F4F4);

  static const black = Color(0xFF000000);
  static const blackShade1 = Color(0xFF1E2937);
  static const blackShade2 = Color(0xFF37516E);
  static const blackShade3 = Color(0xFF05264A);
  static const blackShade4 = Color(0xFF4A5662);
  static const blackShade5 = Color(0xFF181818);
  static const blackShade6 = Color(0xFF313131);
  static const blackShade7 = Color(0xFF575757);
  static const blackShade8 = Color(0xFF585858);
  static const blackShade9 = Color(0xFF1A1A1A);
  static const blackShade10 = Color(0xFF485068);
  static const blackShade20 = Color(0xFF171717);
  static const blackShade21 = Color(0xFF252525);
  static const blackShade23 = Color(0xFF393939);

  static const blueShade1 = Color(0x192587FC);
  static const blueShade2 = Color(0xff152f5e);
  static const blueShade3 = Color(0xff26559d);
  static const blueShade4 = Color(0x6D172E5C);
  static const blueShade10 = Color(0xFF093077);
  static const blueShade9 = Color(0xFF1955B1);
  static const blueShade6 = Color(0xFF2787FB);
  static const blueShade7 = Color(0xFF3B88DF);
  static const blueShade8 = Color(0xFFEAF3FF);
  static const blueShade11 = Color(0xFF0092E8);
  static const blueShade12 = Color(0xFF2B5281);
  static const blueShade13 = Color(0xFFDAEBFF);
  static const blueShade14 = Color(0xFF012A9A);
  static const blueShade18 = Color(0xFFDBEAFF);
  static const blueShade20 = Color(0xFFF4F8FB);
  static const blueShade21 = Color(0xFFE4E8EB);
  static const blueShade22 = Color(0xFF0054B8);
  static const blueShade23 = Color(0xFF235094);
  static const blueShade24 = Color(0xFF396EA5);
  static const blueShade25 = Color(0xFF79A0C9);
  static const blueShade26 = Color(0xFFAECDED);
  static const blueShade27 = Color(0xFF1C407B);
  static const blueShade32 = Color(0xFF1F466F);
  static const blueShade33 = Color(0xFF355D87);
  static const blueShade34 = Color(0xFF184575);
  static const blueShade35 = Color(0xFF003998);
  static const blueShade36 = Color(0xFF37506A);
  static const blueShade37 = Color(0x4C2587FC);
  static const blueShade38 = Color(0x1A396EA5);
  static const blueShade39 = Color(0xff4370FF);
  static const blueShade40 = Color(0xff2687FD);
  static const blueShade41 = Color(0xffADD2FF);
  static const blueLight = Color(0xFF0054B8);
  static const blueLightOld = Color(0xFF2687FD);
  static const blueDark = Color(0xFF3288F1);
  static const skyBlue = Color(0xFFAFCCF0);
  static const blueDarkShade1 = Color(0xFF3267FF);
  static const blueGreyShade1 = Color(0xFF364151);

  static const grey = Color(0xFF6A7180);
  static const greyShade1 = Color(0xFFD1D5DA);
  static const greyShade2 = Color(0xFFE7EEFA);
  static const greyShade3 = Color(0xFFCCDDF1);
  static const greyShade4 = Color(0xFF9DA2AE);
  static const greyShade5 = Color(0xFFD6DADF);
  static const greyShade6 = Color(0xFF707B9C);
  static const greyShade7 = Color(0xFFFAFAFA);
  static const greyShade8 = Color(0xFFF3F4F6);
  static const greyShade9 = Color(0xFFCDD4DB);
  static const greyShade10 = Color(0xFFefeff0);
  static const greyShade11 = Color(0xFF9BA8B7);
  static const greyShade12 = Color(0x146F7A9C);
  static const greyShade13 = Color(0x4CD1D5DA);
  static const greyShade15 = Color(0xFFD9D9D9);
  static const greyShade15_1 = Color(0xFFD4E5F9);
  static const greyShade16 = Color(0xFF323232);
  static const greyShade17 = Color(0xFFA3B1BF);
  static const greyShade18 = Color(0xFF3E546A);
  static const greyShade19 = Color(0xFF1A395A);
  static const greyShade20 = Color(0xFFEDEAEA);
  static const greyShade21 = Color(0xFF4A5662);

  static const green = Color(0xFF1DAB85);
  static const blackTextTile = Color(0xFF364151);
  static const greyNotch = Color(0xFFD5DADF);
  static const blueButton = Color(0xFF2787FB);
  static const blackChatText = Color(0xFF4A5662);
  static const greySwitchOff = Color(0xFFCDD4DB);
  static const greySlider = Color(0x471847DF);
  static const blueSliderThumb = Color(0xFF1847DF);
  static const blueSliderThumbOuter = Color(0xFF92A2E1);
  static const blueSelectedRadio = Color(0xFFE9F3FF);
  static const redButton = Color(0xFFE53232);
  static const violet = Color(0xFFA066AA);

  static const shadow1 = Color(0x0A4E4F51);
  static const shadow2 = Color(0x0A4E4F52);
  static const shadow3 = Color(0x0F000000);
  static const shadow4 = Color(0xFF576AA2);

  static const readMessageDot = Color(0xFFE6F1FF);
  static const notificationDivider = Color(0xFFE7F2FF);
  static const neomorphicLight = Color(0xFFF1F1F2);
  static const neomorphicDark = Color(0xFF2E2E2E);
  static const greyBorder = Color(0xFF3F3F3F);
  static const indicatorCardDark = Color(0xFF232323);
  static const greyShade14 = Color(0xFF464646);
  static const drawerDark = Color(0xFF233E58);

  static const disabledGrey = Color(0xFFC4D2E3);

  static const blueGradientShade1 = Color(0xFF028AE2);
  static const blueGradientShade2 = Color(0xFF0334A1);
  static const blueGradientShade3 = Color(0xFF0185DE);
  static const blueGradientShade4 = Color(0xFF0233A1);
  static const blueShadeTabInset = Color(0xFFA8C5E3);
  static const blueGreyShade2 = Color(0xFF37506A);
  static const blueTitleText = Color(0xFF052445);

  /// blue shades
  static const blue30619D = Color(0Xff30619D);
  static const blueCFDEEE = Color(0XffCFDEEE);
  static const selectedChipBlue = Color(0xFF2587FC);
  static const lightBlueContainer = Color(0xFFE8F0FD);
  static const blueShade15 = Color(0xFF018EE5);
  static const blueShade16 = Color(0x472587FC);
  static const blueShade17 = Color(0x662587FC);
  static const blueShade19 = Color(0xFF4880DC);
  static const lightBlue = Color(0xffE8EEFB);
  static const blueShade28 = Color(0xFFE6ECF3);
  static const blueShade29 = Color(0xFFF2F7FC);
  static const blueShade30 = Color(0xff19386E);
  static const blueShade31 = Color(0xff0C4A8E);
  static const blueShade42 = Color(0xFFC6DDF5);

  /// red shades
  static const redE5634A = Color(0xffE5634A);
  static const redC7525C = Color(0xFFC7525C);

  /// green shades
  static const green3BD6AD = Color(0xff3BD6AD);
  static const green98C21B = Color(0xff98c21b);
  static const green6BAD49 = Color(0xFF6BAD49);

  /// grey shades
  static const greyFAFAFA = Color(0xffFAFAFA);
  static const greyF3F4F6 = Color(0xffF3F4F6);
  static const greyF3F3F3 = Color(0xffF3F3F3);
  static const greyF2F2F3 = Color(0xffF2F2F3);
  static const greyECECEC = Color(0xffECECEC);

  static const green2 = Color(0xFF2ACFC5);
  static const green3 = Color(0xFF81E8EE);
  static const green4 = Color(0xFF5EBD67);
  static const greenShade5 = Color(0xFF3b9d89);
  static const greenShade6 = Color(0xFF456868);
  static const greenLight = Color(0xFF38D6AD);
  static const chartGreen = Color(0xFF9AD75D);
  static const chartBlue = Color(0xFF796DFA);
  static const purple = Color(0xFFA855CB);
  static const purple2 = Color(0xFF9772E4);
  static const amber = Color(0xFFE3C34E);
  static const amberShade2 = Color(0xFFffa545);
  static const blue2 = Color(0xFF3C6FF6);
  static const blue3 = Color(0xFF15ADEC);
  static const blue4 = Color(0xFF2188ff);
  static const brown = Color(0xFFEE8B42);
  static const compareChartLine = Color(0xFFA660FF);
  static const redWarning = Color(0xFFBA0202);
  static const maroon = Color(0xFF800000);

  static const colorF4E7E9 = Color(0xFFF4E7E9);

  /// TreeMap P: Positive, N: Negative
  static const treeMapP1 = Color(0xFFC2DDCE);
  static const treeMapP2 = Color(0xFF92C8AA);
  static const treeMapP3 = Color(0xFF61B285);
  static const treeMapP4 = Color(0xFF319D61);
  static const treeMapP5 = Color(0xFF00873C);
  static const treeMapN1 = Color(0xFFF5CBCB);
  static const treeMapN2 = Color(0xFFF8A5A5);
  static const treeMapN3 = Color(0xFFFA7F7F);
  static const treeMapN4 = Color(0xFFFD5959);
  static const treeMapN5 = Color(0xFFFF3333);
  static const treeMapNeutral = Color(0xFFF2F2F2);
  static const spatialAnalyticsRed = Color(0xFFd75e5d);
  static const spatialAnalyticsBlue = Color(0xFF594ede);
  static const spatialAnalyticsPink = Color(0xFFFF00CC);
  static const spatialAnalyticsBlueLight = Color(0xFF297DE3);

  static const scaffoldBackgroundLight = Color(0xFFE5EFFB);
  static const scaffoldBackgroundDark = Color(0xFF0F3155);

  static Color get scaffoldBackground => HiveUtilsSettings.isLightMode ? scaffoldBackgroundLight : scaffoldBackgroundDark;

  static Color get scaffoldBackgroundLand => HiveUtilsSettings.isLightMode ? greyF2F2F3 : blackShade21;

  static Color get blackShade1OrWhite => HiveUtilsSettings.isLightMode ? blackShade1 : white;

  static Color get whiteOrBlack => HiveUtilsSettings.isLightMode ? white : black;

  static Color get blue => HiveUtilsSettings.isLightMode ? blueLight : blueDark;

  static List<Color> get chartColorSetList => HiveUtilsSettings.isLightMode ? chartColorSet : chartColorSetDark;
  // length of chartColorSet and chartColorSetDark should be equal
  static const chartColorSet = [
    blueShade30,
    green3BD6AD,
    chartBlue,
    Color(0xFF2fccec),
    Color(0xFF873600),
    Color(0xff4363d8),
    Color(0xfff58231),
    Color(0xff911eb4),
    Color(0xfff032e6),
    Color(0xfffabed4),
    Color(0xffbfef45),
    Color(0xff9A6324),
    Color(0xffaaffc3),
    amber,
    red,
    Color(0xff800000),
    Color(0xffdcbeff),
    Color(0xff808000),
    Color(0xffffd8b1),
  ];
  static const chartColorSetDark = [
    blueShade19,
    green3BD6AD,
    chartBlue,
    Color(0xFF2fccec),
    treeMapP4,
    Color(0xff4363d8),
    Color(0xfff58231),
    blueShade18,
    Color(0xfff032e6),
    Color(0xfffabed4),
    Color(0xffbfef45),
    Color(0xff9A6324),
    Color(0xffaaffc3),
    amber,
    red,
    Color(0xff800000),
    Color(0xffdcbeff),
    Color(0xff808000),
    Color(0xffffd8b1),
  ];

  static const aiGlowGradient1 = Color(0xFF45DAFF);
  static const aiGlowGradient2 = Color(0xFF89BEFF);
  static const aiGlowGradient3 = Color(0xFF0590FB);
  static const aiGlowGradient4 = Color(0xFF85FFF1);
  static const aiGlowGradient5 = Color(0xFF64A5FF);

  static const cyanDark = Color(0xFF0594C9);
  static const cyanLight = Color(0xFF4AC2E0);
}
