import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'maintenance_status.dart';

class RemoteConfigUtils {
  RemoteConfigUtils._();

  /// Gets maintenance status with an option parameter to force sync with remote data
  static Future<MaintenanceStatus> getMaintenanceStatusSync({bool forceFetch = false}) async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    if (forceFetch) {
      await remoteConfig.fetchAndActivate();
    }

    return getMaintenanceStatus();
  }

  /// Gets current data without any sync from remote
  static MaintenanceStatus getMaintenanceStatus() {
    final remoteConfig = FirebaseRemoteConfig.instance;

    final modules = <String>[];
    try {
      final modulesString = remoteConfig.getString('modules');
      modules.addAll(List<String>.from(jsonDecode(modulesString) as List));
    } catch (e) {
      //  ignore: json decode
    }

    final Map<String, RemoteConfigValue> data = remoteConfig.getAll();
    return MaintenanceStatus(
      titleEn: data['title_en']?.asString(),
      descriptionEn: data['description_en']?.asString(),
      fromEn: data['from_en']?.asString(),
      toEn: data['to_en']?.asString(),
      titleAr: data['title_ar']?.asString(),
      descriptionAr: data['description_ar']?.asString(),
      fromAr: data['from_ar']?.asString(),
      toAr: data['to_ar']?.asString(),
      active: data['active']?.asBool() ?? false,
      modules: modules,
    );
  }
}
