import '../../common/types.dart';

class MaintenanceStatus {
  MaintenanceStatus({
    this.titleEn,
    this.descriptionEn,
    this.fromEn,
    this.toEn,
    this.titleAr,
    this.descriptionAr,
    this.fromAr,
    this.toAr,
    this.active = false,
    this.modules = const <String>[],
  });

  final String? titleEn;
  final String? descriptionEn;
  final String? fromEn;
  final String? toEn;
  final String? titleAr;
  final String? descriptionAr;
  final String? fromAr;
  final String? toAr;
  final bool active;
  final List<String> modules;

  bool get isFullMaintenance => active && modules.contains('all');

  JSONObject toJson() => {
        'title_en': titleEn,
        'description_en': descriptionEn,
        'from_en': fromEn,
        'to_en': toEn,
        'title_ar': titleAr,
        'description_ar': descriptionAr,
        'from_ar': fromAr,
        'to_ar': toAr,
        'active': active,
        'modules': modules,
      };
}
