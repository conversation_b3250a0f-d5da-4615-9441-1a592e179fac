import 'dart:io';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'app_log.dart';

class AppCache {
  AppCache._();

  static Future<void> clearAll() async {
    try {
      final Directory dir = await getApplicationCacheDirectory();
      if (dir.existsSync()) {
        await dir.delete(recursive: true);
      }
    } catch (e, s) {
      AppLog.error(e, s, 'App cache directory cleaned');
    }
  }

  static Future<void> clearIOSCacheDB() async {
    try {
      if (Platform.isIOS) {
        final String dir =
            '${(await getApplicationCacheDirectory()).path}/${(await PackageInfo.fromPlatform()).packageName}';

        final List<File> cacheFiles = [
          File('$dir/Cache.db'),
          File('$dir/Cache.db-wal'),
          File('$dir/Cache.db-shm'),
        ];

        cacheFiles.toList().forEach((file) async {
          try {
            if (file.existsSync()) {
              await file.delete(recursive: true);
              AppLog.info('IOS cache file deleted. (${file.path})');
            }
          } catch (e, s) {
            AppLog.error(e, s, 'Unable to delete IOS cache file. (${file.path})');
          }
        });
      }
    } catch (e, s) {
      AppLog.error(e, s, 'Unable to delete IOS cache files');
    }
  }
}
