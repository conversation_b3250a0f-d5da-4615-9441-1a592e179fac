import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;
import '../../../route_manager/route_imports.dart';
import '../../config/dependancy_injection/injection_container.dart';

const double kTabWidthBreakpoint = 600;

class DeviceType {
  static bool isDirectionRTL(BuildContext context) {
    return intl.Bidi.isRtlLanguage(
        Localizations.localeOf(context).languageCode);
  }

  static bool isTab([Display? display]) {
    if (display == null) {
      final context = servicelocator<AppRouter>().navigatorKey.currentContext!;
      display = View.maybeOf(context)?.display;
    }

    if (display == null) {
      return isTabDevice(kTabWidthBreakpoint);
    }

    final size = display.size.width / display.devicePixelRatio;
    return size >= kTabWidthBreakpoint;
  }

  static bool isTabDevice([double maxSize = 400]) {
    final BuildContext context = servicelocator<AppRouter>().navigatorKey.currentContext!;
    final size = MediaQuery.sizeOf(context).width / MediaQuery.devicePixelRatioOf(context);
    return size >= maxSize;
  }

  static bool isLandsCape(BuildContext context) =>
      MediaQuery.of(context).orientation == Orientation.landscape;
}
