import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:path_provider/path_provider.dart';
import '../../config/app_config/api_config.dart';
import 'app_log.dart';
import 'app_message.dart';
import '../hive_utils/hive_utils_settings.dart';
import '../../../translations/locale_keys.g.dart';

typedef OnDownloadSuccess = void Function(String fileName, String filePath);
typedef OnDownloadFailed = void Function(dynamic error);

class DownloadService {
  DownloadService(
    this.url, {
    this.filename,
    this.folder,
    this.directoryType = downloadsDir,
    this.isAuthenticated = true,
    this.onDownloadSuccess,
    this.onDownloadFailed,
    this.notify = true,
  });

  final String url;
  final String? filename;

  /// Select from one of the 3 predefined types:
  ///
  /// [downloadsDir], [supportDir], [cacheDir]
  final int directoryType;
  final String? folder;

  /// If access token needs to be added to the download request
  final bool isAuthenticated;
  final OnDownloadSuccess? onDownloadSuccess;
  final OnDownloadFailed? onDownloadFailed;

  /// This will show downloading message when download starts
  final bool notify;

  static const downloadsDir = 1;
  static const supportDir = 2;
  static const cacheDir = 3;

  Future<void> start() async {
    if (notify) {
      AppMessage.showOverlayNotificationSuccess(
        message: LocaleKeys.downloading.tr(),
      );
    }

    final dirPath = await _getDirectoryPath();
    final downloadableUrl = _getCmsDownloadableUrl(url);
    final filename = _getFilename(dirPath);
    final filePath = '$dirPath/$filename';

    AppLog.info(
      'Download Started: \n'
      'URL: $downloadableUrl\n'
      'PATH: $filePath\n'
      'AUTH: $isAuthenticated',
    );

    BaseOptions? baseOptions;
    if (isAuthenticated) {
      baseOptions = BaseOptions(
        headers: {
          'Accept-Language': HiveUtilsSettings.appLanguage,
          // 'Authorization': 'Bearer ${HiveUtilsAuth.getToken()}',
        },
      );
    }
    await Dio(baseOptions).download(downloadableUrl, filePath);

    if (File(filePath).existsSync() && File(filePath).lengthSync() > 0) {
      AppLog.info('Download completed: $filePath');
      onDownloadSuccess?.call(filename, filePath);
    } else {
      AppLog.info('Error: Failed to download');
      onDownloadFailed?.call('Failed to download file');
    }
  }

  Future<String> _getDirectoryPath() async {
    String dirPath;
    switch (directoryType) {
      case downloadsDir:
        if (Platform.isIOS) {
          final Directory directory = await getApplicationDocumentsDirectory();
          dirPath = '${directory.path}/Download';
        } else {
          Directory? directory = Directory('/storage/emulated/0');
          if (!directory.existsSync()) {
            directory = await getExternalStorageDirectory();
          }
          dirPath = '${directory?.path}/Download/BayaanApp';
        }
      case supportDir:
        dirPath = (await getApplicationSupportDirectory()).path;
      case cacheDir:
        dirPath = (await getApplicationCacheDirectory()).path;
      default:
        throw Exception('undefined type');
    }

    if (folder != null) {
      dirPath += '/$folder';
    }
    if (!Directory(dirPath).existsSync()) {
      await Directory(dirPath).create(recursive: true);
    }
    return dirPath;
  }

  String _getCmsDownloadableUrl(String url) {
    const String regex = r'https:\/\/.*-cms(.|\-)*.scad\.gov\.ae\/sites\/';
    final hasMatch = RegExp(regex).hasMatch(url);
    if (hasMatch) {
      final String filepath = url.replaceFirst(RegExp(regex), '');
      return '${ApiConfig.ifpApiPath}/common/download?filename=$filepath';
    }
    return url;
  }

  String _getFilename(String path) {
    final suggestedFilename = filename ??
        Uri.decodeQueryComponent(
          url.substring(url.lastIndexOf('/') + 1),
        ).replaceAll(' ', '');

    return _getFilenameWithoutConflict(path, suggestedFilename);
  }

  String _getFilenameWithoutConflict(String path, String suggestedFilename, [int i = 0]) {
    final arr = suggestedFilename.split('.');
    var actualFilename = arr.sublist(0, arr.length - 1).join('_');
    final extension = arr.last;
    if (i != 0) {
      actualFilename += ' ($i)';
    }
    final file = File('$path/$actualFilename.$extension');
    final doesFileExist = file.existsSync();
    if (doesFileExist) {
      return _getFilenameWithoutConflict(path, suggestedFilename, i + 1);
    }
    return '$actualFilename.$extension';
  }
}
