import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path/path.dart' as path;
import '../../config/app_config/api_config.dart';
import 'app_log.dart';
import 'app_message.dart';
import 'file_utils.dart';
import '../hive_utils/hive_utils_settings.dart';
import '../../../translations/locale_keys.g.dart';

class DownloadHelper {
  DownloadHelper._();

  static Future<String?> downloadFileToDownloadsDir({
    required String url,
    BuildContext? context,
    String? fileName,
    String? folder = '',
    bool checkFileExists = false,
    ValueNotifier<DownloadProgress?>? progress,
    void Function(String filePath)? onComplete,
    void Function(String error)? onError,
    bool isSmallFile = false,
    Map<String, String> headers = const {},
    String? dirFilePath,
  }) async {
    if (context != null) {
      AppMessage.showOverlayNotificationSuccess(message: LocaleKeys.downloading.tr());
    }
    try {
      String filePath = '';
      String dirPath ='';
      String downloadFileName = '';

      if(dirFilePath != null){
        filePath = dirFilePath;
        dirPath = path.dirname(dirFilePath);
        downloadFileName = path.basename(dirFilePath);

      } else {
        dirPath = await getDownloadsDir();

        downloadFileName = fileName ?? FileUtils.getFileName(url: url);

        String downloadFolder = folder ?? '';
        if (downloadFolder.startsWith('/')) {
          downloadFolder = downloadFolder.substring(1);
        }
        if (downloadFolder.endsWith('/')) {
          downloadFolder = downloadFolder.substring(0, downloadFolder.length - 1);
        }
        if (downloadFolder.isNotEmpty) {
          dirPath += '/$downloadFolder';
        }

        filePath = '$dirPath/$downloadFileName';
      }

      if (!Directory(dirPath).existsSync()) {
        await Directory(dirPath).create(recursive: true);
      }
      if (checkFileExists && File(filePath).existsSync()) {
        if (isSmallFile) {
          return filePath;
        }
        onComplete?.call(filePath);
      }

      (dirPath, downloadFileName) = _renameFileIfExists(dirPath, downloadFileName);

      filePath = '$dirPath/$downloadFileName';

      if (isSmallFile) {
        final File? file = await _downloadFileSmallFile(
          url,
          filePath,
          progress,
          headers: headers,
        );

        if (file != null) {
          if (context != null) {
            AppMessage.showOverlayNotificationSuccess(
              title: LocaleKeys.fileDownloaded.tr(),
              message: downloadFileName,
              onTap: () async {
                unawaited(openFile(filePath));
              },
            );
          }
          return file.path;
        } else {
          if (context != null) {
            AppMessage.showOverlayNotificationError(message: LocaleKeys.unableToDownloadFile.tr());
          }
        }
      } else {
        await _downloadLargeFile(
          url,
          filePath,
          progress,
          headers: headers,
          onComplete: (String filePath) {
            onComplete?.call(filePath);
            if (context != null) {
              AppMessage.showOverlayNotificationSuccess(
                title: LocaleKeys.fileDownloaded.tr(),
                message: downloadFileName,
                onTap: () async {
                  unawaited(openFile(filePath));
                },
              );
            }
          },
          onError: (String error) {
            onError?.call(error);
            if (context != null) {
              AppMessage.showOverlayNotificationError(message: LocaleKeys.unableToDownloadFile.tr());
            }
          },
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);

      // onError?.call(e.toString());
      onError?.call(LocaleKeys.somethingWentWrong.tr());
      if (context != null) {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
      }
    }
    return null;
  }

  static Future<void> downloadFileToCacheDir({
    required String url,
    required String folder,
    String? fileName,
    bool replace = true,
    bool checkFileExists = false,
    ValueNotifier<DownloadProgress?>? progress,
    void Function(String filePath)? onComplete,
    void Function(String error)? onError,
    Map<String, String> headers = const {},
  }) async {
    try {
      String downloadFolder = folder;
      if (downloadFolder.startsWith('/')) {
        downloadFolder = downloadFolder.substring(1);
      }
      if (downloadFolder.endsWith('/')) {
        downloadFolder = downloadFolder.substring(0, downloadFolder.length - 1);
      }

      final String dirPath = '${FileUtils.appCacheDirectoryPath}/$downloadFolder';
      final String filePath = '$dirPath/${fileName ?? FileUtils.getFileName(url: url)}';

      if (!Directory(dirPath).existsSync()) {
        await Directory(dirPath).create(recursive: true);
      }

      if (checkFileExists && File(filePath).existsSync()) {
        onComplete?.call(filePath);
      }
      await _downloadLargeFile(
        url,
        filePath,
        progress,
        onComplete: onComplete,
        onError: onError,
        headers: headers,
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      onError?.call(LocaleKeys.somethingWentWrong.tr());
    }
  }

  static Future<void> _downloadLargeFile(
      String url,
      String filePath,
      ValueNotifier<DownloadProgress?>? progress, {
        required Map<String, String> headers,
        void Function(String filePath)? onComplete,
        void Function(String error)? onError,
      }) async {
    String downloadUrl = url;

    const String regex = r'https:\/\/.*-cms(.|\-)*.scad\.gov\.ae\/sites\/';
    final hasMatch = RegExp(regex).hasMatch(url);
    if (hasMatch) {
      final String filepath = url.replaceFirst(RegExp(regex), '');
      downloadUrl = '${ApiConfig.ifpApiPath}/common/download?filename=$filepath';
    }

    final Dio dio = Dio(
      BaseOptions(
        // baseUrl: '${Uri.parse(url).scheme}://${Uri.parse(url).host}',
        headers: {
          'Accept-Language': HiveUtilsSettings.appLanguage,
        }..addAll(headers),
      ),
    );

    await dio.download(
      downloadUrl,
      filePath,
      onReceiveProgress: (int received, int total) {
        if (total == 0) return;

        progress?.value = DownloadProgress(downloaded: received, total: total);
        // print('percentage: ${(received / total * 100).toStringAsFixed(0)}%');
      },
    );

    if (File(filePath).existsSync() && File(filePath).lengthSync() > 0) {
      onComplete?.call(filePath);
    } else {
      onError?.call(LocaleKeys.unableToDownloadFile.tr());
    }
  }

  static Future<File?> _downloadFileSmallFile(
      String url,
      String filePath,
      ValueNotifier<DownloadProgress?>? progress, {
        required Map<String, String> headers,
        void Function(String filePath)? onComplete,
      }) async {
    try {
      final HttpClient httpClient = HttpClient();

      HttpClientRequest request;

      const String regex = r'https:\/\/.*-cms(.|\-)*.scad\.gov\.ae\/sites\/';
      final hasMatch = RegExp(regex).hasMatch(url);
      if (hasMatch) {
        final String filepath = url.replaceFirst(RegExp(regex), '');

        request = await httpClient.getUrl(
          Uri.parse(
            '${ApiConfig.ifpApiPath}/common/download?filename=$filepath',
          ),
        );
      } else {
        request = await httpClient.getUrl(Uri.parse(url));
      }
      request.headers
        ..add('Content-type', 'application/json')
        ..add('Accept-Language', HiveUtilsSettings.appLanguage);

      headers.entries.toList().map((e) => request.headers.add(e.key, e.value));

      AppLog.info('File url:${request.uri}');
      final HttpClientResponse response = await request.close();

      if (kDebugMode) {
        try {
          log('\n=======${'DOWNLOAD'}-${request.method.toUpperCase()}=========BEGIN========================\n'
              '[URL] :${request.uri}\n'
              '[HEADERS] :\n${request.headers}'
              '[RESPONSE_CODE] :${response.statusCode}\n'
              '=======${'DOWNLOAD'}-${request.method.toUpperCase()}==========END=========================\n');
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
      }

      final totalBytes = response.contentLength;
      var downloadedBytes = 0;
      final File file = File(filePath);

      response.listen(
            (data) {
          file.writeAsBytesSync(data, mode: FileMode.append);
          downloadedBytes += data.length;

          // AppLog.info('Downloading: $downloadedBytes/$totalBytes');
          AppLog.info(
            'Downloading: ${(downloadedBytes / 1000000).toStringAsFixed(2)} MB/$totalBytes',
          );

          progress?.value = DownloadProgress(downloaded: downloadedBytes, total: totalBytes);
        },
        onDone: () async {
          progress?.value = null;
          httpClient.close();
          AppLog.info('Download completed: $filePath');

          onComplete?.call(file.path);
        },
        onError: (dynamic error) {
          AppLog.info('Error: $error');
          httpClient.close();
        },
        cancelOnError: true,
      );

      if (response.statusCode == 200) {
        // final bytes = await consolidateHttpClientResponseBytes(response);
        // final File file = File(filePath);
        // await file.writeAsBytes(bytes);
        AppLog.info('File downloaded: $filePath');
        return file;
      } else {
        // final stringData = await response.transform(utf8.decoder).join();
        // print(stringData);
        AppLog.warning('Download failed: ${response.statusCode}\n$response');
      }

      if (await file.length() != totalBytes) {
        await file.delete();
      }

      return null;
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return null;
    }
  }

  static Future<String> getDownloadsDir() async {
    return FileUtils.appDownloadsDirectoryPath;
  }

  // static String getFileName(String url) {
  //   return Uri.decodeQueryComponent(url.substring(url.lastIndexOf('/') + 1)).replaceAll(' ', '');
  //   // return url.substring(url.lastIndexOf('/') + 1);
  // }

  static Future<OpenResult> openFile(String? filePath) async {
    if (filePath == null) {
      return OpenResult(type: ResultType.error, message: 'File path is null');
    }
    final Map<String, String> typeMap = Platform.isAndroid
        ? typeMapAndroid
        : Platform.isIOS
        ? typeMapIos
        : {};

    final String fileExtension = filePath.substring(0, filePath.lastIndexOf('/') + 1);

    final OpenResult r = await OpenFilex.open(filePath, type: typeMap[fileExtension]);
    if (r.type != ResultType.done) {
      String msg = LocaleKeys.somethingWentWrong.tr();
      if (r.type == ResultType.noAppToOpen) {
        msg = LocaleKeys.noCompatibleAppFound.tr();
      } else if (r.type == ResultType.permissionDenied) {
        msg = LocaleKeys.permissionDenied.tr();
      } else if (r.type == ResultType.fileNotFound) {
        msg = LocaleKeys.fileNotFound.tr();
      }
      AppMessage.showOverlayNotificationError(message: msg);
    }
    return r;
  }

  static String? mimeType(String file) {
    final String ext = file.substring(file.lastIndexOf('.'));

    final int i1 = typeMapAndroid.keys.toList().indexWhere((element) => element == ext);
    final int i2 = typeMapIos.keys.toList().indexWhere((element) => element == ext);

    if (i1 >= 0) {
      return typeMapAndroid.values.toList()[i1];
    } else if (i2 >= 0) {
      return typeMapIos.values.toList()[i2];
    } else {
      return null;
    }
  }

  static Map<String, String> typeMapIos = {
    '.rtf': 'public.rtf',
    '.txt': 'public.plain-text',
    '.html': 'public.html',
    '.htm': 'public.html',
    '.xml': 'public.xml',
    '.tar': 'public.tar-archive',
    '.gz': 'org.gnu.gnu-zip-archive',
    '.gzip': 'org.gnu.gnu-zip-archive',
    '.tgz': 'org.gnu.gnu-zip-tar-archive',
    '.jpg': 'public.jpeg',
    '.jpeg': 'public.jpeg',
    '.png': 'public.png',
    '.avi': 'public.avi',
    '.mpg': 'public.mpeg',
    '.mpeg': 'public.mpeg',
    '.mp4': 'public.mpeg-4',
    '.3gpp': 'public.3gpp',
    '.3gp': 'public.3gpp',
    '.mp3': 'public.mp3',
    '.zip': 'com.pkware.zip-archive',
    '.gif': 'com.compuserve.gif',
    '.bmp': 'com.microsoft.bmp',
    '.ico': 'com.microsoft.ico',
    '.doc': 'com.microsoft.word.doc',
    '.xls': 'com.microsoft.excel.xls',
    '.ppt': 'com.microsoft.powerpoint.ppt',
    '.wav': 'com.microsoft.waveform-audio',
    '.wm': 'com.microsoft.windows-media-wm',
    '.wmv': 'com.microsoft.windows-media-wmv',
    '.pdf': 'com.adobe.pdf',
    '': '*/*',
  };
  static Map<String, String> typeMapAndroid = {
    '.3gp': 'video/3gpp',
    '.torrent': 'application/x-bittorrent',
    '.kml': 'application/vnd.google-earth.kml+xml',
    '.gpx': 'application/gpx+xml',
    '.csv': 'application/vnd.ms-excel',
    '.apk': 'application/vnd.android.package-archive',
    '.asf': 'video/x-ms-asf',
    '.avi': 'video/x-msvideo',
    '.bin': 'application/octet-stream',
    '.bmp': 'image/bmp',
    '.c': 'text/plain',
    '.class': 'application/octet-stream',
    '.conf': 'text/plain',
    '.cpp': 'text/plain',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.exe': 'application/octet-stream',
    '.gif': 'image/gif',
    '.gtar': 'application/x-gtar',
    '.gz': 'application/x-gzip',
    '.h': 'text/plain',
    '.htm': 'text/html',
    '.html': 'text/html',
    '.jar': 'application/java-archive',
    '.java': 'text/plain',
    '.jpeg': 'image/jpeg',
    '.jpg': 'image/jpeg',
    '.js': 'application/x-javascript',
    '.log': 'text/plain',
    '.m3u': 'audio/x-mpegurl',
    '.m4a': 'audio/mp4a-latm',
    '.m4b': 'audio/mp4a-latm',
    '.m4p': 'audio/mp4a-latm',
    '.m4u': 'video/vnd.mpegurl',
    '.m4v': 'video/x-m4v',
    '.mov': 'video/quicktime',
    '.mp2': 'audio/x-mpeg',
    '.mp3': 'audio/x-mpeg',
    '.mp4': 'video/mp4',
    '.mpc': 'application/vnd.mpohun.certificate',
    '.mpe': 'video/mpeg',
    '.mpeg': 'video/mpeg',
    '.mpg': 'video/mpeg',
    '.mpg4': 'video/mp4',
    '.mpga': 'audio/mpeg',
    '.msg': 'application/vnd.ms-outlook',
    '.ogg': 'audio/ogg',
    '.pdf': 'application/pdf',
    '.png': 'image/png',
    '.pps': 'application/vnd.ms-powerpoint',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.prop': 'text/plain',
    '.rc': 'text/plain',
    '.rmvb': 'audio/x-pn-realaudio',
    '.rtf': 'application/rtf',
    '.sh': 'text/plain',
    '.tar': 'application/x-tar',
    '.tgz': 'application/x-compressed',
    '.txt': 'text/plain',
    '.wav': 'audio/x-wav',
    '.wma': 'audio/x-ms-wma',
    '.wmv': 'audio/x-ms-wmv',
    '.wps': 'application/vnd.ms-works',
    '.xml': 'text/plain',
    '.z': 'application/x-compress',
    '.zip': 'application/x-zip-compressed',
    '': '*/*',
  };

  static (String, String) _renameFileIfExists(
      String dirPath,
      String downloadFileName,
      ) {
    if (File('$dirPath/$downloadFileName').existsSync()) {
      final List<String> list = downloadFileName.split('.');

      final String newFileName =
          '${list.take(list.length - 1).join('.')} (${DateTime.now().millisecondsSinceEpoch}).${list.last}';
      return (dirPath, newFileName);
    } else {
      return (dirPath, downloadFileName);
    }
  }

// static String getfileHash(File file) {
//   //  final appleInBytes = utf8.encode(file.readAsBytesSync());
//   final value = sha256.convert(file.readAsBytesSync());
//   return value.toString();
// }
}

class DownloadProgress {
  DownloadProgress({required this.downloaded, required this.total});

  final int downloaded;
  final int total;

  double get progressValue => downloaded / total;
}
