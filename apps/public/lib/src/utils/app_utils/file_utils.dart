import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'app_message.dart';
import 'downloadHelper.dart';
import '../../../translations/locale_keys.g.dart';

class FileUtils {
  static late Directory _appDownloadsRootDirectory;
  static late Directory _appCacheRootDirectory;
  static late Directory _appSupportRootDirectory;

  static Future<void> init() async {
    if (Platform.isIOS) {
      _appDownloadsRootDirectory = await getApplicationDocumentsDirectory();
    } else if (Platform.isAndroid) {
      final Directory? dir = await getExternalStorageDirectory();
      _appDownloadsRootDirectory = Directory('/storage/emulated/0');

      if (!_appDownloadsRootDirectory.existsSync() && dir != null && dir.existsSync()) {
        _appDownloadsRootDirectory = dir;
      }
    } else {
      _appDownloadsRootDirectory = await getApplicationSupportDirectory();
    }

    _appCacheRootDirectory = await getApplicationCacheDirectory();
    _appSupportRootDirectory = await getApplicationSupportDirectory();
  }

  static String get appSupportDirectory => _appSupportRootDirectory.path;

  static String get appCacheDirectoryPath => _appCacheRootDirectory.path;

  static String get appDownloadsDirectoryPath {
    String dirPath = '/';
    if (Platform.isIOS) {
      dirPath = '${_appDownloadsRootDirectory.path}/Download';
    } else if (Platform.isAndroid) {
      dirPath = '${_appDownloadsRootDirectory.path}/Download/BayaanApp';
    }
    return dirPath;
  }

  static String get dirPathIndicator => '$appDownloadsDirectoryPath/Indicator';

  static String get dirPathMetadata => '$appDownloadsDirectoryPath/Metadata';

  static String joinPathParts(List<String> parts) {
    return path.joinAll(parts);
  }

  static Future<String> getNewFilePath(String filePath) async {
    int counter = 0;

    final String directoryPath = path.dirname(filePath);
    final String fileName =
    path.basenameWithoutExtension(filePath).replaceAll(RegExp(r'[^\p{L}\p{N}\s._]', unicode: true), '_');
    final String extension = path.extension(filePath).replaceFirst('.', '');

    String newFileName = '$fileName.$extension';
    File file = File(path.join(directoryPath, newFileName));

    while (file.existsSync()) {
      counter++;
      newFileName = '$fileName ($counter).$extension';
      file = File(path.join(directoryPath, newFileName));
    }

    return path.join(directoryPath, newFileName);
  }

  static Future<bool> saveFileAndNotify({required List<int> bytes, required String filePath}) async {
    try {
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        await Permission.storage.request();
      }
      await Directory(path.dirname(filePath)).create(recursive: true);

      final String newFilePath = await getNewFilePath(filePath);

      final File file = File(newFilePath);
      await file.writeAsBytes(bytes);

      AppMessage.showOverlayNotificationSuccess(
        title: LocaleKeys.fileDownloaded.tr(),
        message: path.basename(newFilePath),
        onTap: () {
          DownloadHelper.openFile(file.path);
        },
      );
      return true;
    } catch (_) {
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
      return false;
    }
  }

  static String getFileName({String? filePath, String? url}) {
    if ((filePath ?? '').isNotEmpty) {
      return path.basename(filePath!);
    } else if ((url ?? '').isNotEmpty) {
      return Uri.decodeQueryComponent(url!.substring(url.lastIndexOf('/') + 1)).replaceAll(' ', '');
    } else {
      return '';
    }
  }
}
