import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../../route_manager/route_imports.gr.dart';
import '../../features/home/<USER>/models/update_response_model.dart';
import '../../features/settings/presentation/bloc/setting_bloc.dart';
import 'app_message.dart';
import '../constants/asset_constants/image_constants.dart';
import '../constants/color_constants/color_constants.dart';
import '../hive_utils/hive_utils_settings.dart';
import '../styles/app_text_styles.dart';
import '../../../translations/locale_keys.g.dart';

class AppUpdate {
  static bool _isUpdateChecking = false;

  static void check(BuildContext context) {
    _isUpdateChecking = true;
    context.read<SettingBloc>().add(CheckForApkUpdateEvent());
  }

  static Widget listenerWidget() {
    return BlocListener<SettingBloc, SettingState>(
      listenWhen: (_, state) => state is UpdateCheckSuccessState,
      listener: (context, state) {
        if (!_isUpdateChecking) return;
        _isUpdateChecking = false;

        final currentRoute = context.router.stack.last.name;
        if (![SettingsScreenRoute.name, AppUpdateScreenRoute.name].contains(currentRoute)) {
          if (state is! UpdateCheckSuccessState) return;

          if (!state.hasUpdate) return;
          if (HiveUtilsSettings.getUserGuideStatus() != UserGuides.None) return;

          _appUpdateDialog(context: context, appUpdateResponse: state.data);
        }
      },
      child: const SizedBox.shrink(),
    );
  }

  static void _appUpdateDialog({required BuildContext context, required UpdateResponse appUpdateResponse}) {
    final bool isForceUpdate = appUpdateResponse.isForceUpdate ?? false;
    showDialog<void>(
      barrierDismissible: !isForceUpdate,
      context: context,
      builder: (context) {
        return PopScope(
          canPop: !isForceUpdate,
          child: AlertDialog(
            backgroundColor: AppColors.white,
            contentPadding: const EdgeInsets.all(20),
            insetPadding: const EdgeInsets.all(20),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isForceUpdate)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: const Icon(
                          Icons.close_rounded,
                          color: AppColors.black,
                          size: 18,
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  )
                else
                  const SizedBox(height: 10),
                SvgPicture.asset(AppImages.updateDialogeImage),
                const SizedBox(height: 10),
                Text(
                  LocaleKeys.newUpdateAvailable.tr(),
                  style: AppTextStyles.s20w5cBlackOrWhiteShade.copyWith(color: AppColors.blackShade1),
                ),
                const SizedBox(height: 10),
                Text(
                  LocaleKeys.updateMessage.tr(),
                  style: AppTextStyles.s14w4cGrey,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(AppColors.blueLightOld),
                  ),
                  onPressed: () async {
                    try {
                      unawaited(
                        context.pushRoute(
                          AppUpdateScreenRoute(
                            appUpdateResponse: appUpdateResponse,
                            isFromSettings: false,
                          ),
                        ),
                      );
                    } catch (e) {
                      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      LocaleKeys.updateNow.tr(),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
