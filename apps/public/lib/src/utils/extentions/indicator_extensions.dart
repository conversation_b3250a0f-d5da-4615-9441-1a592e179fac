import '../../common/functions/indicator_date_setting.dart';
import '../../common/types.dart';
import '../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../features/details_page/base/constants.dart';
import '../../features/details_page/base/mixin/indicator_filter_adapter_mixin.dart';

extension IndicatorExtensions on IndicatorDetailsResponse {
  String get title => componentTitle ?? '';

  List<VisualizationsMeta> getFilteredVisualizationMetaList() {
    List<VisualizationsMeta> vizMetaList = [];
    if (type == 'insights-discovery') {
      final defaultVisualization = defaultVisualisation ?? '';
      final visualization = visualizations?.singleWhere(
        (viz) => viz.id == defaultVisualization,
      );
      vizMetaList = visualization?.indicatorVisualizations?.visualizationsMeta ?? [];
    } else {
      vizMetaList = indicatorVisualizations?.visualizationsMeta ?? [];
    }
    return vizMetaList;
  }

  List<JSONObject> _filterSeries(SeriesData series, dynamic filterPanel) {
    final dataList = List.generate(series.length, (index) => series[index])
      ..removeWhere((element) => element['VALUE'] == 'null');

    bool hasFilter = false;
    try {
      hasFilter = bool.parse('${filterPanel ?? 'false'}');
    } catch (e) {
      //  ignore: bool parse error
      hasFilter = true;
    }

    if (hasFilter && filterPanel is FilterPanel) {
      filterPanel.properties = IndicatorDateSetting.removeDuplicates(filterPanel.properties ?? []);

      final filterMap = SelectedFilterMap.fromEntries(
        filterPanel.properties?.map(
              (e) => MapEntry(e.path ?? '', [e.defaultVal ?? '']),
            ) ??
            [],
      );

      for (int i = 0; i < (filterPanel.properties! as List).length; i++) {
        final property = filterPanel.properties![i];

        final String filterKey = '${filterPanel.properties![i].path}';
        final String filterValue = '${filterPanel.properties![i].defaultVal}';

        final isObsDt = filterKey == kObsDt;
        final isStaticFilter = property.staticFilter ?? false;
        final isFilterDependenciesSatisfied = property.isAllDependencySatisfied(filterMap);
        if (isObsDt || isStaticFilter || !isFilterDependenciesSatisfied) continue;

        dataList.removeWhere((element) {
          return element[filterKey].toString().toLowerCase() != filterValue.toLowerCase();
        });
      }
    }

    return dataList;
  }

  SeriesDataList getFilteredSeriesForMultiDrivers([int vizMetaIndex = 0]) {
    List<SeriesMeta> seriesMetaList = [];
    String dbColumn = '';
    FilterPanel? filter;

    if (type == 'insights-discovery') {
      final defaultVisualization = defaultVisualisation ?? '';

      final visualizationsMeta = visualizations!
          .singleWhere((element) => element.id == defaultVisualization)
          .indicatorVisualizations
          ?.visualizationsMeta
          ?.firstOrNull;

      seriesMetaList = visualizationsMeta?.seriesMeta ?? [];
      filter = visualizations!.singleWhere((element) => element.id == defaultVisualization).filterPanel;
      dbColumn = visualizationsMeta?.dbColumn ?? '';
    } else {
      if ((indicatorVisualizations?.visualizationsMeta ?? []).isEmpty) {
        seriesMetaList = [];
      } else {
        seriesMetaList = List.generate(
          (indicatorVisualizations?.visualizationsMeta?[vizMetaIndex].seriesMeta ?? []).length,
          (index) => indicatorVisualizations!.visualizationsMeta![vizMetaIndex].seriesMeta![index],
        );
      }
    }

    final seriesDataList = <SeriesData>[];
    for (final SeriesMeta seriesMeta in seriesMetaList) {
      final String dbColumnValue = seriesMeta.dbIndicatorId ?? '';

      final seriesData = List.generate(seriesMeta.data?.length ?? 0, (index) => seriesMeta.data![index]);

      if (dbColumn.isNotEmpty) {
        seriesData.removeWhere((e) => e[dbColumn] != dbColumnValue);
      }

      final tempList = _filterSeries(seriesData, filterPanel ?? filter);
      seriesDataList.add(tempList);
    }

    return seriesDataList;
  }

  List<JSONObject> getFilteredSeries({
    int seriesMetaIndex = 0,
    String? filterVisualization,
  }) {
    List<JSONObject> filteredList = [];
    if (type == 'insights-discovery') {
      final String defaultVisualization = filterVisualization ?? defaultVisualisation ?? '';
      final List<JSONObject> list = visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .indicatorVisualizations
              ?.visualizationsMeta
              ?.firstOrNull
              ?.seriesMeta?[seriesMetaIndex]
              .data ??
          [];

      final filterPanel = visualizations!
          .singleWhere(
            (element) => element.id == defaultVisualization,
          )
          .filterPanel;

      filteredList = _filterSeries(
        list,
        filterPanel,
      );
    } else {
      final seriesData = indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta![seriesMetaIndex].data;
      final List<JSONObject> list = List.generate(
        seriesData?.length ?? 0,
        (index) => seriesData![index],
      );

      filteredList = _filterSeries(list, filterPanel);
    }

    return filteredList;
  }
}
