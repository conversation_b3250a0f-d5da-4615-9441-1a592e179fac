import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'hive_keys.dart';

enum UserGuides { None, Home, Domains, MyDashboards, Products, AskUs }

class HiveUtilsSettings {
  HiveUtilsSettings._();

  static Box<dynamic> get box => Hive.box<dynamic>(HiveKeys.boxSettings);

  static String boxStatus() {
    return getThemeMode.toString() + textSizeFactor.toString() + appLanguage;
  }

  static bool get isLightMode => getThemeMode == ThemeMode.light;

  static ThemeMode get getThemeMode {
    final String val = box.get(HiveKeys.keyTheme, defaultValue: ThemeMode.system.name).toString();

    if (val == ThemeMode.system.name) {
      final brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
      if (brightness == Brightness.dark) {
        setThemeMode(ThemeMode.dark);
        return ThemeMode.dark;
      } else {
        setThemeMode(ThemeMode.light);
        return ThemeMode.light;
      }
    }

    return ThemeMode.values.firstWhere(
          (d) => d.name == val.toLowerCase(),
    );
  }

  static Future<void> setThemeMode(ThemeMode value) async => box.put(
    HiveKeys.keyTheme,
    value.name,
  );

  static ValueListenable<Box<dynamic>> get textSizeFactorListenable => box.listenable(
    keys: [HiveKeys.keyTextSizeFactor],
  );

  static double get textSizeFactor {
    final value = box.get(HiveKeys.keyTextSizeFactor, defaultValue: 1.0).toString();
    return double.tryParse(value) ?? 1.0;
  }

  static Future<void> setTextSizeFactor(double value) => box.put(
    HiveKeys.keyTextSizeFactor,
    value,
  );

  static bool get isLanguageArabic => appLanguage == 'ar';

  static bool get isLanguageEnglish => appLanguage == 'en';

  static String get appLanguage => (box.get(HiveKeys.keyLocale, defaultValue: 'en') ?? 'en') as String;

  static Future<void> setAppLanguage(String langCode) => box.put(HiveKeys.keyLocale, langCode);

  static Future<void> saveUserGuideStatus({UserGuides value = UserGuides.None}) =>
      box.put(HiveKeys.showUserGuide, value.name);

  static UserGuides getUserGuideStatus() {
    final selectedType =
        box.get(HiveKeys.showUserGuide, defaultValue: UserGuides.None.name) ??
            UserGuides.None.name;
    return UserGuides.values
        .firstWhere((element) => element.name == selectedType);
  }

  static Future<void> setSearchSuggestions({List<String> suggestions = const []}) =>
      box.put(HiveKeys.searchSuggestions, suggestions);

  static List<String> getSearchSuggestions() =>
      (box.get(HiveKeys.searchSuggestions, defaultValue: <String>[]) ?? <String>[]) as List<String>;

// static Future<void> saveTermsAndConditionStatus({bool value = true}) async {
//   final box = Hive.box<bool>(HiveKeys.isTermsAndConditionsAccepted);
//
//   await box.put(
//     HiveKeys.isTermsAndConditionsAccepted,
//     value,
//   );
// }
//
// static bool getTermsAndConditionStatus() {
//   final box = Hive.box<bool>(HiveKeys.isTermsAndConditionsAccepted);
//
//   return box.get(
//         HiveKeys.isTermsAndConditionsAccepted,
//         defaultValue: false,
//       ) ??
//       false;
// }

// converting string to language
// Lang toLang() {
//   return Lang.values.firstWhere((d) => describeEnum(d) == toLowerCase());
// }
}
