import 'dart:async';
import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../../common/models/response_models/api_response.dart';
import '../../../common/models/response_models/repo_response.dart';
import '../../../common/types.dart';
import '../../../features/domains/data/models/domain_model/domain_model.dart';
import '../hive_keys.dart';
import '../hive_utils_persistent.dart';
import '../hive_utils_settings.dart';
import '../../../../translations/locale_keys.g.dart';

part 'box.dart';
part 'cache.dart';
part 'cacheable_repository.dart';
part 'mixin.dart';
