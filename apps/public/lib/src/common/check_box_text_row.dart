import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../utils/constants/asset_constants/image_constants.dart';
import '../utils/constants/color_constants/color_constants.dart';
import '../utils/extentions/string_extentions.dart';
import '../utils/hive_utils/hive_utils_settings.dart';

class CheckBoxTextRow extends StatelessWidget {
  const CheckBoxTextRow({
    required this.title,
    required this.isSelected,
    required this.onChanged,
    this.titleColor,
    this.isRadioButton = false,
    super.key,
    this.isDefaultValue = false,
    this.isDisable = false,
    this.padding = EdgeInsets.zero,
  });

  final EdgeInsets padding;
  final String title;
  final Color? titleColor;
  final bool isSelected;
  final Function onChanged;
  final bool isRadioButton;
  final bool isDefaultValue;
  final bool isDisable;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;
    return Opacity(
      opacity: isDisable ? 0.5 : 1,
      child: AbsorbPointer(
        absorbing: isDefaultValue || isDisable ? true : false,
        child: InkWell(
          onTap: () => onChanged(),
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Padding(
            padding: padding,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isRadioButton) ...[
                  SvgPicture.asset(
                    isDefaultValue
                        ? AppImages.icCheckBoxTickGrey
                        : isSelected
                            ? isLightMode
                                ? AppImages.icCheckBoxTick
                                : AppImages.icCheckBoxTickDark
                            : AppImages.icCheckBoxUnTick,
                    width: 19,
                    height: 19,
                  ),
                ] else ...[
                  SvgPicture.asset(
                    isSelected
                        ? isLightMode
                            ? AppImages.icRadioSelect
                            : AppImages.icRadioSelectDark
                        : AppImages.icRadioUnSelect,
                    width: 19,
                    height: 19,
                  ),
                ],
                const SizedBox(width: 10),
                Flexible(
                  child: Text(
                    title.capitalize(),
                    style: TextStyle(
                      color: titleColor ?? (isLightMode ? AppColors.blackTextTile : AppColors.white),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      height: 1.2,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
