import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:overlay_support/overlay_support.dart';

import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/api_cache/api_cache.dart';
import '../../../utils/hive_utils/hive_utils_persistent.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../types.dart';

part 'debug_bottom_panel.dart';
part 'debug_config.dart';

class DebugOverlayButton extends StatefulWidget {
  const DebugOverlayButton({super.key});

  @override
  State<DebugOverlayButton> createState() => _DebugOverlayButtonState();
}

class _DebugOverlayButtonState extends State<DebugOverlayButton> {
  Offset position = const Offset(0, 100);
  late final _screenSize = MediaQuery.of(context).size;

  OverlaySupportEntry? entry;

  final _isPanelOpen = ValueNotifier(false);

  bool _isDismissed = false;

  final _config = DebugConfig._();

  void _onLongPress() {
    setState(() {
      _isDismissed = true;
      entry?.dismiss();
      entry = null;
    });
  }

  void _onPressed() {
    if (entry != null) {
      entry?.dismiss();
      entry = null;
      _isPanelOpen.value = false;
      return;
    }

    entry = showOverlay(
      (context, progress) => _DebugBottomPanel(_config),
      duration: const Duration(minutes: 30),
    );
    _isPanelOpen.value = true;
  }

  @override
  Widget build(BuildContext context) {
    if (_isDismissed) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        onPanUpdate: _handleDrag,
        onPanEnd: _handleDragEnd,
        onLongPress: _onLongPress,
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.7),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: IconButton(
            onPressed: _onPressed,
            icon: ValueListenableBuilder(
              valueListenable: _isPanelOpen,
              builder: (context, isOpen, child) {
                return Icon(
                  isOpen ? Icons.close : Icons.bug_report,
                  color: Colors.white,
                  size: 16,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handleDrag(DragUpdateDetails details) {
    setState(() {
      position = Offset(
        position.dx + details.delta.dx,
        position.dy + details.delta.dy,
      );

      // Keep the button within screen bounds
      position = Offset(
        position.dx.clamp(0, _screenSize.width - 50),
        position.dy.clamp(0, _screenSize.height - 50),
      );
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    // Snap to nearest edge
    double newX = position.dx;

    if (position.dx < _screenSize.width / 2) {
      // Snap to left
      newX = 10;
    } else {
      // Snap to right
      newX = _screenSize.width - 60;
    }

    setState(() {
      position = Offset(newX, position.dy);
    });
  }
}
