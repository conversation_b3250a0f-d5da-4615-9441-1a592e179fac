import 'dart:convert';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../features/details_page/base/constants.dart';
import '../../../../../features/details_page/base/helper/route_helper.dart';
import '../../../../../features/details_page/base/mixin/indicator_filter_adapter_mixin.dart';
import '../../../../../features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import '../../../../../utils/app_utils/app_message.dart';
import '../../../../../utils/app_utils/device_type.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/extentions/color_extensions.dart';
import '../../../../../utils/extentions/indicator_extensions.dart';
import '../../../../../utils/extentions/list_extensions.dart';
import '../../../../../utils/extentions/string_extentions.dart';
import '../../../../../utils/hive_utils/hive_utils_persistent.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../functions/indicator_date_setting.dart';
import '../../../app_box_shadow.dart';
import '../../../app_shimmer.dart';
import '../../../card_custom_clipper.dart';
import '../../../chart_legend.dart';
import '../../../charts/spline_chart.dart';
import '../../../charts/treemap_chart.dart';
import '../../../domain_icon.dart';
import '../../../error_reload_placeholder.dart';
import '../../../indicator_chart_legend.dart';
import '../../../indicator_classification.dart';
import '../../../indicator_value_v2.dart';
import '../../../user_guide/showcaseview_package/intro_widget.dart';
import '../../../user_guide/showcaseview_package/showcaseview.dart';
import '../../data/data_sources/indicator_details_helper_v2.dart';
import '../../data/data_sources/indicator_details_response_helper.dart';
import '../../data/models/indicator_details_response.dart';
import '../../data/models/indicator_status_response.dart';
import '../bloc/indicator_card_bloc.dart';

class IndicatorCardV2 extends StatelessWidget {
  const IndicatorCardV2({
    required this.id,
    required this.contentType,
    this.overviewContentType,
    super.key,
    this.openButtonKey,
    this.usedForSearch = false,
    this.indicatorDetailsForSearch,
    this.comparedIndicatorName,
    this.onUserGuideBackFromDetailsPage,
    this.overView,
    this.onDetailsButtonTapped,
  });

  final String id;
  final String contentType;
  final String? overviewContentType;
  final GlobalKey? openButtonKey;
  final bool usedForSearch;
  final IndicatorDetailsResponseHelper? indicatorDetailsForSearch;
  final String? comparedIndicatorName;
  final void Function(bool isPreviousActionTriggered)? onUserGuideBackFromDetailsPage;
  final OverView? overView;
  final ValueChanged<IndicatorDetailsResponse>? onDetailsButtonTapped;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => IndicatorCardBloc()),
      ],
      child: _IndicatorCardWidget(
        key: Key('_IndicatorCardWidget.$key'),
        id: id,
        contentType: contentType,
        overviewContentType: overviewContentType ?? contentType,
        openButtonKey: openButtonKey,
        usedForSearch: usedForSearch,
        indicatorDetailsForSearch: indicatorDetailsForSearch,
        comparedIndicatorName: comparedIndicatorName,
        onUserGuideBackFromDetailsPage: onUserGuideBackFromDetailsPage,
        overView: overView,
        onDetailsButtonTapped: onDetailsButtonTapped,
      ),
    );
  }
}

class _IndicatorCardWidget extends StatefulWidget {
  const _IndicatorCardWidget({
    required this.id,
    required this.contentType,
    required this.overviewContentType,
    required this.usedForSearch,
    super.key,
    this.openButtonKey,
    this.indicatorDetailsForSearch,
    this.comparedIndicatorName,
    this.onUserGuideBackFromDetailsPage,
    this.overView,
    this.onDetailsButtonTapped,
  });

  final String id;
  final String contentType;
  final String overviewContentType;
  final GlobalKey? openButtonKey;
  final bool usedForSearch;
  final IndicatorDetailsResponseHelper? indicatorDetailsForSearch;
  final String? comparedIndicatorName;
  final void Function(bool isPreviousActionTriggered)? onUserGuideBackFromDetailsPage;
  final OverView? overView;
  final ValueChanged<IndicatorDetailsResponse>? onDetailsButtonTapped;

  @override
  State<_IndicatorCardWidget> createState() => __IndicatorCardWidgetState();
}

class __IndicatorCardWidgetState extends State<_IndicatorCardWidget> with AutomaticKeepAliveClientMixin {
  IndicatorDetailsResponse? indicatorDetails;

  ///use for getting all data for filteration
  IndicatorDetailsResponse? originalIndicatorData;

  NodeIdUuid? subscription;
  NodeIdUuid? myApps;
  bool isComparisonActive = false;
  bool isPreviousFromUserGuide = false;

  // String? comparedIndicatorName;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    getIndicatorDetails();
  }

  void getIndicatorDetails() {
    if (!mounted) return;

    if (!widget.usedForSearch) {
      if (widget.id.contains('_')) {
        isComparisonActive = true;

        context.read<CompareDetailsBloc>().add(
              GetCompareIndicatorDetailsEvent(
                indicatorId1: widget.id.split('_').first,
                indicatorId2: widget.id.split('_').last,
              ),
            );
      } else {
        context.read<IndicatorCardBloc>().add(
              GetIndicatorDetailsEvent(
                id: widget.id,
                contentType: widget.contentType,
                overviewContentType: widget.overviewContentType,
              ),
            );
      }
    } else {
      indicatorDetails = widget.indicatorDetailsForSearch?.indicatorDetails;
    }
  }

  String? get _yAxisLabel {
    if (indicatorDetails?.getIndicatorType() == IndicatorType.insightDiscovery) {
      String? label;

      final Visualizations? visualization =
          indicatorDetails?.visualizations?.where((e) => e.id == indicatorDetails?.defaultVisualisation).firstOrNull;

      if ((visualization?.filterPanel?.properties ?? []).isNotEmpty) {
        final VisualizationsMeta? visualizationsMeta = visualization?.indicatorVisualizations?.visualizationsMeta
            ?.where((e) => e.id == visualization.indicatorVisualizations?.visualizationDefault)
            .firstOrNull;

        Map<String, dynamic>? map;
        map = visualizationsMeta?.axisValues?['CATEGORY_NAME'] as Map<String, dynamic>?;
        if (map == null || map.isEmpty) {
          map = visualizationsMeta?.axisValues?['CATEGORY_NAME_AR'] as Map<String, dynamic>?;
        }

        if (map != null && map.isNotEmpty) {
          final String? defaultVal = visualization?.filterPanel?.properties
              ?.where((e) => ['CATEGORY_NAME', 'CATEGORY_NAME_AR'].contains(e.path))
              .firstOrNull
              ?.defaultVal;

          label = map[defaultVal ?? '']?.toString();
        } else {
          label = visualizationsMeta?.yAxisLabel;
        }
      }

      return label;
    } else {
      return indicatorDetails?.indicatorVisualizations?.visualizationsMeta
          ?.where((e) => e.id == indicatorDetails?.indicatorVisualizations?.visualizationDefault)
          .firstOrNull
          ?.yAxisLabel;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final bool isLightMode = HiveUtilsSettings.isLightMode;
    return Column(
      children: [
        BlocListener<CompareDetailsBloc, CompareDetailsState>(
          listenWhen: (_, state) =>
              state is GetCompareIndicatorDetailsSuccessState && widget.id == state.compareIndicatorId,
          listener: (context, state) {
            if (state is GetCompareIndicatorDetailsSuccessState) {
              indicatorDetails = state.indicatorDetails;
              setState(() {});
            }
          },
          child: const SizedBox(),
        ),
        BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
          // key: Key('bloc${widget.id}'),
          listener: (context, state) {
            if (state is IndicatorDetailsSuccessState) {
              if (!widget.usedForSearch) {
                indicatorDetails = state.indicatorDetails;
                originalIndicatorData = IndicatorDetailsResponse.fromJson(
                  jsonDecode(jsonEncode(state.indicatorDetails)) as Map<String, dynamic>,
                );
              }
            } else if (state is IndicatorDetailsErrorState) {
              if (widget.usedForSearch) {
                AppMessage.showOverlayNotificationError(message: state.error);
              }
            }
          },
          builder: (context, state) {
            final rtl = DeviceType.isDirectionRTL(context);
            final isOfficial = indicatorDetails?.contentClassificationKey == 'official_statistics';
            final isExperimental = indicatorDetails?.contentClassificationKey == 'experimental_statistics';

            return SizedBox(
              child: Stack(
                children: [
                  Positioned(
                    right: rtl ? null : 0,
                    left: rtl ? 0 : null,
                    top: 0,
                    height: 40,
                    width: 40,
                    child: widget.openButtonKey != null
                        ? IntroWidget(
                            stepKey: widget.openButtonKey ?? GlobalKey(debugLabel: 'null-openButtonKey'),
                            stepIndex: 2,
                            totalSteps: 7,
                            onNext: () {
                              if (indicatorDetails != null) {
                                DetailsPageRouteHelper.indicatorDetailsPageRoute(
                                  context,
                                  id: indicatorDetails?.id ?? '',
                                  contentType: widget.contentType,
                                  indicatorType: indicatorDetails!.getIndicatorType(),
                                  title: indicatorDetails?.componentTitle ?? '',
                                ).then((value) {
                                  isPreviousFromUserGuide = true;
                                  widget.onUserGuideBackFromDetailsPage!(
                                    true,
                                  );
                                });
                              }
                            },
                            title: LocaleKeys.expand.tr(),
                            description: LocaleKeys.expandDesc.tr(),
                            arrowAlignment: Alignment.bottomRight,
                            isDownArrow: true,
                            position: TooltipPosition.top,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            targetPadding: const EdgeInsets.all(6),
                            arrowPadding: const EdgeInsets.only(
                              top: 10,
                              right: 10,
                              left: 10,
                            ),
                            child: openDetailsButton(context, isLightMode),
                          )
                        : openDetailsButton(context, isLightMode),
                  ),
                  ClipPath(
                    clipper: CardCustomClipper(isRtl: rtl),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
                        boxShadow: isLightMode ? AppBox.shadow() : null,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                      child: Container(
                        constraints: BoxConstraints(
                          minHeight: widget.usedForSearch ? 90 : 150,
                        ),
                        child: state is IndicatorDetailsErrorState && !widget.usedForSearch
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 20,
                                ),
                                child: ErrorReloadPlaceholder(
                                  error: kDebugMode
                                      ? 'id:${widget.id} contentType:${widget.contentType}\n${state.error}'
                                      : state.error,
                                  onReload: getIndicatorDetails,
                                ),
                              )
                            : (state is IndicatorDetailsLoadingState || indicatorDetails == null) &&
                                    (!widget.usedForSearch)
                                ? _buildLoadingShimmer()
                                : indicatorDetails != null
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Container(
                                                  margin: const EdgeInsets.only(
                                                    top: 15,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      if (!isComparisonActive) ...[
                                                        DomainIcon(
                                                          domainIdOrName: widget.usedForSearch
                                                              ? indicatorDetails?.domain
                                                              : indicatorDetails?.domainId,
                                                          color: isLightMode ? AppColors.black : AppColors.white,
                                                        ),
                                                        const SizedBox(
                                                          width: 10,
                                                        ),
                                                      ],
                                                      Expanded(
                                                        child: Text(
                                                          !isComparisonActive
                                                              ? indicatorDetails?.domain ?? ''
                                                              : widget.comparedIndicatorName ??
                                                                  LocaleKeys.compareIndicatorsResult.tr(),
                                                          maxLines: 1,
                                                          overflow: TextOverflow.ellipsis,
                                                          style: TextStyle(
                                                            color: isLightMode ? AppColors.black : AppColors.white,
                                                            fontSize: 14,
                                                            fontWeight: FontWeight.w400,
                                                          ),
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                        width: 10,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 40),
                                            ],
                                          ),
                                          const SizedBox(height: 10),
                                          if (!isComparisonActive) ...[
                                            Padding(
                                              padding: EdgeInsets.only(
                                                right: rtl ? 0 : 46,
                                                left: rtl ? 46 : 0,
                                              ),
                                              child: Text.rich(
                                                TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: indicatorDetails?.componentTitle,
                                                      style: TextStyle(
                                                        color: isLightMode ? AppColors.black : AppColors.white,
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    if (isOfficial || isExperimental)
                                                      WidgetSpan(
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: rtl ? 0 : 8,
                                                            right: rtl ? 8 : 0,
                                                          ),
                                                          child: isOfficial
                                                              ? SvgPicture.asset(AppImages.icOfficialActive)
                                                              : isExperimental
                                                                  ? SvgPicture.asset(AppImages.icExperimentalActive)
                                                                  : const SizedBox.shrink(),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            if (kDebugMode) _buildIndicatorDebugData(),
                                            const SizedBox(height: 8),
                                          ],
                                          if (!widget.usedForSearch) ...[
                                            if (!isComparisonActive) ...[
                                              IntrinsicHeight(
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Expanded(
                                                      child: IndicatorValueV2(
                                                        isCardView: true,
                                                        indicatorDetails: indicatorDetails,
                                                        overView: widget.overView,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    IndicatorClassification(security: indicatorDetails?.security),
                                                  ],
                                                ),
                                              ),
                                            ],
                                            const SizedBox(height: 4),
                                            _childWidget(
                                              state,
                                              isLightMode,
                                            ),
                                          ],
                                        ],
                                      )
                                    : const SizedBox(
                                        width: double.infinity,
                                        height: 100,
                                      ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildIndicatorDebugData() {
    final stream = HiveUtilsPersistent.box.watch(key: 'enableIndicatorDebug');

    return StreamBuilder(
      stream: stream,
      builder: (context, snap) {
        final show = snap.data?.value as bool? ?? false;

        if (!show) {
          return const SizedBox.shrink();
        }

        const style = TextStyle(
          color: AppColors.amberShade2,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${indicatorDetails?.id ?? ' '}', style: style),
            Text('${indicatorDetails?.domain} > ${indicatorDetails?.theme} > ${indicatorDetails?.subtheme}',
                style: style),
            Text('Indicator ID: ${indicatorDetails?.indicatorId ?? ' '}', style: style),
          ],
        );
      },
    );
  }

  Widget _buildLoadingShimmer() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 15),
                child: Row(
                  children: [
                    AppShimmer(height: 26, width: 26, radius: 26),
                    SizedBox(width: 10),
                    Expanded(
                      child: AppShimmer(height: 26, radius: 4),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
            ),
            SizedBox(width: 40),
          ],
        ),
        const SizedBox(height: 10),
        const AppShimmer(height: 20, width: 250, radius: 4),
        const SizedBox(height: 5),
        const AppShimmer(height: 28, width: 170, radius: 4),
        const SizedBox(height: 10),
        DecoratedBox(
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            shadows: const [
              BoxShadow(
                color: Color(0x0F000000),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: AppShimmer(
            radius: 10,
            height: 110 * max(1, HiveUtilsSettings.textSizeFactor),
          ),
        ),
      ],
    );
  }

  Widget _childWidget(IndicatorCardState state, bool isLightMode) {
    final String chartType = indicatorDetails?.indicatorVisualizations?.visualizationsMeta != null
        ? (indicatorDetails?.getFilteredVisualizationMetaList() ?? []).isNotEmpty
            ? indicatorDetails?.getFilteredVisualizationMetaList().first.type ?? ''
            : ''
        : '';

    List<SeriesMeta> treeMapSeriesMeta = [];
    VisualizationsMeta? vizMeta;
    if (chartType == 'tree-map-with-change-chart') {
      treeMapSeriesMeta = indicatorDetails?.getFilteredVisualizationMetaList().firstOrNull?.seriesMeta ?? [];
    }
    if (isComparisonActive) {
      vizMeta = indicatorDetails?.getFilteredVisualizationMetaList().first;
    }
    return IgnorePointer(
      child: Container(
        decoration: ShapeDecoration(
          color: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Column(
          children: [
            if (chartType == 'tree-map-with-change-chart')
              _treeMapChartRepresentation(treeMapSeriesMeta)
            else
              _lineChartRepresentation(isLightMode),
            if (isComparisonActive)
              _buildCompareLegends(vizMeta)
            else if (chartType != 'tree-map-with-change-chart')
              _buildLegends(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompareLegends(VisualizationsMeta? vizMeta) {
    final chartColorSet = HiveUtilsSettings.isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;
    final labels = vizMeta!.seriesTitles?.values ?? [];

    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 4, 12, 8),
      child: Wrap(
        spacing: 12,
        alignment: WrapAlignment.center,
        children: labels.indexed
            .map(
              (entry) => ChartLegend(
                label: entry.$2.toString(),
                color: chartColorSet.elementAt(entry.$1),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildLegends() {
    final type = indicatorDetails?.getIndicatorType();
    if (type != IndicatorType.forecast) {
      return IndicatorChartLegend(indicatorDetails: indicatorDetails);
    }

    final filterPanel = indicatorDetails?.filterPanel;
    final selectedFilterMap = IndicatorFilterAdapterMixin.getDefaultFilter(filterPanel as FilterPanel?);
    final legends = <String>[];
    if (selectedFilterMap.isEmpty) {
      final seriesMeta = indicatorDetails?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta ?? [];
      legends.addAll(
        seriesMeta.where((e) => e.type == 'solid').map(
              (e) => (e.label ?? '').toString(),
            ),
      );
    } else {
      final properties = filterPanel?.properties ?? [];

      final selectedFilterWithoutObsDt = selectedFilterMap.entries.where((e) {
        if (e.key == kObsDt) return false;
        final property = properties.singleWhere((filter) => filter.path == e.key);
        return property.type != 'radio';
      });

      final multiSelectFilter = selectedFilterWithoutObsDt.firstWhere(
        (e) => e.value.length > 1,
        orElse: () => selectedFilterWithoutObsDt.firstWhere(
          (e) {
            final property = properties.singleWhere((filter) => filter.path == e.key);
            return e.value.firstOrNull != property.defaultVal;
          },
          orElse: () => selectedFilterWithoutObsDt.first,
        ),
      );
      legends.addAll(multiSelectFilter.value);
    }

    final isLightMode = HiveUtilsSettings.isLightMode;
    final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      alignment: WrapAlignment.center,
      children: legends.indexed
          .map(
            (e) => [
              ChartLegend(
                label: e.$2,
                color: colorSet.elementAt(e.$1),
              ),
              ChartLegend(
                label: '${e.$2}-${LocaleKeys.forecast.tr()}',
                color: colorSet.elementAt(e.$1),
                isForecast: true,
              ),
            ],
          )
          .expand((e) => e)
          .toList(),
    );
  }

  Widget _lineChartRepresentation(bool isLightMode) {
    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];
    List<SeriesMeta> seriesMeta = [];
    List<Map<String, dynamic>> comparisonSeries = [];

    seriesMeta = indicatorDetails?.getFilteredVisualizationMetaList().elementAtOrNull(0)?.seriesMeta ?? [];

    if (indicatorDetails?.indicatorVisualizations != null) {
      final visualizationMeta = (indicatorDetails?.indicatorVisualizations?.visualizationsMeta ?? []).firstOrNull;
      if ((visualizationMeta?.seriesMeta ?? []).length > 1 && visualizationMeta?.id == 'compare-chart') {
        comparisonSeries = indicatorDetails!.getFilteredSeries(seriesMetaIndex: 1);
      }
    }

    final List<List<Map<String, dynamic>>> l = indicatorDetails?.getFilteredSeriesForMultiDrivers() ?? [];

    for (var i = 0; i < l.length; i++) {
      l[i].sort(
        (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
      );
    }

    for (int i = 0; i < l.length; i++) {
      if (seriesMeta[i].id!.contains('-forecast')) {
        foreCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList()
              .limitListLengthforCast(),
        );

        final List<SplineChartData> lowData = [];

        for (final e in l[i]) {
          if (e['VALUE_LL'] != null) {
            lowData.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_LL'] ?? '0'}'),
              ),
            );
          }
        }

        lowerAreaForecast.add(lowData);

        final List<SplineChartData> upData = [];

        for (final e in l[i]) {
          if (e['VALUE_UL'] != null) {
            upData.add(
              SplineChartData(
                e['OBS_DT'].toString(),
                num.parse('${e['VALUE'] ?? '0'}'),
                y: num.parse('${e['VALUE_UL'] ?? '0'}'),
              ),
            );
          }
        }

        upperAreaForecast.add(upData);

        areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      } else {
        nowCast.add(
          l[i]
              .map(
                (e) => SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                ),
              )
              .toList()
              .limitListLength(),
        );
      }
    }
    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
      l: l,
      indicatorDetails: IndicatorDetailsResponseHelper(indicatorDetails!),
    );

    return SizedBox(
      height: 120 * max(1, HiveUtilsSettings.textSizeFactor),
      child: SplineChart(
        key: ValueKey('${indicatorDetails?.id}-${indicatorDetails?.title}'),
        indicatorCard: true,
        frequency: indicatorDateSetting['selectedFrequencyForFilter'] as String,
        showForecast: foreCast.isNotEmpty,
        chartDataList: nowCast,
        forecastChartDataList: foreCast,
        areaHighlightChartData: areaForecast,
        isCompareActive: isComparisonActive,
        yAxisLabel: _yAxisLabel?.limitLength(maxLength: 12),
        comparisonChartData: isComparisonActive
            ? comparisonSeries
                .map(
                  (e) => SplineChartData(
                    e['OBS_DT'].toString(),
                    double.parse('${e['VALUE'] ?? '0'}'),
                  ),
                )
                .toList()
            : [],
      ),
    );
  }

  Widget _treeMapChartRepresentation(
    List<SeriesMeta> treeMapSeriesMeta,
  ) {
    final List<TreemapColorMapper> colorMappers = [];
    for (int i = 0; i < treeMapSeriesMeta.length; i++) {
      colorMappers.add(
        TreemapColorMapper.value(
          value: treeMapSeriesMeta[i].data?.first['CHANGE'].toString(),
          color: treeMapSeriesMeta[i].color.toString().toColor(),
        ),
      );
    }
    return FittedBox(
      child: Container(
        padding: const EdgeInsets.only(bottom: 6),
        height: 210 * max(1, HiveUtilsSettings.textSizeFactor),
        width: MediaQuery.sizeOf(context).width * 0.9,
        child: TreemapChart(
          key: Key(
            'treemap.indicator.v2.${treeMapSeriesMeta.map((e) => e.color).join()}',
          ),
          chartSeriesData: treeMapSeriesMeta,
        ),
      ),
    );
  }

  Widget openDetailsButton(BuildContext context, bool isLightMode) {
    return Container(
      decoration: ShapeDecoration(
        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
        shape: const OvalBorder(),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(100),
          onTap: () {
            if (widget.usedForSearch) {
              DetailsPageRouteHelper.indicatorDetailsPageRoute(
                context,
                id: indicatorDetails?.id ?? '',
                contentType: widget.contentType,
                indicatorType: indicatorDetails!.getIndicatorType(),
                title: indicatorDetails?.componentTitle ?? '',
              );
            } else {
              if (indicatorDetails != null) {
                if ((widget.comparedIndicatorName ?? '').isNotEmpty) {
                  DetailsPageRouteHelper.compareIndicatorDetailsPage(
                    context,
                    combinedId: indicatorDetails?.id ?? '',
                    indicatorType: indicatorDetails?.getIndicatorType(),
                  );
                } else {
                  widget.onDetailsButtonTapped != null
                      ? widget.onDetailsButtonTapped?.call(indicatorDetails!)
                      : DetailsPageRouteHelper.indicatorDetailsPageRoute(
                          context,
                          id: indicatorDetails?.id ?? '',
                          contentType: widget.contentType,
                          indicatorType: indicatorDetails!.getIndicatorType(),
                          title: indicatorDetails?.componentTitle ?? '',
                        );
                }
              }
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
              child: Icon(
                Icons.arrow_outward_rounded,
                color: isLightMode ? AppColors.blueShade30 : AppColors.blueLightOld,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
