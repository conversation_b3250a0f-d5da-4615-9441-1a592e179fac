import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../route_manager/route_imports.dart';
import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../../../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../../../models/response_models/repo_response.dart';
import '../../../lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';
import '../../data/models/indicator_details_response.dart';
import '../../domain/repositories/indicator_card_repository_imports.dart';

part 'indicator_card_event.dart';
part 'indicator_card_state.dart';

class IndicatorCardBloc extends Bloc<IndicatorCardEvent, IndicatorCardState> {
  IndicatorCardBloc() : super(const IndicatorDetailsInitState(id: '', contentType: '')) {
    on<GetIndicatorDetailsEvent>(getIndicatorDetailsEventHandler);
   }

  final _eventLogs = <IndicatorCardEvent>[];

  void _emitIndicatorEventStatus({
    required String status,
    required String id,
    required String contentType,
  }) {
    final context = servicelocator<AppRouter>().navigatorKey.currentContext!;
    context.read<HomeBloc>().add(
          IndicatorHomeCurrentEvent(
            status: status,
            id: id,
            contentType: contentType,
          ),
        );
    context.read<LazyIndicatorListViewBloc>().add(
          LazyIndicatorItemEvent(
            status: status,
            id: id,
            contentType: contentType,
          ),
        );
  }

  FutureOr<void> getIndicatorDetailsEventHandler(
    GetIndicatorDetailsEvent event,
    Emitter<IndicatorCardState> emit,
  ) async {
    if (_eventLogs.contains(event)) return;
    _eventLogs.add(event);

    try {
      _emitIndicatorEventStatus(
        status: 'loading',
        id: event.id,
        contentType: event.contentType,
      );

      emit(IndicatorDetailsLoadingState(id: event.id, contentType: event.contentType));

      final RepoResponse<IndicatorDetailsResponse> indicatorDetailsResponse =
          await servicelocator<IndicatorCardRepository>().indicatorDetails(
        id: event.id,
        contentType: event.contentType,
        payload: event.payload,
      );

      if (indicatorDetailsResponse.isSuccess) {
        final IndicatorDetailsResponse indicatorDetails = indicatorDetailsResponse.response!;
        _emitIndicatorEventStatus(
          status: 'success',
          id: event.id,
          contentType: event.contentType,
        );

        emit(
          IndicatorDetailsSuccessState(
            id: event.id,
            indicatorDetails: indicatorDetails,
            isFromDriverForSearch: event.isFromDriverForSearch,
            contentType: event.contentType,
          ),
        );
      } else {
        _emitIndicatorEventStatus(
          status: 'error',
          id: event.id,
          contentType: event.contentType,
        );

        emit(
          IndicatorDetailsErrorState(
            id: event.id, contentType: event.contentType,
            error: indicatorDetailsResponse.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        IndicatorDetailsErrorState(
          id: event.id, contentType: event.contentType,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    } finally {
      _eventLogs.remove(event);
    }
  }

}
