import 'dart:async';

import '../../../../../utils/app_utils/string_utils.dart';
import '../../../../functions/indicator_date_setting.dart';
import '../models/indicator_details_response.dart';

class IndicatorDetailsResponseHelper {
  IndicatorDetailsResponseHelper(this.indicatorDetails, {this.overView});

  IndicatorDetailsResponse indicatorDetails;
  final OverView? overView;

  String get chartType => indicatorDetails.indicatorVisualizations!.visualizationsMeta?.firstOrNull?.type ?? '';

  String get title => indicatorDetails.componentTitle ?? '';

  String get domainName => indicatorDetails.domain ?? '';

  String? get value =>  valUnit() ?? ''; // == null ? null: double.parse(valUnit().firstOrNull ?? '').toStringAsFixed(2) ;

  // String get numberUnit => valUnit().lastOrNull ?? '';

  String get unit => indicatorDetails.unit ?? '';

  bool get negativeArrow => num.parse(compareValue()) < 0;

  (String?, String?) get originalValue {
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization =
          indicatorDetails.defaultVisualisation ?? '';
      // todo check [0] or based on "id": "latest-date-value",
      try {
        List<OverviewValuesMeta> valList = indicatorDetails.visualizations!
                .singleWhere((element) => element.id == defaultVisualization)
                .indicatorValues
                ?.valuesMeta ??
            [];

        if (valList.isEmpty) {
          return (null, null);
        } else {
          return ('${valList.firstOrNull?.value}', '${valList.firstOrNull?.valueFormat}');
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        return (null, null);
      }
    } else {
      if (overView?.value != null) {
        return ('${overView?.value}', '${overView?.valueFormat}');
      } else {
        final List<OverviewValuesMeta> valList =
            indicatorDetails.indicatorValues?.overviewValuesMeta ?? [];
        return ('${valList.isEmpty ? null : valList.firstOrNull?.value}', '${valList.isEmpty ? null : valList.firstOrNull?.valueFormat}');
      }
    }
  }


  // static List<String?> shortNumber({required String? value, required String? angularPipeFormat}) {
  //   // final (String?, String?) orgValue = originalValue;
  //   // final String? value = orgValue.$1;
  //   // final String? format = orgValue.$2;
  //
  //   final num? val = num.tryParse('$value');
  //
  //   if (val == null) {
  //     return [null, ''];
  //   }
  //
  //   String? returnValue = val.toString();
  //   String returnValueUnit = '';
  //
  //   if (val > 1000000000000) {
  //     returnValue = (val / 1000000000000).toString();
  //     returnValueUnit = 'T';
  //   } else if (val > 1000000000) {
  //     returnValue = (val / 1000000000).toString();
  //     returnValueUnit = 'B';
  //   } else if (val > 1000000) {
  //     returnValue = (val / 1000000).toString();
  //     returnValueUnit = 'M';
  //   } else if (val > 1000) {
  //     returnValue = (val / 1000).toString();
  //     returnValueUnit = 'k';
  //   }
  //
  //   final List<String> l = (angularPipeFormat??'number-1.1-1').split('.').lastOrNull?.split('-') ?? [];
  //
  //   final int min = int.parse(l[0]);
  //   final int max = int.parse(l[1]);
  //
  //   final NumberFormat formatter =
  //   NumberFormat("#.${List.generate(max, (index) => '#').join()}");
  //   final String formattedValue = formatter.format(num.parse(returnValue));
  //   final double roundedValue = double.parse(formattedValue);
  //   returnValue = roundedValue.toString();
  //
  //   final String s = roundedValue.toString().split('.').lastOrNull ?? '';
  //   if (s.length < min) {
  //     returnValue += List.generate(min - s.length, (index) => '0').join();
  //   }
  //   return [returnValue, returnValueUnit];
  // }


  String? valUnit() {
    final (String?, String?) orgValue = originalValue;

    final String? value = orgValue.$1;
    final String? format = orgValue.$2;

    return ShortNumberPipe.transform(number: value??'', angularPipeFormat: format??'');
  }

  List<Map<String, dynamic>> getFilteredSeries({
    int seriesMetaIndex = 0,
    String? filterVisualization,
  }) {
    List<Map<String, dynamic>> filteredList = [];
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization = filterVisualization ??
          indicatorDetails.defaultVisualisation ?? '';
      final List<Map<String, dynamic>> list = indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .indicatorVisualizations
              ?.visualizationsMeta?.firstOrNull
              ?.seriesMeta?[seriesMetaIndex]
              .data ??
          [];
      filteredList = filterSeries(
          list,
          indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .filterPanel,);
    } else {
 

      final List<Map<String, dynamic>> list = List.generate(
        (indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta![seriesMetaIndex].data?.length ?? 0),
        (index) => (indicatorDetails.indicatorVisualizations!
            .visualizationsMeta?.firstOrNull?.seriesMeta![seriesMetaIndex].data![index] ?? {}),
      );

      filteredList = filterSeries(list, indicatorDetails.filterPanel);
      // filteredList = list;
    }
    // return filteredList.reversed.take(12).toList().reversed.toList();
    return filteredList;

    // @JsonKey(name: 'TREATMENT_METHOD_EN')
    // @JsonKey(name: 'VALUE')
    // @JsonKey(name: 'MAX_VALUE')
    // @JsonKey(name: 'MIN_VALUE')
    // @JsonKey(name: 'MAX_OBS_DT')
    // @JsonKey(name: 'MIN_OBS_DT')
    // @JsonKey(name: 'OBS_DT')
    // @JsonKey(name: 'YEAR')
  }

  List<List<Map<String, dynamic>>> getFilteredSeriesForMultiDrivers(
      {int visualizationsMetaIndex = 0,}) {
    List<SeriesMeta> list = [];
    List<Map<String, dynamic>> tempList = [];

  dynamic  filter ;
    final List<List<Map<String, dynamic>>> seriesList = [];
      String dbColumn = '';
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization =
          indicatorDetails.defaultVisualisation ?? '';

      final VisualizationsMeta? visualizationsMeta = indicatorDetails.visualizations!
          .singleWhere((element) => element.id == defaultVisualization)
          .indicatorVisualizations
          ?.visualizationsMeta?.firstOrNull; // todo change first to from list
      list = visualizationsMeta?.seriesMeta ?? [];

      filter = indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .filterPanel;

       dbColumn = visualizationsMeta?.dbColumn??'';

    } else {
      if((indicatorDetails.indicatorVisualizations?.visualizationsMeta??[]).isEmpty){
        list = [];
      }else {
        list = List.generate(
          (indicatorDetails.indicatorVisualizations
              ?.visualizationsMeta?[visualizationsMetaIndex].seriesMeta ?? [])
              .length,
              (index) =>
          indicatorDetails.indicatorVisualizations!
              .visualizationsMeta![visualizationsMetaIndex]
              .seriesMeta![index],);
      }
    }

    // for(int i=0;i<(indicatorDetails.indicatorVisualizations!
    //      .visualizationsMeta![visualizationsMetaIndex].seriesMeta ??
    //      []).length;i++){
    //    list.add(indicatorDetails.indicatorVisualizations!
    //        .visualizationsMeta![visualizationsMetaIndex].seriesMeta![i]);
    //  }

    // if ('Abu Dhabi Awarded Projects' == indicatorDetails.componentTitle) {
     // log(jsonEncode(filter.properties));
    // }

    for (final SeriesMeta series in list) {
      final String dbColumnValue = series.dbIndicatorId ?? '';

      final List<Map<String, dynamic>> list =
          List.generate(series.data?.length??0, (index) => series.data![index]);
//series.data ?? [];

      if(dbColumn.isNotEmpty) {
        list.removeWhere((e) => e[dbColumn] != dbColumnValue);
      }

      tempList = filterSeries(list, indicatorDetails.filterPanel ?? filter );
      seriesList.add(tempList);
      //filteredList.reversed.take(12).toList().reversed.toList()
    }

    return seriesList;
  }

  List<VisualizationsMeta> getFilteredVisualizationMetaList() {
    List<VisualizationsMeta> list = [];
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization =
          indicatorDetails.defaultVisualisation ?? '';
      list = indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .indicatorVisualizations
              ?.visualizationsMeta ??
          [];
    } else {
      list = indicatorDetails.indicatorVisualizations?.visualizationsMeta ?? [];
    }
    return list;
  }

  List<Map<String, dynamic>> filterSeries(
      List<Map<String, dynamic>> series, dynamic filterPanel,) {
    final List<Map<String, dynamic>> dataList =
        List.generate(series.length, (index) => series[index]);

    dataList
      ..removeWhere((element) => element['VALUE'] == 'null')
      ..map((e) => e).toList();
    bool hasFilter = false;
    try {
      hasFilter = bool.parse('${filterPanel ?? 'false'}');
    } catch (e, s) {
      hasFilter = true;
    }

    if (hasFilter) {
    filterPanel?.properties = IndicatorDateSetting.removeDuplicates(filterPanel?.properties as List<Properties>);
      for (int i = 0;
          i < (filterPanel!.properties! as List).length;
          i++) {
        final String filterKey =
            '${filterPanel!.properties![i].path}';
        final String filterValue =
            '${filterPanel!.properties![i].defaultVal }';
    if (filterKey != 'OBS_DT') {
       dataList.removeWhere((element) {
     
          return element[filterKey].toString().toLowerCase() !=
              filterValue.toLowerCase();
        });
    }


      }
    }
    return dataList;
  }

  // for displaying negative arrow
  String compareValue() {
    String compareValue = '0';
    if ((overView?.compareFilters ?? []).isNotEmpty) {
      switch (overView?.compareFilters!.firstOrNull) {
        case 'Y/Y':
          compareValue =
              '${overView?.yearlyCompareValue ?? 0}';
          break;
        case 'Q/Q':
          compareValue =
              '${overView?.quarterlyCompareValue ?? 0}';
          break;
        case 'M/M':
          compareValue =
              '${overView?.monthlyCompareValue ?? 0}';
          break;
      }
    }

    return compareValue;
  }
}
