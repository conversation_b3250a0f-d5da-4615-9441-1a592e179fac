import '../models/indicator_details_response.dart';

extension IndicatorDetailsHelper on IndicatorDetailsResponse {
  IndicatorType getIndicatorType() {
    if (contentClassificationKey == 'official_statistics') {
      return IndicatorType.official;
    } else if (contentClassificationKey == 'experimental_statistics') {
      return IndicatorType.experimental;
    } else if (type == 'coi') {
      return IndicatorType.forecast;
    } else if (type == 'insights-discovery') {
      return IndicatorType.insightDiscovery;
    } else if (type == 'Internal') {
      final chartType = indicatorVisualizations?.visualizationsMeta?.first.type ?? '';
      if (chartType == 'tree-map-with-change-chart') {
        return IndicatorType.forecast;
      }

      return IndicatorType.scenarioDriver;
    } else {
      return IndicatorType.other;
    }
  }

  IndicatorCategory getIndicatorCategory() {
    final IndicatorType type = getIndicatorType();
    if ([
          IndicatorType.insightDiscovery,
          IndicatorType.scenarioDriver,
          IndicatorType.forecast,
        ].contains(type) ||
        contentClassificationKey == 'analytical_apps') {
      return IndicatorCategory.analyticalApps;
    } else if (IndicatorType.official == type) {
      return IndicatorCategory.official;
    } else if (IndicatorType.experimental == type) {
      return IndicatorCategory.experimental;
    } else {
      return IndicatorCategory.other;
    }
  }
}

enum IndicatorCategory {
  official,
  experimental,
  analyticalApps,
  other,
}

enum IndicatorType {
  official,
  experimental,
  insightDiscovery,
  scenarioDriver, // type = Internal
  forecast,
  other,
}
