import 'dart:async';

import 'package:easy_localization/easy_localization.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../services/http_service_impl.dart';
import '../../../../../utils/hive_utils/hive_keys.dart';
import '../../../../models/response_models/repo_response.dart';
import '../../../../types.dart';
import '../../domain/repositories/indicator_card_repository_imports.dart';
import '../data_sources/indicator_card_api_end_points.dart';
import '../models/indicator_details_response.dart';
import '../models/indicator_status_response.dart';

class IndicatorCardImpl extends IndicatorCardRepository {
  final _httpService = HttpServiceRequests();

  Future<RepoResponse<IndicatorDetailsResponse>> _getAnalyticalAppIndicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload = const {},
  }) {
    final String endpoint = IndicatorCardApiEndPoints.indicatorDetailsAnalyticalApps(
      id: id,
      contentType: 'analytical-apps',
    );

    final body = {'indicatorDrivers': payload};
    final cacheKey = getCacheKey(endpoint, payload: body);

    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: body,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }

  Future<RepoResponse<IndicatorDetailsResponse>> _getNonAnalyticalAppIndicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload = const {},
  }) {
    String cType = contentType;
    const Map<String, String> map = {
      'scad_official_indicator': 'statistics-insights',
      'analytical-apps': 'analytical-apps',
      'analytical_apps': 'analytical_apps',
      'official-insights': 'official-insights',
      'innovative-insights': 'innovative-insights',
      'experimental_statistics': 'innovative-insights',
      'official_statistics': 'statistics-insights',
      'statistics-insights': 'statistics-insights',
      'coi': 'statistics-insights',
    };

    if (map.keys.contains(contentType)) {
      cType = map[contentType]!;
    }

    final String endpoint = cType == 'innovative-insights'
        ? IndicatorCardApiEndPoints.indicatorDetailsInnovativeInsights(
            contentType: cType,
            id: id,
          )
        : IndicatorCardApiEndPoints.indicatorDetails(
            contentType: cType,
            id: id,
          );

    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> indicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload = const {},
  }) async {
    final isAnalyticalApp = contentType == 'analytical-apps' || contentType == 'analytical_apps';
    try {
      if (isAnalyticalApp) {
        return _getAnalyticalAppIndicatorDetails(
          id: id,
          contentType: contentType,
          payload: payload,
        );
      } else {
        return _getNonAnalyticalAppIndicatorDetails(
          id: id,
          contentType: contentType,
          payload: payload,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<IndicatorStatusResponse>> getIndicatorStatus({
    required String id,
  }) async {
    final String endpoint = '${IndicatorCardApiEndPoints.getIndicatorStatus}$id/';
    final cacheKey = getCacheKey(HiveKeys.keyIndicatorStatus(id));
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
      ),
      parseResult: (json) => IndicatorStatusResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<Map<String, OverView>>> allIndicatorDetailsOverview({
    required List<String> ids,
    required String type,
  }) async {
    try {
      String cType = type;
      const Map<String, String> cTypeMap = {
        'scad_official_indicator': 'official_statistics',
      };
      if (cTypeMap.keys.contains(type)) {
        cType = cTypeMap[type]!;
      }

      final map = <String, OverView>{};

      final cacheKeyPrefix = 'allIndicatorDetailsOverview-$cType';
      final pendingIds = <String>[];
      for (final id in ids) {
        final cacheKey = getCacheKey('$cacheKeyPrefix-$id');
        final cache = getCache<JSONObject>(cacheKey);
        if (cache == null) {
          pendingIds.add(id);
          continue;
        }

        map[id] = OverView.fromJson(cache);
      }

      if (pendingIds.isEmpty) {
        return RepoResponse<Map<String, OverView>>.success(response: map);
      }

      final response = await _httpService.postJson(
        IndicatorCardApiEndPoints.indicatorOverview,
        server: ApiServer.ifp,
        jsonPayloadMap: {
          'ids': pendingIds,
          'type': cType,
        },
      );

      if (response.isSuccess) {
        for (final key in response.response.keys) {
          final json = response.response[key] as Map<String, dynamic>;
          final cacheKey = getCacheKey('$cacheKeyPrefix-$key');
          await setCache(key: cacheKey, value: json);
          map[key] = OverView.fromJson(json);
        }

        return RepoResponse<Map<String, OverView>>.success(response: map);
      } else {
        return RepoResponse<Map<String, OverView>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Map<String, OverView>>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
