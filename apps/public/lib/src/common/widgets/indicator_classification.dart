import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import 'indicator_card/data/models/indicator_details_response.dart';

class IndicatorClassification extends StatelessWidget {
  IndicatorClassification({
    this.security,
    this.classificationLevel,
    this.margin = EdgeInsets.zero,
    super.key,
  });

  final Security? security;
  final int? classificationLevel;
  final EdgeInsets margin;

  final _isLightMode = HiveUtilsSettings.isLightMode;

  final Map<String, dynamic> _classificationMap = {
    '0-en': 'Open',
    '0-ar': 'مفتوح',
    '0-tooltip-en':'Open and shareable with no restrictions',
    '0-tooltip-ar': 'متاحة ومصرح تشاركها دون قيود',
    '0-color': AppColors.green,

    '1-en': 'Confidential',
    '1-ar': 'خصوصي',
    '1-tooltip-en':'Accessible by government and semi-government entities and not shareable',
    '1-tooltip-ar':'متاحة للجهات الحكومية وشبه الحكومية دون مشاركتها',

    '2-en': 'Sensitive',
    '2-ar': 'حساس',
    '2-tooltip-en':'Accessible by authorized individuals for specific statistical purposes and not shareable',
    '2-tooltip-ar': 'متاحة للأشخاص المصرح لهم فقط لأغراض إحصائية دون مشاركتها',

    '3-en': 'Secret',
    '3-ar': 'سري',
  };

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
    if (security?.id != null) {
      String? classificationText = security?.name;

      if (HiveUtilsSettings.isLanguageArabic &&
          RegExp(r'^\d+\s*-\s*[a-z]+$')
              .hasMatch(classificationText?.toLowerCase() ?? '')) {
        classificationText = null;
      }

      return _classificationLevel(
        security?.id?.toString() ?? '',
        classificationText,
      );
    } else if ([0, 1, 2, 3].contains(classificationLevel)) {
      return _classificationLevel('$classificationLevel', null);
    } else {
      return const SizedBox();
    }
  }

  Widget _classificationLevel(String level, String? levelName) {
    final appLanguage = HiveUtilsSettings.appLanguage;
    final String classificationText = (levelName == null)
        ? _classificationMap['$level-$appLanguage']
        ?.toString() ??
        ''
        : toBeginningOfSentenceCase(
      levelName.split('-').last.toUpperCase().trim().toLowerCase(),
    );

    final Color color = _isLightMode
        ? (_classificationMap['$level-color'] as Color?) ?? AppColors.maroon
        : AppColors.greyShade2;

    String toolTipMessage =
        _classificationMap['$level-tooltip-$appLanguage']?.toString() ?? '';

    if (toolTipMessage.isEmpty) {
      if (HiveUtilsSettings.isLanguageArabic && !RegExp(r'^[a-z]+$').hasMatch(classificationText.toLowerCase())) {
        toolTipMessage = 'التصنيف $classificationText';
      } else {
        toolTipMessage = '$classificationText Classification';
      }
    }

    return Padding(
      padding: margin,
      child: Tooltip(
        message: toolTipMessage,
        preferBelow: false,
        verticalOffset: 12,
        triggerMode: TooltipTriggerMode.tap,
        showDuration: const Duration(seconds: 3),
        margin: const EdgeInsets.symmetric(horizontal: 24),
        child: Container(
          padding: const EdgeInsets.fromLTRB(4, 1, 4, 1),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              5,
            ),
            border: Border.all(
              color: color,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  classificationText.toUpperCase(),
                  style: TextStyle(
                    fontSize: 10,
                    color: color,
                  ),
                ),
              ),
              const SizedBox(
                width: 4,
              ),
              Icon(
                Icons.info_outline_rounded,
                size: 18,
                color: color,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
