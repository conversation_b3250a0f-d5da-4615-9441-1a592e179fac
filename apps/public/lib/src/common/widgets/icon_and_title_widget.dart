import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class IconAndTitleWidget extends StatelessWidget {
  const IconAndTitleWidget({
    required this.icon,
    required this.title,
    required this.content,
    super.key,
  });

  final String icon;
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(icon),
        const SizedBox(width: 12),
        Flexible(
          child: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: '$title ',
                  style: const TextStyle(
                    color: AppColors.greyShade4,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: content,
                  style: TextStyle(
                    color:
                        isLightMode ? AppColors.blackTextTile : AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
