import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import '../../utils/styles/app_text_styles.dart';

class ContactCard extends StatelessWidget {
  const ContactCard({
    required this.iconPath,
    required this.contact,
    required this.onTap,
    super.key,
  });

  final String iconPath;
  final String contact;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.isLightMode;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        height: 110,
        decoration: BoxDecoration(
          color: isLightMode ? AppColors.white : AppColors.blueShade32,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: AppColors.greyShade6.withValues(alpha: 0.08),
              blurRadius: 50,
              spreadRadius: 0.23,
              offset: const Offset(0, 35),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16,horizontal: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SvgPicture.asset(
                iconPath,
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      textDirection :TextDirection.ltr,
                      maxLines: 1,
                      contact,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyles.s16w4cGrey.copyWith(
                        color: !isLightMode ? AppColors.white : null,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
