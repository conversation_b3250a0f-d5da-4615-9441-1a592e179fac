import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/asset_constants/image_constants.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class ChartLegend extends StatelessWidget {
  const ChartLegend({
    required this.label,
    required this.color,
    this.isLightMode,
    this.isForecast = false,
    super.key,
  });

  final String label;
  final Color color;
  final bool isForecast;
  final bool? isLightMode;

  @override
  Widget build(BuildContext context) {
    final isLightMode = this.isLightMode ?? HiveUtilsSettings.isLightMode;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 6),
          child: SvgPicture.asset(
            isForecast ? AppImages.icLegendPurple : AppImages.icLegendGreen,
            colorFilter: ColorFilter.mode(
              color,
              BlendMode.srcIn,
            ),
          ),
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            label,
            textAlign: TextAlign.start,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.white,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }
}
