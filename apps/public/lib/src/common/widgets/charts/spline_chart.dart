import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../utils/app_utils/string_utils.dart';
import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/extentions/date_time_extensions.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../functions/indicator_date_setting.dart';
import 'chart_utils.dart';

class AxisParams<T> {
  const AxisParams({this.minimum, this.maximum, this.interval});

  final T? maximum;
  final T? minimum;
  final double? interval;

  @override
  String toString() => 'AxisParams(minimum: $minimum, maximum: $maximum, interval: $interval)';
}

class SplineChart extends StatefulWidget {
  const SplineChart({
    required this.frequency,
    this.indicatorCard = false,
    this.chartDataList = const [],
    this.comparisonChartData = const [],
    this.forecastChartDataList = const [],
    this.showForecast = false,
    this.showMarker = false,
    this.areaHighlightChartData = const [],
    this.isCompareActive = false,
    this.isLightMode,
    this.driver = false,
    this.yAxisLabel,
    this.yAxisLabelRight,
    super.key,
  });

  final List<List<SplineChartData>> chartDataList;
  final List<SplineChartData> comparisonChartData;
  final List<List<SplineChartData>> areaHighlightChartData;
  final List<List<SplineChartData>> forecastChartDataList;
  final bool showForecast;
  final bool showMarker;
  final bool indicatorCard;

  final bool isCompareActive;
  final bool? isLightMode;
  final bool driver;
  final String frequency;
  final String? yAxisLabel;
  final String? yAxisLabelRight;

  @override
  State<SplineChart> createState() => _SplineChartState();
}

class _SplineChartState extends State<SplineChart> {
  late final isLightMode = widget.isLightMode ?? HiveUtilsSettings.isLightMode;

  late final _chartColors = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

  @override
  Widget build(BuildContext context) {
    final Map<String, DateTime> maxAndMin = _calculateMaxAndMinValue(
      chartDataList: widget.chartDataList,
      forecast: widget.forecastChartDataList,
    );

    final solidSeriesLength = (widget.chartDataList.map((e) => e.length).toList()..sort()).lastOrNull ?? 0;
    final forecastSeriesLength = (widget.forecastChartDataList.map((e) => e.length).toList()..sort()).lastOrNull ?? 0;

    final dataLength = solidSeriesLength + forecastSeriesLength;

    final interval = ChartUtils.calculateInterval(
      dataLength: dataLength,
      frequency: widget.frequency,
      maxAndMin: maxAndMin,
    );

    final yAxisParams = _getYAxisParams();

    // print('_SplineChartState.build: -----------key: ${widget.key}\n'
    //     '\t\t\t\tOG-MaxMin: ${maxAndMin['ogMin']} -> ${maxAndMin['ogMax']}\n'
    //     '\t\t\t\tmaxAndMin: ${maxAndMin['min']} -> ${maxAndMin['max']}\n'
    //     '\t\t\t\tx-interval: $interval ; chart-type: ${widget.frequency} \n'
    //     '\t\t\t\ty-axis-params: $yAxisParams \n'
    //     '----------------------------------------');

    final intervalType = dataLength == 1
        ? DateTimeIntervalType.days
        : widget.frequency == 'Yearly'
        ? DateTimeIntervalType.years
        : DateTimeIntervalType.months;

    return ValueListenableBuilder(
      valueListenable: HiveUtilsSettings.textSizeFactorListenable,
      builder: (context, box, child) {
        final labelSize = 10.0 * min(HiveUtilsSettings.textSizeFactor, 1.1);

        return SfCartesianChart(
          trackballBehavior: _getChartTrackballBehavior(),
          onTrackballPositionChanging: (args) => ChartUtils.onTrackballPositionChanging(args, widget.frequency),
          backgroundColor: isLightMode
              ? widget.indicatorCard
              ? Colors.transparent
              : AppColors.blueShade29
              : Colors.transparent,
          zoomPanBehavior: ZoomPanBehavior(
            maximumZoomLevel: 1,
            enablePinching: true,
            zoomMode: ZoomMode.x,
            enablePanning: true,
            enableDoubleTapZooming: true,
          ),
          plotAreaBorderWidth: 0,
          primaryXAxis: DateTimeAxis(
            maximum: maxAndMin['max'],
            minimum: maxAndMin['min'],
            axisLine: AxisLine(
              width: 0.3,
              color: isLightMode ? AppColors.black : AppColors.white,
            ),
            majorTickLines: MajorTickLines(
              color: isLightMode ? AppColors.black : AppColors.greyShade4,
            ),
            labelRotation: -90,
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: MajorGridLines(
              width: 10,
              color: (isLightMode
                  ? widget.driver
                  ? AppColors.blueShade21
                  : AppColors.greyShade15_1
                  : AppColors.blueShade33)
                  .withValues(alpha: .8),
            ),
            minorGridLines: const MinorGridLines(
              width: 1,
              color: Colors.transparent,
            ),
            interval: interval,
            plotOffset: 10,
            intervalType: intervalType,
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                ' ${IndicatorDateSetting.setupNameAll(
                  widget.frequency,
                  DateTime.fromMillisecondsSinceEpoch(
                    axisLabelRenderArgs.value as int,
                  ).toFormattedDateTimeString('yyyy-MM-dd'),
                )} ',
                TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: labelSize,
                ),
              );
            },
          ),
          primaryYAxis: NumericAxis(
            name: 'yAxis1',
            numberFormat: NumberFormat.compactSimpleCurrency(name: '', decimalDigits: 2),
            anchorRangeToVisiblePoints: false,
            rangePadding: ChartRangePadding.round,
            minimum: yAxisParams.minimum,
            maximum: yAxisParams.maximum,
            interval: yAxisParams.interval,
            desiredIntervals: 6,
            title: AxisTitle(
              text: widget.yAxisLabel,
              textStyle: TextStyle(
                fontSize: labelSize,
                color: isLightMode ? AppColors.black : AppColors.white,
              ),
            ),
            axisLine: AxisLine(
              width: 0.3,
              color: isLightMode ? AppColors.black : AppColors.white,
            ),
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                axisLabelRenderArgs.text,
                TextStyle(
                  fontSize: labelSize,
                  color: widget.comparisonChartData.isNotEmpty
                      ? _chartColors.first
                      : isLightMode
                      ? AppColors.black
                      : AppColors.white,
                ),
              );
            },
            majorTickLines: const MajorTickLines(width: 0),
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: const MajorGridLines(width: 0),
            minorGridLines: const MinorGridLines(width: 0),
          ),
          axes: <ChartAxis>[
            if (widget.isCompareActive)
              NumericAxis(
                name: 'yAxis2',
                opposedPosition: true,
                numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
                rangePadding: ChartRangePadding.round,
                title: AxisTitle(
                  text: widget.yAxisLabelRight,
                  textStyle: TextStyle(
                    fontSize: labelSize,
                    color: isLightMode ? AppColors.black : AppColors.white,
                  ),
                ),
                axisLine: AxisLine(
                  width: 0.3,
                  color: isLightMode ? AppColors.black : AppColors.white,
                ),
                axisLabelFormatter: (axisLabelRenderArgs) {
                  return ChartAxisLabel(
                    axisLabelRenderArgs.text,
                    TextStyle(
                      fontSize: labelSize,
                      color: AppColors.chartColorSet[1],
                    ),
                  );
                },
                majorTickLines: const MajorTickLines(width: 0),
                minorTickLines: const MinorTickLines(width: 0),
                majorGridLines: const MajorGridLines(width: 0),
                minorGridLines: const MinorGridLines(width: 0),
              ),
          ],
          series: <CartesianSeries<SplineChartData, DateTime>>[
            /// ACTUAL DATA
            ..._solidSplineSeries,

            /// to show triangular forecast
            if (widget.showForecast) ...[
              /// FORECASTING - first line
              for (final forecastChartData in widget.forecastChartDataList)
                SplineSeries<SplineChartData, DateTime>(
                  dataLabelSettings: !widget.showMarker
                      ? const DataLabelSettings()
                      : const DataLabelSettings(
                    isVisible: true,
                    labelPosition: ChartDataLabelPosition.outside,
                  ),
                  animationDuration: 300,
                  dataSource: forecastChartData,
                  xValueMapper: (SplineChartData data, _) => IndicatorDateSetting.dateFormatterForChart(
                    widget.frequency,
                    data.label ?? '',
                  ),
                  yValueMapper: (SplineChartData sales, _) => sales.x,
                  dashArray: const <double>[3, 3],
                  color: _chartColors[widget.forecastChartDataList.indexOf(forecastChartData) % _chartColors.length],
                  name: 'forecast',
                  markerSettings: MarkerSettings(
                    isVisible: true,
                    width: 5,
                    height: 5,
                    color: _chartColors[widget.forecastChartDataList.indexOf(forecastChartData) % _chartColors.length],
                    shape: DataMarkerType.rectangle,
                  ),
                ),

              // /// FORECASTING - last line
              for (int i = 0; i < widget.areaHighlightChartData.length; i++)
                SplineSeries<SplineChartData, DateTime>(
                  animationDuration: 300,
                  dataSource: widget.areaHighlightChartData[i],
                  xValueMapper: (SplineChartData sales, _) => IndicatorDateSetting.dateFormatterForChart(
                    widget.frequency,
                    sales.label ?? '',
                  ),
                  yValueMapper: (SplineChartData sales, _) => sales.y,
                  dashArray: const <double>[3, 3],
                  splineType: SplineType.cardinal,
                  color: _chartColors[(i ~/ 2) % _chartColors.length],
                  name: 'Forecast',
                ),

              // /// FORECASTING - area highlighting
              for (final dataList in widget.areaHighlightChartData)
                SplineRangeAreaSeries<SplineChartData, DateTime>(
                  animationDuration: 300,
                  dataSource: dataList,
                  xValueMapper: (SplineChartData sales, _) => IndicatorDateSetting.dateFormatterForChart(
                    widget.frequency,
                    sales.label ?? '',
                  ),
                  highValueMapper: (SplineChartData sales, _) => sales.x,
                  lowValueMapper: (SplineChartData sales, _) => sales.y,
                  splineType: SplineType.cardinal,
                  color: _chartColors.first.withValues(alpha: 0.2),
                  name: 'Forecast',
                ),
            ],
          ],
        );
      },
    );
  }

  TrackballBehavior _getChartTrackballBehavior() {
    final shouldNotGroup = widget.chartDataList.length == 1 || widget.chartDataList.length > 12;
    final trackballDisplayMode =
    shouldNotGroup ? TrackballDisplayMode.nearestPoint : TrackballDisplayMode.groupAllPoints;
    final format = shouldNotGroup ? 'point.x: point.y' : 'point.y';

    return TrackballBehavior(
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipDisplayMode: trackballDisplayMode,
      shouldAlwaysShow: true,
      lineDashArray: const [4, 2],
      markerSettings: const TrackballMarkerSettings(
        height: 5,
        width: 5,
        markerVisibility: TrackballVisibilityMode.visible,
      ),
      tooltipSettings: InteractiveTooltip(
        format: format,
        color: isLightMode ? AppColors.white : AppColors.greyShade3,
        textStyle: const TextStyle(
          color: AppColors.greyShade19,
        ),
      ),
    );
  }

  List<SplineSeries<SplineChartData, DateTime>> get _solidSplineSeries {
    final seriesList = <SplineSeries<SplineChartData, DateTime>>[];

    for (int i = 0; i < widget.chartDataList.length; i++) {
      final color = _chartColors[i % _chartColors.length];

      final spline = SplineSeries<SplineChartData, DateTime>(
        dataLabelSettings: _getDataLabelSettings(i),
        animationDuration: 300,
        name: 'xAxis',
        splineType: SplineType.cardinal,
        dataSource: widget.chartDataList[i],
        xValueMapper: (SplineChartData data, _) => IndicatorDateSetting.dateFormatterForChart(
          widget.frequency,
          data.label ?? '',
        ),
        yValueMapper: (SplineChartData data, _) => data.x,
        color: color,
        cardinalSplineTension: 0,
        markerSettings: MarkerSettings(
          width: 6,
          height: 6,
          isVisible: widget.comparisonChartData.length > 24 || widget.chartDataList[i].length > 24 ? false : true,
          color: color,
        ),
        yAxisName: widget.isCompareActive
            ? i == 0
            ? 'yAxis1'
            : 'yAxis2'
            : 'yAxis1',
      );

      seriesList.add(spline);
    }

    return seriesList;
  }

  DataLabelSettings _getDataLabelSettings(int index) {
    if (!widget.showMarker) {
      return const DataLabelSettings();
    }

    final color = _chartColors[index % _chartColors.length];

    return DataLabelSettings(
      isVisible: true,
      useSeriesColor: true,
      margin: EdgeInsets.zero,
      color: color,
      borderColor: color,
      labelPosition: ChartDataLabelPosition.outside,
      overflowMode: OverflowMode.shift,
      angle: 90,
      builder: (data, point, series, pointIndex, seriesIndex) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            ShortNumberPipe.transform(
              number: point.y.toString(),
              angularPipeFormat: '1.0-2',
            ) ??
                '',
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  Map<String, DateTime> _calculateMaxAndMinValue({
    List<List<SplineChartData>> chartDataList = const [],
    List<List<SplineChartData>> forecast = const [],
  }) {
    final now = DateTime.now();

    try {
      final x = chartDataList.where((e) => e.isNotEmpty).firstOrNull?.firstOrNull?.label;
      final initial = x == null ? now : DateTime.tryParse(x) ?? now;
      DateTime ogMax = initial;
      DateTime ogMin = initial;

      for (final collection in chartDataList) {
        for (final element in collection) {
          final date = DateTime.tryParse(element.label ?? '') ?? now;
          if (date.compareTo(ogMax) > 0) ogMax = date;
          if (date.compareTo(ogMin) < 0) ogMin = date;
        }
      }

      if (widget.showForecast) {
        for (final collection in forecast) {
          for (final element in collection) {
            final date = DateTime.tryParse(element.label ?? '') ?? now;
            if (date.compareTo(ogMax) > 0) ogMax = date;
            if (date.compareTo(ogMin) < 0) ogMin = date;
          }
        }
      }

      DateTime max = ogMax.copyWith();
      DateTime min = ogMin.copyWith();

      final diff = max.difference(min);

      if (diff.inDays == 0) {
        max = max.add(const Duration(days: 1));
        min = min.subtract(const Duration(days: 1));

        return {'max': max, 'min': min, 'ogMax': ogMax, 'ogMin': ogMin};
      }

      return {'max': max, 'min': min, 'ogMax': ogMax, 'ogMin': ogMin};
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return {'max': now, 'min': now};
    }
  }

  AxisParams<double> _getYAxisParams() {
    final seriesList = [
      ...widget.chartDataList,
      if (widget.showForecast) ...widget.forecastChartDataList,
      if (widget.showForecast) ...widget.areaHighlightChartData,
    ];

    double? minValue = seriesList.firstOrNull?.firstOrNull?.x?.toDouble() ?? 0.0;
    double? maxValue = seriesList.lastOrNull?.lastOrNull?.x?.toDouble() ?? 0.0;
    // Check all chart data lists
    for (final series in seriesList) {
      for (final point in series) {
        final x = point.x?.toDouble() ?? 0.0;
        final y = point.y?.toDouble() ?? x;
        final maxX = max(x, y);
        final minX = min(x, y);

        if (minValue == null || minX < minValue) minValue = minX;
        if (maxValue == null || maxX > maxValue) maxValue = maxX;
      }
    }

    minValue = minValue?.floor().toDouble();
    maxValue = maxValue?.ceil().toDouble();

    // If range is too small, force a reasonable interval
    if (minValue != null && maxValue != null) {
      final dataLength = seriesList.fold(0, (value, seriesData) => value += seriesData.length);
      if (dataLength == 1) {
        final power = maxValue.toString().split('.').first.length;
        final offset = pow(10, power) / 4;
        maxValue += offset;
        minValue -= offset;
      }

      final range = maxValue - minValue;
      if (range <= 10) {
        final interval = range <= 5 ? 1.0 : 2.0;
        return AxisParams(
          minimum: minValue,
          maximum: maxValue,
          interval: interval <= 0 ? null : interval,
        );
      }
    }

    return const AxisParams();
  }
}

class SplineChartData {
  SplineChartData(
      this.label,
      this.x, {
        this.y,
        this.legend,
        this.tempLabel,
      });

  final String? label;
  final DateTime? tempLabel;
  final num? x;
  final num? y;
  final String? legend;

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'tempLabel': tempLabel,
      'x': x,
      'y': y,
      'legend': legend,
    };
  }
}
