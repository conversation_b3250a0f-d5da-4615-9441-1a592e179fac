import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';

class AppChart {
  static TooltipBehavior tooltipBehavior({
    String? formatter,
    bool useTooltipStringFn = false,
    String Function(ChartPoint<dynamic> chartPoint)? tooltipStringFn,
  }) {
    return TooltipBehavior(
      enable: true,
      tooltipPosition: TooltipPosition.pointer,
      shadowColor: Colors.transparent,
      color: AppColors.blackShade1,
      elevation: 0,
      borderWidth: 0,
      format: useTooltipStringFn? null:(formatter ?? 'point.x:point.y'),
      textAlignment: ChartAlignment.near,
      textStyle: const TextStyle(color: Colors.white, fontSize: 12),
      builder: !useTooltipStringFn
          ? null
          : (data, chartPoint, chartSeries, int pointIndex, int seriesIndex) {
        return FittedBox(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.blackShade1,
            ),
            alignment: Alignment.center,
            child: Text(
              tooltipStringFn?.call(chartPoint) ??
                  '${chartPoint.x} : ${NumberFormat.compact().format(chartPoint.y)}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        );
      },
    );
  }

  // This TrackballBehavior should not be used for horizontal charts
  // unless it is updated to show the tooltip for higher values.
  static TrackballBehavior trackballBehavior({
    String? Function(TrackballDetails trackballDetails)? tooltipStringFn,
  }) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;

    final List<Color> chartColorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return TrackballBehavior(
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipDisplayMode: TrackballDisplayMode.nearestPoint,
      shouldAlwaysShow: true,
      markerSettings: const TrackballMarkerSettings(
        height: 10,
        width: 10,
        markerVisibility: TrackballVisibilityMode.visible,
        borderColor: Colors.black,
        color: Colors.blue,
      ),
      tooltipAlignment: ChartAlignment.near,
      builder: (context, trackballDetails) {
        String? text = tooltipStringFn?.call(trackballDetails);
        if (text == null) {
          final x = trackballDetails.point?.x;
          final y = trackballDetails.point?.y;
          if (x != null && y != null) {
            text = '$x : ${NumberFormat.compact().format(y)}';
          }
        }

        if (text == null) {
          return const SizedBox.shrink();
        }

        return FittedBox(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColors.greyShade13),
              color: (trackballDetails.series.color as Color?) ?? chartColorSet[(trackballDetails.seriesIndex ?? 0) % chartColorSet.length],
            ),
            alignment: Alignment.center,
            child: Text(
              text,
              textAlign: TextAlign.left,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        );
      },
    );
  }
}
