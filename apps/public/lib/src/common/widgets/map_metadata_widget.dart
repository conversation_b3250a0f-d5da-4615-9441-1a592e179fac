import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/styles/app_text_styles.dart';

class MapMetadataWidget extends StatelessWidget {
  const MapMetadataWidget({
    required this.region,
    required this.count,
    required this.color,
    super.key,
  });

  final Color color;
  final String region;
  final String count;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 3, right: 8),
          child: Icon(Icons.circle, size: 10, color: color),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                region,
                style: AppTextStyles.s12w5cBlack,
              ),
              Text(
                count,
                style: AppTextStyles.s12w4cblueGreyShade1.copyWith(
                  color: AppColors.grey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
