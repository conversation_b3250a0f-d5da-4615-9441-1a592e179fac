import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/color_constants/color_constants.dart';

class PrimaryIconButton extends StatelessWidget {
  const PrimaryIconButton({
    required this.iconPath,
    super.key,
    this.backgroundColor = AppColors.blueShade1,
    this.padding = const EdgeInsets.all(8),
    this.onTap,
    this.size,
    this.borderRadius = 24,
    this.tooltip,
    this.elevation = 0,
    this.shadowColor,
    this.iconColor = Colors.black,
    this.enabled = true,
  });

  final Color backgroundColor;
  final Color iconColor;
  final Color? shadowColor;
  final double? size;
  final double elevation;
  final String? tooltip;
  final String iconPath;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTap;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Material(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      color: backgroundColor,
      shadowColor: shadowColor,
      elevation: elevation,
      child: InkWell(
        onTap: enabled ? onTap : null,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Tooltip(
          message: tooltip ?? '',
          child: Padding(
            padding: padding,
            child: SvgPicture.asset(
              iconPath,
              height: size,
              width: size,
              colorFilter: ColorFilter.mode(
                enabled ? iconColor : AppColors.greyShade4,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
