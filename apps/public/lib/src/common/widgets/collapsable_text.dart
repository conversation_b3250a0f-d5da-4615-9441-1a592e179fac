import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import '../../utils/styles/app_text_styles.dart';

class CollapsableText extends StatefulWidget {
  const CollapsableText({
    required this.text,
    this.style,
    super.key,
  });

  final String text;
  final TextStyle? style;

  @override
  State<CollapsableText> createState() => _CollapsableTextState();
}

class _CollapsableTextState extends State<CollapsableText> {
  int _getLineLength(double maxWidth) {
    final tp = TextPainter(
      text: TextSpan(
        text: widget.text,
        style: AppTextStyles.s14w4cblackShade4,
      ),
      textScaler: TextScaler.linear(HiveUtilsSettings.textSizeFactor),
      textDirection: HiveUtilsSettings.isLanguageArabic ? TextDirection.rtl : TextDirection.ltr,
    )..layout(maxWidth: maxWidth);

    return tp.computeLineMetrics().length;
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    final defaultTextStyle = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: isLightMode ? AppColors.grey : AppColors.white,
    );

    return ValueListenableBuilder(
      valueListenable: HiveUtilsSettings.textSizeFactorListenable,
      builder: (context, box, child) {
        final textSizeFactor = HiveUtilsSettings.textSizeFactor;

        return Padding(
          padding: EdgeInsets.symmetric(vertical: 4 - textSizeFactor),
          child: ExpandableNotifier(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final lines = _getLineLength(constraints.maxWidth - 24);
                final controller = ExpandableController.of(
                  context,
                  required: true,
                )!;

                return Row(
                  crossAxisAlignment: controller.expanded ? CrossAxisAlignment.start : CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: ExpandablePanel(
                        theme: ExpandableThemeData(
                          bodyAlignment: HiveUtilsSettings.isLanguageArabic
                              ? ExpandablePanelBodyAlignment.right
                              : ExpandablePanelBodyAlignment.left,
                          headerAlignment: ExpandablePanelHeaderAlignment.center,
                          tapBodyToCollapse: true,
                          hasIcon: false,
                        ),
                        header: const SizedBox(),
                        collapsed: Text(
                          widget.text,
                          softWrap: true,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: widget.style ?? defaultTextStyle,
                        ),
                        expanded: Text(
                          widget.text,
                          maxLines: 100,
                          style: widget.style ?? defaultTextStyle,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    if (lines > 1)
                      Builder(
                        builder: (context) {
                          return Material(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(20),
                              onTap: controller.toggle,
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: controller.expanded ? 6 : 2, horizontal: 2),
                                child: AnimatedBuilder(
                                  animation: controller,
                                  builder: (BuildContext context, Widget? child) {
                                    return Transform.rotate(
                                      angle: 0,
                                      // angle: pi,
                                      child: child,
                                    );
                                  },
                                  child: Icon(
                                    controller.expanded
                                        ? Icons.keyboard_arrow_up_rounded
                                        : Icons.keyboard_arrow_down_rounded,
                                    size: 18,
                                    color: isLightMode ? AppColors.greyShade11 : AppColors.blueLightOld,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
