import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../translations/locale_keys.g.dart';
import '../../utils/constants/color_constants/color_constants.dart';

class ErrorReloadPlaceholder extends StatelessWidget {
  const ErrorReloadPlaceholder({
    required this.error,
    this.onReload,
    super.key,
    this.padding = const EdgeInsets.all(8),
  });

  final String error;
  final VoidCallback? onReload;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    error,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
            if (onReload != null)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.greyShade1),
                  ),
                  onPressed: onReload,
                  child: Text(LocaleKeys.reload.tr()),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
