import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class AppShimmer extends StatelessWidget {
  const AppShimmer({
    super.key,
    this.height,
    this.width,
    this.radius = 12,
    this.baseColor,
    this.highlightColor,
  });

  final double? height;
  final double? width;
  final double radius;
  final Color? baseColor;
  final Color? highlightColor;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Shimmer.fromColors(
      baseColor: baseColor ?? (isLightMode ? AppColors.greyShade10 : AppColors.blueShade30),
      highlightColor: highlightColor ?? (isLightMode ? AppColors.greyShade1 : AppColors.blueShade36),
      child: Container(
        decoration: BoxDecoration(
          color: baseColor ?? (isLightMode ? AppColors.greyShade10 : AppColors.blueShade30),
          borderRadius: BorderRadius.circular(radius),
        ),
        height: height,
        width: width,
      ),
    );
  }
}
