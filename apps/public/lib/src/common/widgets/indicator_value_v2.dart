import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../translations/locale_keys.g.dart';
import '../../utils/app_utils/string_utils.dart';
import '../../utils/constants/asset_constants/image_constants.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/extentions/date_time_extensions.dart';
import '../../utils/extentions/indicator_extensions.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import 'indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'indicator_card/data/models/indicator_details_response.dart';

class IndicatorValueV2 extends StatefulWidget {
  const IndicatorValueV2({
    required this.isCardView,
    this.indicatorDetails,
    this.overView,
    this.visualization,
    this.chartIndex = 0,
    super.key,
  }) : assert(
  indicatorDetails != null || visualization != null,
  'indicatorDetails or visualization is required',
  );

  final int chartIndex;
  final bool isCardView;
  final Visualizations? visualization;
  final IndicatorDetailsResponse? indicatorDetails;
  final OverView? overView;

  @override
  State<IndicatorValueV2> createState() => _IndicatorValueV2State();
}

class _IndicatorValueV2State extends State<IndicatorValueV2> {
  final bool _isLightMode = HiveUtilsSettings.isLightMode;
  final bool _isRtl = HiveUtilsSettings.isLanguageArabic;

  String _value = '';
  String _headerTitle = '';
  String _valueFormat = '';
  String _valueUnit = '';
  String _compareValue = '';
  String _compareValuePercentage = '';
  String? _comparingTo;

  bool hasCompareTo = false;

  OverView? get overview => widget.overView ?? widget.indicatorDetails?.overView;

  @override
  void initState() {
    super.initState();
    _initValues();
  }

  @override
  void didUpdateWidget(covariant IndicatorValueV2 oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.overView.hashCode != widget.overView.hashCode) {
      setState(_initValues);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCardView) {
      final indicatorCategory = widget.indicatorDetails?.getIndicatorCategory();

      if (indicatorCategory == IndicatorCategory.analyticalApps) {
        final overviewValuesMeta = widget.indicatorDetails?.indicatorValues?.overviewValuesMeta ?? [];
        if (overviewValuesMeta.isEmpty) {
          return const SizedBox.shrink();
        }

        final indicatorType = widget.indicatorDetails?.getIndicatorType();
        if (indicatorType == IndicatorType.insightDiscovery) {
          // show last point of the series, the whole if body is for this
          final String chartType = widget.indicatorDetails?.indicatorVisualizations?.visualizationsMeta != null
              ? (widget.indicatorDetails?.getFilteredVisualizationMetaList() ?? []).isNotEmpty
              ? widget.indicatorDetails?.getFilteredVisualizationMetaList().first.type ?? ''
              : ''
              : '';

          //  hopefully its not a tree chart
          if (chartType == 'tree-map-with-change-chart') return const SizedBox.shrink();

          final seriesDataList = widget.indicatorDetails?.getFilteredSeriesForMultiDrivers() ?? [];
          seriesDataList.firstOrNull?.sort(
                (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
          );

          final lastPoint = seriesDataList.firstOrNull?.lastOrNull;
          if (lastPoint == null) {
            return const SizedBox.shrink();
          }

          final value = ShortNumberPipe.transform(
            number: lastPoint['VALUE'],
            angularPipeFormat: _valueFormat,
          ) ??
              '';

          return _buildValueAndUnit(value, _valueUnit);
        }
      }

      bool? isNegativeArrow;

      if (widget.isCardView && _compareValue.isNotEmpty) {
        final num n = num.tryParse(_compareValue) ?? 0;
        isNegativeArrow = n == 0 ? null : n < 0;
      } else {
        isNegativeArrow = null;
      }

      String value = ShortNumberPipe.transform(
        number: _value,
        angularPipeFormat: _valueFormat,
      ) ??
          '';
      if (_valueUnit.isNotEmpty) {
        value = value.replaceAll('%', '');
      }
      final String compareValue = ShortNumberPipe.transform(
        number: _compareValue,
        angularPipeFormat: _valueFormat,
      ) ??
          '';

      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_headerTitle.isNotEmpty) Text(_headerTitle),
          if (value.isNotEmpty) _buildValueAndUnit(value, _valueUnit),
          if (hasCompareTo && compareValue.isNotEmpty) ...[
            Row(
              children: [
                if (isNegativeArrow != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Transform.flip(
                        flipX: HiveUtilsSettings.isLanguageArabic,
                        child: RotatedBox(
                          quarterTurns: isNegativeArrow ? 2 : 0,
                          child: SvgPicture.asset(
                            AppImages.icTrendingUp,
                            colorFilter: ColorFilter.mode(
                              isNegativeArrow ? AppColors.red : AppColors.green,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 3),
                        child: Text(
                          isNegativeArrow ? LocaleKeys.downTo.tr() : LocaleKeys.upTo.tr(),
                          style: TextStyle(
                            color: _isLightMode ? AppColors.black : AppColors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                Text.rich(
                  maxLines: 2,
                  TextSpan(
                    text: compareValue,
                    style: TextStyle(
                      color: _isLightMode ? AppColors.black : AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    children: [
                      const TextSpan(text: '  '),
                      if (_compareValuePercentage.isNotEmpty)
                        TextSpan(
                          text: '($_compareValuePercentage%)',
                          style: TextStyle(
                            color: _isLightMode ? AppColors.black : AppColors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: _buildCompareToDropdown(),
            ),
          ],
        ],
      );
    } else {
      IndicatorValues? indicatorValues;
      if (widget.visualization != null) {
        indicatorValues = widget.visualization?.indicatorValues;
      } else {
        indicatorValues = widget.indicatorDetails?.indicatorValues;
      }

      final overviewValuesMeta =
          indicatorValues?.valuesMeta ?? indicatorValues?.multiValuesMeta?.elementAt(widget.chartIndex) ?? [];

      if (overviewValuesMeta.isEmpty) {
        return const SizedBox();
      } else {
        return Wrap(
          spacing: 12,
          children: overviewValuesMeta.map((e) {
            if (e.type == 'forecasted-data') {
              return Container(
                margin: const EdgeInsets.only(top: 22),
                height: 36,
                child: const VerticalDivider(color: AppColors.green),
              );
            } else {
              String val = '';
              bool? isNegativeArrow;
              Color? arrowColor;

              if (e.valueFormat != null && (e.valueFormat?.startsWith('number') ?? false) ||
                  (e.valueFormat?.startsWith('percentage') ?? false)) {
                final num n = num.tryParse(e.value.toString()) ?? 0;

                isNegativeArrow = (e.valueFormat?.startsWith('percentage') ?? false)
                    ? null
                    : n == 0
                    ? null
                    : n < 0;

                if (isNegativeArrow != null) {
                  if (e.invertColor == true) {
                    arrowColor = isNegativeArrow ? AppColors.green : AppColors.red;
                  } else {
                    arrowColor = isNegativeArrow ? AppColors.red : AppColors.green;
                  }
                  if (e.invertArrow == true) {
                    isNegativeArrow = !isNegativeArrow;
                  }
                }

                val = ShortNumberPipe.transform(
                  number: e.value,
                  angularPipeFormat: e.valueFormat,
                ) ??
                    '';
              } else {
                val = e.value ?? '';
              }

              if (val.isEmpty) return const SizedBox();

              String title = e.title ?? '';
              if (title.contains('{')) {
                final List<String> list = ((title.split('{'))..removeWhere((element) => !element.contains('}')))
                    .map((e) => '{${e.substring(0, e.indexOf('}') + 1)}')
                    .toList();

                for (int i = 0; i < list.length; i++) {
                  title = title.replaceFirst(
                    list[i],
                    _getQuarter(
                      (e.jsonValue ?? {})[list[i].substring(1, list[i].length - 1)]?.toString(),
                      e.templateFormat,
                    ) ??
                        '',
                  );
                }
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(e.headerTitle ?? ''),
                  const SizedBox(height: 4),
                  Text(
                    title,
                    style: TextStyle(
                      color: _isLightMode ? AppColors.grey : AppColors.whiteShade2,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (val.isNotEmpty)
                    Text.rich(
                      maxLines: 2,
                      TextSpan(
                        children: [
                          if (isNegativeArrow != null)
                            WidgetSpan(
                              baseline: TextBaseline.alphabetic,
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 3, right: 3),
                                child: RotatedBox(
                                  quarterTurns: isNegativeArrow ? 2 : 0,
                                  child: SvgPicture.asset(
                                    AppImages.icTrendingUp,
                                    colorFilter: ColorFilter.mode(
                                      arrowColor ?? Colors.transparent,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          WidgetSpan(
                            baseline: TextBaseline.alphabetic,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 2, right: 3),
                              child: Text(
                                val,
                                style: TextStyle(
                                  color: _isLightMode ? AppColors.black : AppColors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              );
            }
          }).toList(),
        );
      }
    }
  }

  Widget _buildValueAndUnit(String value, String unit) {
    return Text.rich(
      maxLines: 2,
      TextSpan(
        text: value,
        style: TextStyle(
          color: _isLightMode ? AppColors.black : AppColors.white,
          fontSize: 20,
          fontWeight: FontWeight.w700,
        ),
        children: [
          TextSpan(
            text: '  ',
            style: TextStyle(
              color: _isLightMode ? AppColors.black : AppColors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          TextSpan(
            text: unit,
            style: TextStyle(
              color: _isLightMode ? AppColors.black : AppColors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompareToDropdown() {
    return SizedBox(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            LocaleKeys.comparingTo.tr(),
            style: TextStyle(
              color: _isLightMode ? AppColors.grey : AppColors.white,
              fontSize: 12,
            ),
          ),
          if ((overview?.compareFilters ?? []).length > 1)
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: DropdownButton(
                  alignment: Alignment.center,
                  isDense: true,
                  underline: const SizedBox(),
                  value: _comparingTo,
                  style: TextStyle(
                    color: _isLightMode ? AppColors.grey : AppColors.white,
                    fontSize: 12,
                  ),
                  items: overview?.compareFilters!
                      .map(
                        (e) => DropdownMenuItem(
                      value: e,
                      child: Text(e),
                    ),
                  )
                      .toList(),
                  onChanged: (value) {
                    _onCompareToChanged(value);
                    setState(() {});
                  },
                ),
              ),
            )
          else
            Padding(
              padding: EdgeInsets.only(bottom: 4, top: 3, left: _isRtl ? 0 : 3, right: _isRtl ? 4 : 0),
              child: Text(
                overview?.compareFilters?.firstOrNull ?? '',
                style: TextStyle(
                  color: _isLightMode ? AppColors.grey : AppColors.white,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _onCompareToChanged(String? compareTo) {
    _comparingTo = compareTo;
    String compareValue = '';
    switch (compareTo) {
      case 'Y/Y':
        _compareValue = overview?.yearlyCompareValue?.toString() ?? '';
        compareValue = overview?.yearlyChangeValue?.toString() ?? '';
      case 'M/M':
        _compareValue = overview?.monthlyCompareValue?.toString() ?? '';
        compareValue = overview?.monthlyChangeValue?.toString() ?? '';
      case 'Q/Q':
        _compareValue = overview?.quarterlyCompareValue?.toString() ?? '';
        compareValue = overview?.quarterlyChangeValue?.toString() ?? '';
      default:
        _compareValue = '';
        compareValue = '';
    }

    _compareValuePercentage = ShortNumberPipe.transform(
      number: compareValue,
      angularPipeFormat: _valueFormat,
    ) ??
        '';
  }

  String? _getQuarter(String? date, String? templateFormat) {
    try {
      final DateTime dateTime = DateTime.parse(date.toString());

      if (templateFormat == 'quarter') {
        if (dateTime.month >= 10) {
          return 'Q4 ${dateTime.year}';
        } else if (dateTime.month >= 7) {
          return 'Q3 ${dateTime.year}';
        } else if (dateTime.month >= 4) {
          return 'Q2 ${dateTime.year}';
        } else {
          return 'Q1 ${dateTime.year}';
        }
      } else {
        return dateTime.toFormattedDateTimeString(templateFormat ?? '');
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return date;
    }
  }

  void _initValues() {
    if (widget.isCardView) {
      if (widget.indicatorDetails!.getIndicatorCategory() == IndicatorCategory.analyticalApps) {
        final OverviewValuesMeta? meta;
        // if(widget.indicatorDetails?.getIndicatorType() == IndicatorType.scenarioDriver){
        //   meta =
        //       (widget.indicatorDetails?.visualizations?.where((element) => element.id == widget.indicatorDetails?.defaultVisualisation).firstOrNull?.indicatorValues?.overviewValuesMeta ??
        //               [])
        //           .firstOrNull;
        // } else {
        meta = (widget.indicatorDetails?.indicatorValues?.overviewValuesMeta ?? []).firstOrNull;
        // }
        if (meta?.value != null) {
          _headerTitle = meta?.headerTitle ?? '';
          _value = meta?.value?.toString() ?? '';
          _valueFormat = meta?.valueFormat?.toString() ?? '';
          _valueUnit = _value.isEmpty ? '' : widget.indicatorDetails?.unit?.toString() ?? '';

          hasCompareTo = false;
        } else if ((meta?.values ?? []).isNotEmpty) {
          final int filterIndex = meta?.values?.indexWhere((e) => e.type == 'ABSOLUTE') ?? -1;
          if (filterIndex >= 0) {
            _value = meta?.values![filterIndex].value ?? '';
          } else {
            final int filterPercentage = meta?.values?.indexWhere(
                  (e) => e.type == 'PERCENTAGE' && meta?.aggregation == e.aggregation,
            ) ??
                -1;
            if (filterPercentage >= 0) {
              _value = '${meta?.values![filterPercentage].value}';
              _valueFormat = 'percentage-0.0-2'; //line code: eu89g4hg687 - added this line to fix % not showing
            }
          }
          _valueUnit = _value.isEmpty ? '' : widget.indicatorDetails?.unit?.toString() ?? '';
        }
      } else if ([IndicatorCategory.official, IndicatorCategory.experimental]
          .contains(widget.indicatorDetails!.getIndicatorCategory())) {
        _value = overview?.value?.toString() ?? '';
        _valueFormat = overview?.valueFormat?.toString() ?? '';
        _valueUnit = widget.indicatorDetails?.unit?.toString() ?? '';
        hasCompareTo = (overview?.compareFilters ?? []).isNotEmpty;
        if (hasCompareTo) {
          _onCompareToChanged(
            _comparingTo ?? overview?.compareFilters?.firstOrNull,
          );
        }
      }
    }
  }
}
