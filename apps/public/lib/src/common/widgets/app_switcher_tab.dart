import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import '../../utils/styles/app_text_styles.dart';
import 'domain_icon.dart';

class AppSwitcherTabItem {
  AppSwitcherTabItem({
    required this.label,
    this.iconAssetSvg,
    this.domainId,
  });

  final String label;
  final String? iconAssetSvg;
  final String? domainId;
}

class AppSwitcherTabController extends ValueNotifier<int> {
  AppSwitcherTabController({int? initialTab}) : super(initialTab ?? 0);

  void jumpToTab(int i) {
    value = i;
    notifyListeners();
  }
}

class AppSwitcherTab extends StatefulWidget {
  const AppSwitcherTab({required this.controller, required this.tabs, required this.onChanged, super.key});

  final AppSwitcherTabController controller;
  final List<AppSwitcherTabItem> tabs;
  final void Function(int index) onChanged;

  @override
  State<AppSwitcherTab> createState() => _AppSwitcherTabState();
}

class _AppSwitcherTabState extends State<AppSwitcherTab> {
  final bool _isLightMode = HiveUtilsSettings.isLightMode;

  bool _isExpanded = false;

  Timer? _timer;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.tabs.isEmpty) {
      return const SizedBox();
    }

    return Container(
      decoration: BoxDecoration(
        color: _isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ValueListenableBuilder(
        valueListenable: widget.controller,
        builder: (context, _, __) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.all(5),
                child: _button(
                  isLeftIcon: true,
                  onTap: widget.controller.value <= 0
                      ? null
                      : () {
                    widget.controller.jumpToTab(widget.controller.value - 1);
                    widget.onChanged(widget.controller.value);
                    _setCollapseTimer();
                  },
                ),
              ),
              Expanded(
                child: Column(
                  children: List.generate(widget.tabs.length, (index) {
                    return _label(
                      index: index,
                      onTap: () {
                        _isExpanded ? _collapse(index) : _expand();
                      },
                    );
                  }),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(5),
                child: _button(
                  isLeftIcon: false,
                  onTap: widget.controller.value >= widget.tabs.length - 1
                      ? null
                      : () {
                    widget.controller.jumpToTab(widget.controller.value + 1);
                    widget.onChanged(widget.controller.value);
                    _setCollapseTimer();
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _label({required int index, required VoidCallback onTap}) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 200),
      child: !_isExpanded && index != widget.controller.value
          ? const SizedBox()
          : Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Material(
                color: (_isExpanded && index == widget.controller.value)
                    ? AppColors.greyShade13
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
                child: InkWell(
                  borderRadius: BorderRadius.circular(6),
                  onTap: onTap,
                  child: Padding(
                    padding: const EdgeInsets.all(6),
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (widget.tabs[index].iconAssetSvg != null)
                            SvgPicture.asset(
                              widget.tabs[index].iconAssetSvg ?? '',
                              height: 22,
                              width: 22,
                              colorFilter:
                              _isLightMode ? null : const ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                            )
                          else if (widget.tabs[index].domainId != null)
                            DomainIcon(
                              domainIdOrName: widget.tabs[index].domainId,
                              size: 22,
                              color: _isLightMode ? null : AppColors.white,
                            ),
                          const SizedBox(width: 12),
                          Flexible(
                            child: Text(
                              widget.tabs[index].label,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.s18w5cBlackShade1.copyWith(
                                color: _isLightMode ? AppColors.blackShade1 : AppColors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _button({required bool isLeftIcon, required VoidCallback? onTap}) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        borderRadius: BorderRadius.circular(6),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(6),
          child: Icon(
            isLeftIcon ? Icons.chevron_left_rounded : Icons.chevron_right_rounded,
            size: 26,
            color: (isLeftIcon ? widget.controller.value > 0 : widget.controller.value < widget.tabs.length - 1)
                ? AppColors.blue
                : AppColors.greyShade4,
          ),
        ),
      ),
    );
  }

  void _expand() {
    setState(() {
      _isExpanded = true;
    });
    _setCollapseTimer();
  }

  void _collapse(int index) {
    _timer?.cancel();
    _isExpanded = false;
    widget.controller.jumpToTab(index);
    widget.onChanged(index);
  }

  void _setCollapseTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 3), (_) {
      _collapse(widget.controller.value);
    });
  }
}
