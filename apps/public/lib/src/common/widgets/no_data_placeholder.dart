import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../translations/locale_keys.g.dart';
import '../../utils/constants/asset_constants/animation_asset.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class NoDataPlaceholder extends StatelessWidget {
  const NoDataPlaceholder({
    this.msg,
    super.key,
    this.padding = EdgeInsets.zero,
  });

  final String? msg;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    String? message = msg;

    final bool isLightMode = HiveUtilsSettings.isLightMode;
    return Padding(
      padding: padding,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isLightMode)
            Lottie.asset(
              AnimationAsset.animationNoData,
              height: 110,
              width: 140,
            )
          else
            Lottie.asset(
              AnimationAssetDark.animationNoData,
              height: 110,
              width: 140,
            ),
          const SizedBox(height: 6),
          Text(
            message ?? LocaleKeys.noDataAvailable.tr(),
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: AppColors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
