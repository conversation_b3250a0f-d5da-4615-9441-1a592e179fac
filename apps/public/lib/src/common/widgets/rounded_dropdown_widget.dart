import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../translations/locale_keys.g.dart';
import '../../features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import '../../features/domains/data/models/domain_model/domain_model.dart';
import '../../utils/app_utils/device_type.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import 'indicator_card/data/models/indicator_details_response.dart';

class RoundedDropDownWidget<T> extends StatelessWidget {
  const RoundedDropDownWidget({
    super.key,
    this.showTitle = true,
    this.title,
    this.titleColor,
    this.width,
    this.constraintWidth,
    this.items,
    this.value,
    this.onChanged,
    this.showBorder = true,
    this.isSquare = false,
    this.boxShadow = false,
    this.compute = false,
    this.showIcon = true,
    this.showAsRequired = false,
    this.itemLabelBuilder,
    this.leadingIcon,
    this.enabled = true,
  });

  final bool? showTitle;
  final String? title;
  final Color? titleColor;
  final double? width;

  // TODO(Jerin): Remove compute if not needed since not used anywhere else
  final double? constraintWidth;
  final List<T>? items;
  final T? value;
  final bool showBorder;
  final bool isSquare;
  final bool boxShadow;

  // TODO(Jerin): Remove compute if not needed since not used anywhere else
  final bool compute;
  final bool showIcon;
  final bool showAsRequired;
  final void Function(T?)? onChanged;
  final String Function(T? item)? itemLabelBuilder;
  final Widget? leadingIcon;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<bool> selected = ValueNotifier(false);
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          FittedBox(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: title,
                    style: TextStyle(
                      color: titleColor ?? AppColors.greyShade4,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  if (showAsRequired) ...[
                    const WidgetSpan(
                      child: SizedBox(width: 5),
                    ),
                    const TextSpan(
                      text: '*',
                      style: TextStyle(
                        color: AppColors.redWarning,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 5),
        ],
        IgnorePointer(
          ignoring: !enabled,
          child: Opacity(
            opacity: enabled ? ((items ?? []).isNotEmpty ? 1 : 0.4) : 0.4,
            child: Container(
              width: compute ? (width ?? MediaQuery.sizeOf(context).width) : width,
              height: 40,
              decoration: boxShadow
                  ? ShapeDecoration(
                color: isLightMode ? AppColors.white : AppColors.blueShade32,
                shape: RoundedRectangleBorder(
                  borderRadius: isSquare ? BorderRadius.circular(15) : BorderRadius.circular(70),
                ),
                shadows: const [
                  BoxShadow(
                    color: AppColors.shadow1,
                    blurRadius: 5,
                    offset: Offset(1, 4),
                  ),
                ],
              )
                  : BoxDecoration(
                color: isLightMode ? AppColors.white : AppColors.blueShade36,
                borderRadius: isSquare ? BorderRadius.circular(15) : BorderRadius.circular(70),
                border: showBorder
                    ? Border.all(
                  color: isLightMode ? AppColors.greyShade1 : AppColors.blueShade32,
                )
                    : null,
              ),
              child: compute
                  ? LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  return IgnorePointer(
                    ignoring: compute ? false : (items?.length ?? 0) <= 1,
                    child: DropdownMenu(
                      trailingIcon: !showIcon ? const SizedBox() : null,
                      initialSelection: value,
                      hintText: LocaleKeys.select.tr(),
                      width: constraints.maxWidth,
                      inputDecorationTheme: InputDecorationTheme(
                        labelStyle: const TextStyle(fontSize: 4),
                        prefixStyle: const TextStyle(fontSize: 4),
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                        constraints: BoxConstraints.tight(
                          const Size.fromHeight(40),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      menuStyle: MenuStyle(
                        backgroundColor: WidgetStateProperty.resolveWith((states) {
                          return isLightMode
                              ? AppColors.white
                              : AppColors.blueShade32; //your desired selected background color
                        }),
                        surfaceTintColor: WidgetStateProperty.resolveWith((states) {
                          return Colors.transparent; //your desired selected background color
                        }),
                      ),
                      onSelected: (value) {
                        if (onChanged != null) {
                          onChanged?.call(value as T);
                          selected.value = false;
                        }
                      },
                      dropdownMenuEntries: [
                        ...items?.map((T value) {
                          return DropdownMenuEntry(
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.resolveWith((states) {
                                return isLightMode ? AppColors.white : AppColors.blueShade32;
                              }),
                              overlayColor: WidgetStateProperty.resolveWith((states) {
                                return isLightMode ? Colors.transparent : AppColors.blueShade32;
                              }),
                              foregroundColor: WidgetStateProperty.resolveWith((states) {
                                return isLightMode ? AppColors.black : AppColors.white;
                              }),
                            ),
                            value: value,
                            label: itemLabelBuilder?.call(value) ?? _getDisplayLabel(value, context),
                          );
                        }).toList() ??
                            [],
                      ],
                    ),
                  );
                },
              )
                  : PopupMenuButton<T>(
                color: isLightMode ? AppColors.white : AppColors.blueShade32,
                surfaceTintColor: AppColors.white,
                offset: const Offset(0, 40),
                borderRadius: BorderRadius.circular(24),
                onSelected: (T value) {
                  if (onChanged != null) {
                    onChanged?.call(value);
                    selected.value = false;
                  }
                },
                onOpened: () => selected.value = true,
                onCanceled: () => selected.value = false,
                itemBuilder: (BuildContext context) {
                  return items?.map((T value) {
                    return PopupMenuItem<T>(
                      value: value,
                      child: Container(
                        width: width ?? MediaQuery.sizeOf(context).width,
                        padding: const EdgeInsets.all(8),
                        child: Text(
                          itemLabelBuilder?.call(value) ?? _getDisplayLabel(value, context),
                        ),
                      ),
                    );
                  }).toList() ??
                      [];
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Row(
                    children: [
                      if (leadingIcon != null) leadingIcon!,
                      Flexible(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                itemLabelBuilder?.call(value) ?? _getDisplayLabel(value, context),
                                style: TextStyle(
                                  color: isLightMode ? AppColors.blueShade36 : AppColors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            ValueListenableBuilder(
                              valueListenable: selected,
                              builder: (context, value, _) {
                                if (selected.value) {
                                  return const Icon(Icons.arrow_drop_up);
                                } else {
                                  return const Icon(Icons.arrow_drop_down);
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getDisplayLabel(T? value, BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    if (value is String) {
      return value;
    } else if (value is Domain) {
      return value.name ?? '';
    } else if (value is Subdomain) {
      return value.name ?? '';
    } else if (value is Subtheme) {
      return value.name ?? '';
    } else if (value is DomainModel) {
      return rtl ? value.domainNameAr ?? '' : value.domainName ?? '';
    } else if (value is Properties) {
      return value.label ?? '';
    } else if (value is Visualizations) {
      return value.componentTitle ?? '';
    } else {
      return LocaleKeys.select.tr();
    }
  }
}
