import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/adapters.dart';

import '../../../../route_manager/route_imports.dart';
import '../../../../route_manager/route_imports.gr.dart';
import '../../../../translations/locale_keys.g.dart';
import '../../../config/dependancy_injection/injection_container.dart';
import '../../../features/domains/presentation/bloc/domains_bloc.dart';
import '../../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../../features/settings/presentation/bloc/setting_bloc.dart';
import '../../../utils/app_utils/app_update.dart';
import '../../../utils/app_utils/device_type.dart';
import '../../../utils/constants/asset_constants/image_constants.dart';
import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/api_cache/api_cache.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';

part 'src/app_drawer.dart';
part 'src/app_drawer_controller.dart';
part 'src/drawer_state.dart';
