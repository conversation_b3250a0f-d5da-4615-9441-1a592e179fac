import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../../../../utils/hive_utils/api_cache/api_cache.dart';
import '../../../../../utils/hive_utils/hive_keys.dart';
import '../../../../types.dart';
import '../../../indicator_card/data/models/indicator_details_response.dart';
import '../../../indicator_card/domain/repositories/indicator_card_repository_imports.dart';

part 'lazy_indicator_list_view_event.dart';
part 'lazy_indicator_list_view_state.dart';

class LazyIndicatorListViewBloc extends Bloc<LazyIndicatorListViewEvent, LazyIndicatorListViewState>
    with ApiCacheMixin {
  LazyIndicatorListViewBloc() : super(LazyIndicatorListViewVoidState()) {
    on<LazyIndicatorItemEvent>(_onLazyIndicatorItemStatus);
    on<GetIndicatorOverviewOfTypeEvent>(_getAllIndicatorDetailsOverview);
  }

  Future<void> _onLazyIndicatorItemStatus(
    LazyIndicatorItemEvent event,
    Emitter<LazyIndicatorListViewState> emit,
  ) async {
    final cacheKey = getCacheKey(HiveKeys.keyLazyItemStatusTmp);
    final cache = getCache<JSONObject>(cacheKey);
    final Map<String, String> statusMap = Map.from(cache ?? {});
    statusMap['${event.id}---${event.contentType}'] = event.status;

    await setCache(key: cacheKey, value: statusMap);
    emit(LazyIndicatorItemState(statusMap: statusMap));
  }

  Future<void> _getAllIndicatorDetailsOverview(
    GetIndicatorOverviewOfTypeEvent event,
    Emitter<LazyIndicatorListViewState> emit,
  ) async {
    try {
      if (event.ids.isEmpty) return;

      final response = await servicelocator<IndicatorCardRepository>().allIndicatorDetailsOverview(
        ids: event.ids,
        type: event.type,
      );

      if (response.isSuccess) {
        emit(GetIndicatorOverviewOfTypeState(event, response.response ?? {}));
      }
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      emit(
        GetIndicatorOverviewOfTypeErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
          event: event,
        ),
      );
    }
  }
}
