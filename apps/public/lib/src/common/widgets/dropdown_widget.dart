import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';

class DropDownWidget<T> extends StatelessWidget {
  const DropDownWidget({
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    this.label = '',
    this.height = 24,
    super.key,
    this.borderColor,
  });

  final List<DropdownMenuItem<T>>? items;
  final T? selectedValue;
  final ValueChanged<T?> onChanged;
  final String label;
  final double height;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(color: AppColors.greyShade4),
              ),
              const SizedBox(height: 4),
            ],
          ),
        DropdownButtonHideUnderline(
          child: DropdownButtonFormField2<T>(
            isExpanded: true,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide(
                  color: borderColor ?? AppColors.greyShade8,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide(
                  color: borderColor ?? AppColors.greyShade8,
                ),
              ),
            ),
            hint: const Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Item',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.greyShade4,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            buttonStyleData: ButtonStyleData(height: height),
            items: items,
            value: selectedValue,
            onChanged: onChanged,
            style: const TextStyle(
              color: AppColors.blackShade4,
            ),
            iconStyleData: const IconStyleData(
              icon: Icon(
                Icons.keyboard_arrow_down_rounded,
                color: AppColors.black,
                size: 16,
              ),
              iconSize: 14,
              iconEnabledColor: AppColors.black,
              iconDisabledColor: AppColors.grey,
            ),
            dropdownStyleData: DropdownStyleData(
              maxHeight: 200,
              width: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
              ),
              offset: const Offset(-20, 0),
              scrollbarTheme: ScrollbarThemeData(
                radius: const Radius.circular(10),
                thickness: WidgetStateProperty.all<double>(6),
                thumbVisibility: WidgetStateProperty.all<bool>(true),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
