import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class BottomSheetTopNotch extends StatelessWidget {
  const BottomSheetTopNotch({
    this.color,
    super.key,
  });

  final Color? color;

  @override
  Widget build(BuildContext context) {
    final color = this.color ?? (HiveUtilsSettings.isLightMode ? AppColors.greyNotch : AppColors.whiteShade2);

    return Align(
      child: Container(
        width: 50,
        height: 5,
        decoration: ShapeDecoration(
          color: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
        ),
      ),
    );
  }
}
