import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import 'app_box_shadow.dart';
import 'collapsable_text.dart';

class ExpandableWidget extends StatelessWidget {
  ExpandableWidget({
    this.expandedChild,
    super.key,
    this.headerChild,
    this.onChanged,
    this.iconSize,
    this.iconPadding = 6,
    this.showDivider = true,
    this.leadingIcon,
    this.title,
    this.trailingIcon,
  });

  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final String? title;
  final Widget? expandedChild;
  final Widget? headerChild;

  final ValueChanged<bool>? onChanged;
  final double? iconSize;
  final double iconPadding;
  final bool showDivider;

  final ValueNotifier<bool> controllerValue = ValueNotifier<bool>(true);
  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  Widget build(BuildContext context) {
    return ExpandableNotifier(
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
          boxShadow: isLightMode ? AppBox.shadow() : null,
        ),
        margin: EdgeInsets.zero,
        child: Column(
          children: <Widget>[
            ScrollOnExpand(
              scrollOnCollapse: false,
              child: ExpandablePanel(
                theme: const ExpandableThemeData(
                  headerAlignment: ExpandablePanelHeaderAlignment.center,
                  tapBodyToCollapse: false,
                  hasIcon: false,
                ),
                header: Builder(
                  builder: (context) {
                    final controller = ExpandableController.of(
                      context,
                      required: true,
                    )!;
                    return Material(
                      color: controller.expanded
                          ? isLightMode
                          ? AppColors.blueLightOld
                          : Colors.transparent
                          : Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          controller.toggle();
                          if (onChanged != null) {
                            onChanged?.call(controller.expanded);
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Row(
                            children: [
                              Expanded(
                                child: _buildTitleHeader(
                                  context,
                                  isLightMode,
                                  controller.expanded,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                child: Container(
                                  height: iconSize,
                                  width: iconSize,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: controller.expanded
                                          ? isLightMode
                                          ? AppColors.greyShade8
                                          : AppColors.blue
                                          : isLightMode
                                          ? AppColors.blueLightOld
                                          : AppColors.grey,
                                    ),
                                    color: controller.expanded
                                        ? isLightMode
                                        ? AppColors.greyShade8
                                        : AppColors.blueShade36
                                        : isLightMode
                                        ? AppColors.blueLightOld
                                        : Colors.transparent,
                                  ),
                                  padding: EdgeInsets.all(iconPadding),
                                  child: Icon(
                                    controller.expanded ? Icons.remove : Icons.add,
                                    color: isLightMode
                                        ? controller.expanded
                                        ? AppColors.blueLight
                                        : AppColors.white
                                        : null,
                                    size: 18,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                collapsed: const SizedBox(),
                expanded: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    if (showDivider)
                      Divider(
                        height: 1,
                        color: isLightMode ? AppColors.scaffoldBackgroundLight : AppColors.blackShade4,
                      ),
                    expandedChild ?? const SizedBox(),
                  ],
                ),
                builder: (_, collapsed, expanded) {
                  return Expandable(
                    collapsed: collapsed,
                    expanded: expanded,
                    theme: const ExpandableThemeData(crossFadePoint: 0),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleHeader(BuildContext context, bool isLightMode, bool isExpanded) {
    if (headerChild != null) {
      return headerChild!;
    }

    final controller = ExpandableController.of(
      context,
      required: true,
    )!;

    return Row(
      children: [
        if (leadingIcon != null) leadingIcon! else const SizedBox(width: 8),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: CollapsableText(
              text: title ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: isLightMode
                    ? controller.expanded
                    ? AppColors.white
                    : AppColors.grey
                    : AppColors.white,
              ),
            ),
          ),
        ),
        if (trailingIcon != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: trailingIcon,
          ),
      ],
    );
  }
}
