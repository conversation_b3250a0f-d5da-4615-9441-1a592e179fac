import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/asset_constants/image_constants.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class AppCheckBox extends StatefulWidget {
  const AppCheckBox({super.key, this.isChecked});
  final bool? isChecked;

  @override
  State<AppCheckBox> createState() => _AppCheckBoxState();
}

class _AppCheckBoxState extends State<AppCheckBox> {
  bool isChecked = false;
 final bool isLightMode =
        HiveUtilsSettings.isLightMode;
  @override
  void initState() {
    super.initState();
    isChecked = widget.isChecked??false;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onTap: (){
      //   setState(() {
      //     isChecked=!isChecked;
      //   });
      // },
      child: Container(
        width: 15,
        height: 15,
        padding: const EdgeInsets.all(3.33),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: isChecked ?AppColors.blueShade22:AppColors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4),side: BorderSide(color:  isLightMode ? AppColors.greyShade1 : AppColors.blueShade22),
          ),
        ),
        child: isChecked ? SvgPicture.asset(
          AppImages.icCheck ,
          colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
        ):null,
      ),
    );
  }
}
