import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class CountWidget extends StatelessWidget {
  const CountWidget({
    required this.count,
    super.key,
  });

  final int count;

  @override
  Widget build(BuildContext context) {
    final bool isLightTheme = HiveUtilsSettings.isLightMode;
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: isLightTheme ? AppColors.blueGreyShade2 : AppColors.blueGreyShade2,
        shape: BoxShape.circle,
      ),
      padding: const EdgeInsets.all(8),
      child: Text(
        count.toString(),
        style: const TextStyle(
          color: AppColors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
