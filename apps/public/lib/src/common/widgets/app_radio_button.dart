import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/constants/asset_constants/image_constants.dart';
import '../../utils/constants/color_constants/color_constants.dart';

class AppRadioButton extends StatefulWidget {
  const AppRadioButton({super.key, this.isChecked});
  final bool? isChecked;

  @override
  State<AppRadioButton> createState() => _AppRadioButtonState();
}

class _AppRadioButtonState extends State<AppRadioButton> {
  bool isChecked = false;
  @override
  void initState() {
    super.initState();
    isChecked = widget.isChecked??false;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onTap: (){
      //   setState(() {
      //     isChecked=!isChecked;
      //   });
      // },
      child: SizedBox(
        width: 18,
        height: 18,
        child: SvgPicture.asset(
          isChecked ? AppImages.icRadioOn : AppImages.icRadioOff,
          colorFilter: isChecked ? const ColorFilter.mode(
              AppColors.blueShade22,
              BlendMode.srcIn,
            ) : null,
        ),
      ),
    );
  }
}
