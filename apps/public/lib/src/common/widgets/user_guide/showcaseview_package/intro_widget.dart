import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../route_manager/route_imports.gr.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../../../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../../../features/products/presentation/bloc/products_bloc.dart';
import '../../../../utils/app_utils/device_type.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../utils/styles/app_text_styles.dart';
import 'showcaseview.dart';

class IntroWidget extends StatefulWidget {
  const IntroWidget({
    required this.title,
    required this.description,
    required this.stepIndex,
    required this.child,
    required this.stepKey,
    required this.totalSteps,
    this.position = TooltipPosition.bottom,
    this.arrowPadding,
    this.arrowAlignment,
    super.key,
    this.isDownArrow = false,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.targetPadding,
    this.targetBorderRadius = 150,
    this.onNext,
    this.onPrevious,
  });

  final bool isDownArrow;
  final String title;
  final String description;
  final EdgeInsetsGeometry? arrowPadding;
  final AlignmentGeometry? arrowAlignment;
  final int stepIndex;
  final int totalSteps;
  final CrossAxisAlignment crossAxisAlignment;
  final Widget child;
  final GlobalKey stepKey;
  final TooltipPosition position;
  final EdgeInsets? targetPadding;
  final double targetBorderRadius;
  final FutureOr<void> Function()? onNext;
  final FutureOr<void> Function()? onPrevious;

  @override
  State<IntroWidget> createState() => _IntroWidgetState();
}

class _IntroWidgetState extends State<IntroWidget> {
  bool isScrolling = false;

  @override
  void didUpdateWidget(covariant IntroWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.title != oldWidget.title || widget.description != oldWidget.description) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.sizeOf(context);
    final isLightMode = HiveUtilsSettings.isLightMode;
    return Showcase.withWidget(
      key: widget.stepKey,
      height: size.height,
      width: size.width - 30,
      targetPadding: widget.targetPadding ?? EdgeInsets.zero,
      targetShapeBorder: const CircleBorder(),
      targetBorderRadius: BorderRadius.all(
        Radius.circular(widget.targetBorderRadius),
      ),
      onTargetClick: () => onNextPressed(context),
      onBarrierClick: () => onNextPressed(context),
      disableMovingAnimation: true,
      disposeOnTap: false,
      tooltipPosition: widget.position,
      container: AnimatedOpacity(
        opacity: isScrolling ? 0 : 1,
        duration: const Duration(milliseconds: 200),
        child: Column(
          crossAxisAlignment: widget.crossAxisAlignment,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Visibility(
              visible: !widget.isDownArrow,
              child: Container(
                padding: widget.arrowPadding,
                margin: const EdgeInsets.only(bottom: 10),
                alignment: widget.arrowAlignment,
                child: SvgPicture.asset(AppImages.icUpArrow),
              ),
            ),
            Container(
              width: MediaQuery.sizeOf(context).width * .91,
              constraints: const BoxConstraints(
                // maxHeight: MediaQuery.sizeOf(context).height*.25,
                minHeight: 10,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            widget.title,
                            style: AppTextStyles.s16w6cWhite,
                          ),
                        ),
                        InkWell(
                          // constraints: const BoxConstraints(),
                          // padding: EdgeInsets.zero,
                          child: const Icon(
                            Icons.close_rounded,
                            color: AppColors.white,
                            size: 18,
                          ),
                          onTap: () async {
                            ShowCaseWidget.of(context).dismiss();
                            preStatusUpdate(context);
                            await HiveUtilsSettings.saveUserGuideStatus();
                            if (context.mounted) _navToHome(context);
                          },
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        widget.description,
                        maxLines: 8,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.s14w4cBlue.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: 60,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                height: 30,
                                child: InkWell(
                                  onTap: onPreviousPressed,
                                  child: RotatedBox(
                                    quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                                    child: SvgPicture.asset(
                                      AppImages.icPrevWhiteLine,
                                      colorFilter: ColorFilter.mode(
                                        widget.stepIndex == 1
                                            ? AppColors.white.withAlpha((0.4 * 255).toInt())
                                            : AppColors.white,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (widget.stepIndex != widget.totalSteps)
                                SizedBox(
                                  height: 30,
                                  child: InkWell(
                                    onTap: () => onNextPressed(context),
                                    child: RotatedBox(
                                      quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                                      child: SvgPicture.asset(
                                        AppImages.icNextWhiteLine,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Text(
                          '${widget.stepIndex} ${LocaleKeys.of.tr()} ${widget.totalSteps}',
                          style: AppTextStyles.s14w4cBlue.copyWith(color: AppColors.white),
                        ),
                      ],
                    ),
                    if (widget.stepIndex == widget.totalSteps)
                      Padding(
                        padding: const EdgeInsets.only(top: 8, bottom: 8),
                        child: SizedBox(
                          height: 30,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              actionButton(context, false),
                              actionButton(context, true),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Visibility(
              visible: widget.isDownArrow,
              child: Container(
                padding: widget.arrowPadding,
                margin: const EdgeInsets.only(top: 10),
                alignment: widget.arrowAlignment,
                child: SvgPicture.asset(AppImages.icDownArrow),
              ),
            ),
          ],
        ),
      ),
      child: widget.child,
    );
  }

  TextButton actionButton(BuildContext context, bool isDoneButton) {
    return TextButton(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        backgroundColor: isDoneButton ? AppColors.white : Colors.transparent,
        shape: RoundedRectangleBorder(
          side: const BorderSide(color: AppColors.white),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      onPressed: () async {
        ShowCaseWidget.of(context).dismiss();
        preStatusUpdate(context);
        await HiveUtilsSettings.saveUserGuideStatus();
        if (!context.mounted) return;
        _navToHome(context);
        if (!isDoneButton) {
          unawaited(AutoRouter.of(context).push(const UserGuideScreenRoute()));
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!isDoneButton)
            const Icon(
              Icons.chevron_left_rounded,
              color: AppColors.white,
              size: 20,
            ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: isDoneButton ? 30 : 0),
            child: Text(
              isDoneButton ? LocaleKeys.done.tr() : LocaleKeys.backToUserGuide.tr(),
              style: AppTextStyles.s14w4cBlue.copyWith(
                color: isDoneButton ? AppColors.blueLight : AppColors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> onPreviousPressed() async {
    if (widget.stepIndex != 1) {
      if (widget.onPrevious != null) {
        setState(() {
          isScrolling = true;
        });
        await widget.onPrevious!.call();
        setState(() {
          isScrolling = false;
        });
      }
      if (mounted) ShowCaseWidget.of(context).previous();
    }
  }

  Future<void> onNextPressed(BuildContext context) async {
    if (widget.stepIndex != widget.totalSteps) {
      if (widget.onNext != null) {
        setState(() {
          isScrolling = true;
        });
        await widget.onNext!.call();

        setState(() {
          isScrolling = false;
        });
      }
      if (context.mounted) ShowCaseWidget.of(context).next();
    } else {
      ShowCaseWidget.of(context).dismiss();
      await HiveUtilsSettings.saveUserGuideStatus();
    }
  }

  void _navToHome(BuildContext context) {
    AutoRouter.of(context).popUntilRouteWithName(HomeNavigationRoute.name);
    // AutoRouter.of(context).popUntilRoot();
    // if (AutoRouter.of(context).current.name != HomeNavigationRoute.name) {
    //   AutoRouter.of(context).replace(HomeNavigationRoute());
    // }
    context.read<HomeBloc>().add(NavToHomeEvent());
  }

  void preStatusUpdate(BuildContext context) {
    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Products) {
      context.read<ProductsBloc>().add(ProductsResetEvent());
    }
  }
}
