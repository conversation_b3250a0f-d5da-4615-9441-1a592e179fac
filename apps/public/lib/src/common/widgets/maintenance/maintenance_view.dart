import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../utils/constants/asset_constants/image_constants.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../utils/remote_config_utils/maintenance_status.dart';

class MaintenanceView extends StatelessWidget {
  const MaintenanceView({
    required this.data,
    super.key,
  });

  final MaintenanceStatus data;

  @override
  Widget build(BuildContext context) {
    final String title;
    final String description;
    final String from;
    final String to;

    if (HiveUtilsSettings.isLanguageArabic) {
      title = data.titleAr ?? '';
      description = data.descriptionAr ?? '';
      from = data.fromAr ?? '';
      to = data.toAr ?? '';
    } else {
      title = data.titleEn ?? '';
      description = data.descriptionEn ?? '';
      from = data.fromEn ?? '';
      to = data.toEn ?? '';
    }

    return Directionality(
      textDirection: HiveUtilsSettings.isLanguageArabic ? TextDirection.rtl : TextDirection.ltr,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            AppImages.maintenance,
          ),
          const SizedBox(height: 32),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            description,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            '$from - $to',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
