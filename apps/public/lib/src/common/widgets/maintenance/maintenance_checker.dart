import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../utils/remote_config_utils/maintenance_status.dart';
import '../primary_button.dart';
import 'maintenance_view.dart';

typedef MaintenanceCheckPredicate = bool Function(MaintenanceStatus);

class MaintenanceChecker extends StatefulWidget {
  const MaintenanceChecker({
    required this.checkPredicate,
    required this.child,
    super.key,
  });

  /// If this response is true. Maintenance view will be shown
  final MaintenanceCheckPredicate checkPredicate;

  final Widget child;

  @override
  State<MaintenanceChecker> createState() => _MaintenanceCheckerState();
}

class _MaintenanceCheckerState extends State<MaintenanceChecker> {
  MaintenanceStatus? _maintenance;

  bool get _isUnderMaintenance {
    final maintenance = _maintenance;
    if (maintenance == null) return false;
    return maintenance.active && widget.checkPredicate.call(maintenance);
  }

  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(const CheckMaintenanceEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (previous, current) => current is CheckMaintenanceSuccessState,
      listener: _maintenanceListener,
      child: Center(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 600),
          switchInCurve: Curves.fastEaseInToSlowEaseOut,
          switchOutCurve: Curves.fastEaseInToSlowEaseOut,
          child: _isUnderMaintenance
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: _MaintenanceObserverOnLifecycle(
                    data: _maintenance!,
                  ),
                )
              : widget.child,
        ),
      ),
    );
  }

  void _maintenanceListener(BuildContext context, HomeState state) {
    if (state is! CheckMaintenanceSuccessState) return;
    setState(() => _maintenance = state.data);
  }
}

/// Widget to check maintenance when widget life cycle changes.
///
/// Applying this directly in `MaintenanceChecker` will increase cost if wrapped on multiple widgets.`
class _MaintenanceObserverOnLifecycle extends StatefulWidget {
  const _MaintenanceObserverOnLifecycle({
    required this.data,
  });

  final MaintenanceStatus data;

  @override
  State<_MaintenanceObserverOnLifecycle> createState() => _MaintenanceObserverOnLifecycleState();
}

class _MaintenanceObserverOnLifecycleState extends State<_MaintenanceObserverOnLifecycle> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state != AppLifecycleState.resumed) return;
    context.read<HomeBloc>().add(const CheckMaintenanceEvent());
  }

  @override
  Widget build(BuildContext context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          MaintenanceView(
            data: widget.data,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 24),
            child: PrimaryButton(
              text: HiveUtilsSettings.isLanguageEnglish ? 'Refresh' : 'تحديث',
              onTap: () => context.read<HomeBloc>().add(const CheckMaintenanceEvent()),
              backgroundColor: AppColors.blueLightOld,
              color: Colors.white,
            ),
          ),
        ],
      );

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
