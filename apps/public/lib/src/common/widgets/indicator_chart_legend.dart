import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';
import '../functions/indicator_date_setting.dart';
import 'chart_legend.dart';
import 'indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'indicator_card/data/models/indicator_details_response.dart';

class IndicatorChartLegend extends StatelessWidget {
  IndicatorChartLegend({
    this.indicatorDetails,
    this.legendList,
    this.index = 0,
    this.isForecastOn = false,
    bool? isLightMode,
    super.key,
  }) : assert(
  indicatorDetails != null || legendList != null,
  'indicatorDetails or legendList is required',
  ) {
    _isLightMode = isLightMode ?? HiveUtilsSettings.isLightMode;
  }

  final IndicatorDetailsResponse? indicatorDetails;
  final List<String>? legendList;
  late final bool _isLightMode;
  final int index;
  final bool isForecastOn;

  @override
  Widget build(BuildContext context) {
    List<String> filterString = [];

    final List<Color> colorSet =
    _isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    if (legendList != null) {
      filterString = legendList!;
    } else if (indicatorDetails != null) {
      final IndicatorType? indicatorType = indicatorDetails?.getIndicatorType();

      if (indicatorType == IndicatorType.scenarioDriver ||
          indicatorType == IndicatorType.forecast) {
        final VisualizationsMeta? visualizationsMeta = indicatorDetails
            ?.indicatorVisualizations?.visualizationsMeta
            ?.elementAt(index);

        if (indicatorType == IndicatorType.scenarioDriver &&
            (visualizationsMeta?.type ?? '').contains('tree')) {
          return const SizedBox();
        }

        List<SeriesMeta>? seriesMeta = visualizationsMeta?.seriesMeta;
        if (seriesMeta == null) {
          return const SizedBox();
        }

        final legends = <ChartLegend>[];
        int colorIndex = 0;

        if (!isForecastOn) {
          seriesMeta = seriesMeta.where((e) => !(e.id?.endsWith('forecast') ?? false)).toList();
        }

        for (final meta in seriesMeta) {
          String label = meta.label.toString();
          Color color = colorSet[colorIndex % colorSet.length];
          colorIndex++;
          bool isForecast = false;
          if (meta.id?.endsWith('forecast') ?? false) {
            if (!isForecastOn) continue;

            isForecast = true;
            try {
              final b = label.replaceAll(' Forecast', '');
              color = legends
                  .firstWhere(
                    (e) => b == e.label.replaceAll('Forecast', ''),
              )
                  .color;
              label += ' -forecast';
              colorIndex--;
            } catch (e) {
              // ignore: bad element
            }
          }

          legends.add(
            ChartLegend(
              label: label,
              color: color,
              isLightMode: _isLightMode,
              isForecast: isForecast,
            ),
          );
        }

        return Wrap(
          spacing: 12,
          runSpacing: 8,
          children: legends,
        );
      } else {
        dynamic filterPanel;

        if (indicatorType == IndicatorType.insightDiscovery) {
          filterPanel = (indicatorDetails?.visualizations ?? [])
              .where(
                (element) =>
            element.id == indicatorDetails?.defaultVisualisation,
          )
              .firstOrNull
              ?.filterPanel;
        } else {
          filterPanel = indicatorDetails?.filterPanel;
        }

        bool hasFilter = false;
        try {
          hasFilter = bool.parse('${filterPanel ?? 'false'}');
        } catch (e) {
          hasFilter = true;
        }

        if (hasFilter) {
          final List<Properties> properties =
          IndicatorDateSetting.removeDuplicates(
            filterPanel?.properties as List<Properties>,
          );

          final String defaultVal =
              '${(properties.first.path == 'OBS_DT' ? properties[1] : properties[0]).defaultVal}';

          filterString.add(
            defaultVal
                .toLowerCase()
                .split(' ')
                .map(
                  (e) => toBeginningOfSentenceCase(e),
            )
                .join(' '),
          );
        } else {
          filterString.add(
            indicatorDetails?.componentTitle ?? '-',
          );
        }
      }
    }

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: List.generate(
        filterString.length,
            (i) => ChartLegend(
          label: filterString[i],
          color: colorSet[i % colorSet.length],
          isLightMode: _isLightMode,
        ),
      ).toList(),
    );
  }
}
