import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/dependancy_injection/injection_container.dart';
import '../../features/domains/domain/repositories/domains_repository_imports.dart';
import '../functions/domain_id_mapper.dart';
import 'app_shimmer.dart';

class _DomainIconUrlCubit extends Cubit<String?> {
  _DomainIconUrlCubit() : super(null);

  Future<void> loadUrl(String idOrName) async {
    try {
      final response = await servicelocator<DomainsRepository>().getDomainIconsList();
      final domains = response.response;
      if (!response.isSuccess || domains == null) {
        throw Exception('api-failed-or-no-domains-found');
      }

      int index;
      final bool valueIsId = num.tryParse(idOrName) != null;
      if (valueIsId) {
        final domainId = getMappedDomainId(idOrName);
        index = domains.indexWhere(
          (element) => element.domainId == domainId,
        );
      } else {
        index = domains.indexWhere(
          (element) => [element.domainNameAr, element.domainName].any((e) => e == idOrName),
        );
      }

      if (index == -1) {
        throw Exception('domain-not-found');
      }

      if (!isClosed) {
        emit(domains[index].domainIcon);
      }
    } on Exception catch (e) {
      Completer<void>().completeError(e);
      return emit('error');
    }
  }
}

class DomainIcon extends StatefulWidget {
  const DomainIcon({
    required this.domainIdOrName,
    super.key,
    this.color,
    this.size = 18,
    this.padding = EdgeInsets.zero,
    this.errorBuilder,
  });

  final String? domainIdOrName;
  final Color? color;
  final double size;
  final EdgeInsets padding;
  final WidgetBuilder? errorBuilder;

  @override
  State<DomainIcon> createState() => _DomainIconState();
}

class _DomainIconState extends State<DomainIcon> {
  late final _DomainIconUrlCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = _DomainIconUrlCubit();

    final idOrName = widget.domainIdOrName;
    if (idOrName != null) {
      _cubit.loadUrl(idOrName);
    }
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final domainIdOrName = widget.domainIdOrName;
    if (domainIdOrName == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: widget.padding,
      child: BlocBuilder<_DomainIconUrlCubit, String?>(
        bloc: _cubit,
        builder: (context, url) {
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 100),
            child: Builder(
              builder: (context) {
                if (url == null) {
                  return AppShimmer(
                    width: widget.size,
                    height: widget.size,
                  );
                }

                if (url == 'error') {
                  if (widget.errorBuilder != null) {
                    return SizedBox.square(
                      dimension: widget.size,
                      child: widget.errorBuilder!(context),
                    );
                  }
                  return const SizedBox.shrink();
                }

                return SizedBox.square(
                  dimension: widget.size,
                  child: SvgPicture.network(
                    url,
                    fit: BoxFit.fill,
                    colorFilter: widget.color == null
                        ? null
                        : ColorFilter.mode(
                            widget.color!,
                            BlendMode.srcIn,
                          ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
