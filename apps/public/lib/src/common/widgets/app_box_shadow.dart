import 'package:flutter/material.dart';

import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/hive_utils/hive_utils_settings.dart';

class AppBox {
  static List<BoxShadow> shadow() {
    if (HiveUtilsSettings.isLightMode) {
      return [
        const BoxShadow(
          color: AppColors.greyShade13,
          blurRadius: 29,
          offset: Offset(0, 4),
        ),
      ];
    } else {
      return [
        const BoxShadow(
          color: AppColors.blackShade1,
          blurRadius: 29,
          offset: Offset(0, 4),
        ),
      ];
    }
  }
}
