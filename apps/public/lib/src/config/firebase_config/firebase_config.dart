import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

import '../../utils/app_utils/app_log.dart';
import '../../utils/hive_utils/hive_utils_persistent.dart';

class FirebaseConfig {
  FirebaseConfig._();

  static FirebaseConfig instance = FirebaseConfig._();

  static FirebaseAnalytics? get _firebaseAnalytics => FirebaseAnalytics.instance;

  Future<void> logScreenToAnalytics(
      String screenName,
      String screenClass, {
        Map<String, Object>? extra,
      }) async {
    AppLog.info('FirebaseAnalyticsConfig.logScreen: $screenName');
    try {
      final deviceUuid = HiveUtilsPersistent.getUuid();
      final params = <String, Object>{
        'device_uuid': deviceUuid,
        if (extra != null) ...extra,
      };

      await _firebaseAnalytics?.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: params,
      );
    } catch (e, s) {
      debugPrint('FirebaseConfig.setScreenToAnalytics: $e');
      Completer<dynamic>().completeError(e, s);
    }
  }
}
