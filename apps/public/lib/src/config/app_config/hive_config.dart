import 'dart:async';
import 'dart:io';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../../utils/hive_utils/hive_keys.dart';
import 'secret.dart';

class HiveConfig {
  static Future<void> initConfig() async {
    try {
    await Hive.initFlutter();

    final Directory directory = await getApplicationSupportDirectory();

    Hive.init(directory.path);

    final HiveAesCipher hiveAesCipher = HiveAesCipher(Secret.hiveEncryptionKey.split('').map((e) => int.parse(e)).toList());

    await Hive.openBox<dynamic>(HiveKeys.boxSettings, encryptionCipher: hiveAesCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxApiCache, encryptionCipher: hiveAesCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxAuth, encryptionCipher: hive<PERSON>esCipher);
    await Hive.openBox<dynamic>(HiveKeys.boxPersistent, encryptionCipher: hiveAesCipher);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
