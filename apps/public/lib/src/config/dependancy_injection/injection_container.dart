import 'package:get_it/get_it.dart';

import '../../../route_manager/route_imports.dart';
import '../../common/widgets/indicator_card/data/repositories/indicator_card_repository_impl.dart';
import '../../common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import '../../common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import '../../common/widgets/lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';
import '../../features/chat_with_sme/data/repositories/chat_with_sme_repo_implementation.dart';
import '../../features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import '../../features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import '../../features/details_page/compare/data/repositories/compare_details_repo_impl.dart';
import '../../features/details_page/compare/domain/repositories/compare_details_repository.dart';
import '../../features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import '../../features/details_page/compute/data/repositories/compute_details_repo_impl.dart';
import '../../features/details_page/compute/domain/repositories/compute_details_repository.dart';
import '../../features/details_page/compute/presentation/%20bloc/compute_details_bloc.dart';
import '../../features/details_page/official_experimental/presentation/bloc/official_experimental_details_bloc.dart';
import '../../features/domains/data/repositories/domains_repo_impl.dart';
import '../../features/domains/domain/repositories/domains_repository_imports.dart';
import '../../features/domains/presentation/bloc/domains_bloc.dart';
import '../../features/drawer_items/about_this_app/data/repositories/about_repo_impl.dart';
import '../../features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import '../../features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import '../../features/drawer_items/glossary/data/repositories/glossary_repo_implementation.dart';
import '../../features/drawer_items/glossary/domain/repositories/glossary_repository_imports.dart';
import '../../features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import '../../features/drawer_items/terms_and_conditions/data/repositories/t_and_c_repo_impl.dart';
import '../../features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';
import '../../features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import '../../features/home/<USER>/repositories/home_repo_impl.dart';
import '../../features/home/<USER>/repositories/home_repository_imports.dart';
import '../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../features/products/data/repositories/products_repo_impl.dart';
import '../../features/products/domain/repositories/products_repository_imports.dart';
import '../../features/products/presentation/bloc/products_bloc.dart';
import '../../features/search/data/repositories/search_repo_impl.dart';
import '../../features/search/domain/repositories/search_repository_imports.dart';
import '../../features/search/presentation/bloc/search_bloc.dart';
import '../../features/settings/data/repositories/settings/setting_repository_impl.dart';
import '../../features/settings/domain/repositories/settings/setting_repository_imports.dart';
import '../../features/settings/presentation/bloc/setting_bloc.dart';
import '../../services/http_service_impl.dart';

//initialise get_it //
final servicelocator = GetIt.instance;

//dependacy container//
Future<void> getInit() async {
  //register bloc//
  servicelocator
    ..registerSingleton<AppRouter>(AppRouter())
    ..registerLazySingleton<HttpServiceRequests>(HttpServiceRequests.new)
    ..registerFactory<DomainsBloc>(() => DomainsBloc())
    ..registerLazySingleton<DomainsRepository>(DomainsRepositoryImpl.new)
    ..registerFactory<SearchBloc>(() => SearchBloc())
    ..registerLazySingleton<SearchRepository>(SearchRepositoryImpl.new)
    ..registerFactory<ChatWithSmeBloc>(() => ChatWithSmeBloc())
    ..registerLazySingleton<ChatWithSmeRepository>(ChatWithSmeRepoImplementation.new)
    ..registerFactory<HomeBloc>(() => HomeBloc(servicelocator()))
    ..registerLazySingleton<HomeRepository>(HomeRepositoryImpl.new)
    ..registerFactory<IndicatorCardBloc>(IndicatorCardBloc.new)
    ..registerLazySingleton<IndicatorCardRepository>(IndicatorCardImpl.new)
    ..registerFactory<AboutAppBloc>(() => AboutAppBloc(servicelocator()))
    ..registerLazySingleton<AboutAppRepository>(AboutAppRepositoryImpl.new)
    ..registerFactory<TAndCBloc>(() => TAndCBloc(servicelocator()))
    ..registerLazySingleton<TAndCRepository>(TAndCRepositoryImpl.new)
    ..registerFactory<ProductsBloc>(ProductsBloc.new)
    ..registerLazySingleton<ProductsRepository>(ProductsRepositoryImpl.new)
    ..registerFactory<GlossaryBloc>(() => GlossaryBloc(servicelocator()))
    ..registerLazySingleton<GlossaryRepository>(GlossaryRepoImplementation.new)
    ..registerFactory<LazyIndicatorListViewBloc>(LazyIndicatorListViewBloc.new)
    ..registerFactory<CompareDetailsBloc>(CompareDetailsBloc.new)
    ..registerLazySingleton<CompareDetailsRepository>(CompareDetailsRepoImpl.new)
    ..registerFactory<OfficialExperimentalDetailsBloc>(OfficialExperimentalDetailsBloc.new)
    ..registerFactory<ComputeDetailsBloc>(ComputeDetailsBloc.new)
    ..registerLazySingleton<ComputeDetailsRepository>(ComputeDetailsRepositoryImpl.new)
    ..registerFactory<SettingBloc>(SettingBloc.new)
    ..registerLazySingleton<SettingRepository>(SettingRepositoryImpl.new);
}