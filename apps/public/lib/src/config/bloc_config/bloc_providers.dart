//Define global blocs as Multibloc provider list

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import '../../common/widgets/lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';
import '../../features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import '../../features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import '../../features/details_page/compute/presentation/%20bloc/compute_details_bloc.dart';
import '../../features/details_page/official_experimental/presentation/bloc/official_experimental_details_bloc.dart';
import '../../features/domains/presentation/bloc/domains_bloc.dart';
import '../../features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import '../../features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import '../../features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import '../../features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../features/products/presentation/bloc/products_bloc.dart';
import '../../features/search/presentation/bloc/search_bloc.dart';
import '../../features/settings/presentation/bloc/setting_bloc.dart';
import '../dependancy_injection/injection_container.dart';

final providers = [
  BlocProvider<SearchBloc>(
    create: (context) => servicelocator<SearchBloc>(),
  ),
  BlocProvider<AboutAppBloc>(
    create: (context) => servicelocator<AboutAppBloc>(),
  ),
  BlocProvider<TAndCBloc>(
    create: (context) =>
        servicelocator<TAndCBloc>(),//..add(const TAndCLoadingEvent()),
  ),
  BlocProvider<HomeBloc>(
    create: (context) => servicelocator<HomeBloc>(),
  ),
  BlocProvider<IndicatorCardBloc>(
    create: (context) => servicelocator<IndicatorCardBloc>(),
  ),
  BlocProvider<SettingBloc>(
    create: (context) => servicelocator<SettingBloc>(),
      // ..add(const ProfilePicSelectionEvent())
      // ..add(const DefaultSettingLoadEvent()),
  ),

  BlocProvider<DomainsBloc>(
    create: (context) =>
        servicelocator<DomainsBloc>(), //..add(const DomainsInitEvent()),
  ),
  BlocProvider<ChatWithSmeBloc>(
      create: (context) => servicelocator<ChatWithSmeBloc>()),
  BlocProvider<ProductsBloc>(
    create: (context) =>
        servicelocator<ProductsBloc>(), //..add(const ProductsLoadEvent()),
  ),
  BlocProvider<LazyIndicatorListViewBloc>(
    create: (context) => servicelocator<LazyIndicatorListViewBloc>(),
  ),
  BlocProvider<GlossaryBloc>(
    create: (context) => servicelocator<GlossaryBloc>(),
  ),
  BlocProvider<CompareDetailsBloc>(
    create: (context) => servicelocator<CompareDetailsBloc>(),
  ),
  BlocProvider<OfficialExperimentalDetailsBloc>(
    create: (context) => servicelocator<OfficialExperimentalDetailsBloc>(),
  ),
  BlocProvider<ComputeDetailsBloc>(
    create: (context) => servicelocator<ComputeDetailsBloc>(),
  ),
];
