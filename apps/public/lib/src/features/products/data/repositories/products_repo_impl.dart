import 'dart:async';

import '../../../../common/models/response_models/repo_response.dart';
import '../data_sources/products_end_points.dart';
import '../models/product_dashboard.dart';
import '../models/scad_api/publication_category_response.dart';
import '../models/scad_api/publication_domain_response.dart';
import '../models/scad_api/publication_theme_response.dart';
import '../models/scad_api/publication_year_response.dart';
import '../models/scad_api/publications_response.dart';
import '../models/scad_api/web_report_response.dart';
import '../../domain/repositories/products_repository_imports.dart';
import '../../../../services/http_service_impl.dart';
import '../../../../utils/extentions/string_extentions.dart';

class ProductsRepositoryImpl extends ProductsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<List<TableauDashboardResponseItem>>> getDashboards() async {
    final endpoint = ProductsEndPoints.getDashboards;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache<List<TableauDashboardResponseItem>>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => TableauDashboardResponseItem.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<PublicationsResponse>> scadApiPublications({
    required int pageNo,
    required int pageSize,
    List<String> paramList = const [],
  }) async {
    final endpoint = '${ProductsEndPoints.publication}&page=$pageNo&pageSize=$pageSize&${paramList.join('&')}';
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => PublicationsResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<WebReports>> scadApiWebReports({
    required int pageNo,
    required int pageSize,
    String query = '',
  }) async {
    final endpoint = '${ProductsEndPoints.webReport}&page=$pageNo&pageSize=$pageSize&search=$query';
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => WebReports.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<PublicationDomainResponse>> scadApiPublicationsDomains() async {
    final endpoint = ProductsEndPoints.publicationDomains;
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => PublicationDomainResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<PublicationThemeResponse>> scadApiPublicationsThemes({
    required String scadDomainId,
  }) async {
    final endpoint = ProductsEndPoints.publicationThemes.setUrlParams(
      {'scad_topic_id': scadDomainId},
    );

    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => PublicationThemeResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<PublicationCategoryResponse>> scadApiPublicationsCategories({
    required String scadThemeId,
  }) async {
    final endpoint = ProductsEndPoints.publicationCategories.setUrlParams(
      {'scad_theme_id': scadThemeId},
    );

    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => PublicationCategoryResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<PublicationYearResponse>> scadApiPublicationsYears() async {
    final endpoint = ProductsEndPoints.publicationYears;
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => PublicationYearResponse.fromJson(json),
    );
  }
}
