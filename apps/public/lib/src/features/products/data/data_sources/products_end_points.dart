import '../../../../config/app_config/api_config.dart';

class ProductsEndPoints extends ApiConfig {
  static String ifpPath = ApiConfig.ifpApiPath;
  static String appPath = ApiConfig.appApiPath;
  static String scadPath = ApiConfig.scadApiPath;

  static String getDashboards = '$appPath/user/product/list/';
  // static String getDashboards = '$ifpPath/content-type/analytical-apps/mobile-dashboards';

  static String getAnalyticalApps = '$ifpPath/content-type/analytical-apps/v2';
  static String getNewsLetters = '$ifpPath/content-type/newsletter-publications/';

  static String getProductAccess = '$ifpPath/content-type/access-check';

  // static String publicationDomains = '$scadPath/o/headless-admin-taxonomy/v1.0/taxonomy-vocabularies/476702/taxonomy-categories?fields=name,id,numberOfTaxonomyCategories';
  // static String publicationThemes = '$scadPath/o/headless-admin-taxonomy/v1.0/taxonomy-categories/{{scad_topic_id}}/taxonomy-categories?fields=name,id';
  // static String publicationCategories = '$scadPath/o/headless-admin-taxonomy/v1.0/taxonomy-categories/{{scad_theme_id}}/taxonomy-categories?fields=name,id';
  // static String publicationYears = '$scadPath/o/headless-admin-taxonomy/v1.0/taxonomy-vocabularies/109974/taxonomy-categories?fields=name,id';
  //
  // static String publication =
  //     '$scadPath/o/headless-delivery/v1.0/content-structures/108409/structured-contents?fields=id,title,contentFields';
  // static String webReport =
  //     '$scadPath/o/headless-delivery/v1.0/content-structures/1625810/structured-contents?fields=id,title,contentFields';

  static String publication = '$appPath/content-structures/108409?fields=id,title,contentFields';
  static String webReport = '$appPath/content-structures/1625810?fields=id,title,contentFields';

  static String publicationDomains = '$appPath/taxonomy/taxonomy-vocabularies/476702?fields=name,id,numberOfTaxonomyCategories';
  static String publicationThemes = '$appPath/taxonomy/taxonomy-categories/{{scad_topic_id}}?fields=name,id';
  static String publicationCategories = '$appPath/taxonomy/taxonomy-categories/{{scad_theme_id}}?fields=name,id';
  static String publicationYears = '$appPath/taxonomy/taxonomy-vocabularies/109974?fields=name,id';
}
