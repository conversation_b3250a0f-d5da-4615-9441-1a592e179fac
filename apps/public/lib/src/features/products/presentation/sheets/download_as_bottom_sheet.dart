import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../route_manager/route_imports.gr.dart';
import '../../../../common/widgets/bottom_sheet_top_notch.dart';
import '../../../../utils/app_utils/app_message.dart';
import '../../../../utils/app_utils/downloadHelper.dart';
import '../../../../utils/app_utils/download_service.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';

enum DownloadFileType {
  pdf(AppImages.icPdf, AppColors.redC7525C),
  excel(AppImages.icExcel, AppColors.green6BAD49),
  other(AppImages.icDocument, AppColors.greyShade6);

  const DownloadFileType(this.assetPath, this.color);

  final String assetPath;
  final Color color;

  static DownloadFileType fromExtension(String extension) {
    if (extension == 'pdf') return pdf;
    if (['xlsx', 'xls', 'xlsm'].contains(extension)) return excel;
    return other;
  }

  static DownloadFileType fromFileName(String filename) {
    final extension = filename.split('.').lastOrNull;
    if (extension == null) return other;
    return fromExtension(extension);
  }
}

class DownloadItem {
  DownloadItem({
    required this.fileName,
    required this.downloadUrl,
    required this.type,
  });

  final String fileName;
  final String downloadUrl;
  final DownloadFileType type;
}

class DownloadAsBottomSheet extends StatelessWidget {
  DownloadAsBottomSheet._(
    this.items, {
    this.folder,
    this.isAuthenticated = true,
    this.isPreview = false,
  });

  static Future<dynamic> show(
    BuildContext context, {
    required Iterable<DownloadItem> items,
    String? folder,
    bool isAuthenticated = true,
    bool isPreview = false,
  }) {
    return showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (context) => DownloadAsBottomSheet._(
        items,
        folder: folder,
        isAuthenticated: isAuthenticated,
        isPreview: isPreview,
      ),
    );
  }

  final String? folder;
  final bool isAuthenticated;
  final Iterable<DownloadItem> items;
  final bool isPreview;

  final _isTermsAccepted = ValueNotifier(false);

  bool get isLightMode => HiveUtilsSettings.isLightMode;

  @override
  Widget build(BuildContext context) {
    final pdfFiles = items.where(
      (e) => e.type == DownloadFileType.pdf,
    );

    final excelFiles = items.where(
      (e) => e.type == DownloadFileType.excel,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: BottomSheetTopNotch(),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              isPreview ? LocaleKeys.preview.tr() : LocaleKeys.downloadAs.tr(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Row(
              children: [
                if (pdfFiles.isNotEmpty)
                  Expanded(
                    child: Column(
                      children: pdfFiles
                          .map(
                            (e) => _buildItemTile(context, e),
                          )
                          .toList(),
                    ),
                  ),
                if (excelFiles.isNotEmpty)
                  Expanded(
                    child: Column(
                      children: excelFiles
                          .map(
                            (e) => _buildItemTile(context, e),
                          )
                          .toList(),
                    ),
                  ),
              ],
            ),
          ),
          if (!isPreview) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                children: [
                  ValueListenableBuilder(
                    valueListenable: _isTermsAccepted,
                    builder: (context, value, child) => SizedBox(
                      width: 16,
                      height: 16,
                      child: Checkbox(
                        checkColor: AppColors.white,
                        activeColor: AppColors.blueLight,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        side: const BorderSide(color: AppColors.greyShade1),
                        value: value,
                        onChanged: (value) {
                          if (value == null) return;
                          _isTermsAccepted.value = value;
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: LocaleKeys.iAgreeTo.tr(),
                          style: const TextStyle(
                            color: AppColors.grey,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: ' ${LocaleKeys.termsAndConditions.tr()}',
                          recognizer: TapGestureRecognizer()
                            ..onTap = () => AutoRouter.of(context).push(
                                  TermsAndConditionsScreenRoute(),
                                ),
                          style: TextStyle(
                            color: isLightMode ? AppColors.blueLight : AppColors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }

  Widget _buildItemTile(BuildContext context, DownloadItem item) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
      child: Material(
        color: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: AppColors.greyShade1),
        ),
        child: InkWell(
          onTap: () => _onFileTapped(context, item.downloadUrl, item.fileName),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: item.type.color,
                    shape: BoxShape.circle,
                  ),
                  padding: const EdgeInsets.all(12),
                  child: SvgPicture.asset(
                    item.type.assetPath,
                    colorFilter: const ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item.fileName,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onFileTapped(BuildContext context, String url, String fileName) {
    if (isPreview) {
      return _previewFile(context, url, fileName);
    }

    if (!_isTermsAccepted.value) {
      AppMessage.showOverlayNotificationError(
        message: LocaleKeys.termsAndConditionsWarning.tr(),
      );
      return;
    }

    _downloadFile(url, fileName);
  }

  void _previewFile(BuildContext context, String url, String filename) {
    final extension = filename.split('.').last;
    switch (extension) {
      case 'pdf':
        context.pushRoute(
          PdfPreviewScreenRoute(url: url, title: filename),
        );

      case 'xlsx':
      case 'xlsm':
        // context.pushRoute(
        //   ExcelPreviewScreenRoute(url: url, title: filename),
        // );
        return;

      default:
        AppMessage.showOverlayNotificationError(
          message: LocaleKeys.unsupportedFileType.tr(),
        );
    }
  }

  void _downloadFile(String url, String filename) => DownloadService(
        url,
        filename: filename,
        isAuthenticated: isAuthenticated,
        folder: folder,
        onDownloadSuccess: (filename, filePath) => AppMessage.showOverlayNotificationSuccess(
          title: LocaleKeys.fileDownloaded.tr(),
          message: filename,
          onTap: () => DownloadHelper.openFile(filePath),
        ),
        onDownloadFailed: (error) => AppMessage.showOverlayNotificationError(
          message: LocaleKeys.unableToDownloadFile.tr(),
        ),
      ).start();
}
