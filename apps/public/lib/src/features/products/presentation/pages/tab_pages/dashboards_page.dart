import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../route_manager/route_imports.gr.dart';
import '../../../../../common/constants.dart';
import '../../../../../common/widgets/domain_icon.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/expandable_widget.dart';
import '../../../../../common/widgets/expandable_widget_list_item.dart';
import '../../../../../common/widgets/indicator_classification.dart';
import '../../../../../common/widgets/maintenance/maintenance_checker.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../../data/models/product_dashboard.dart';
import '../../bloc/products_bloc.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../utils/remote_config_utils/maintenance_status.dart';
import 'package:webview_flutter/webview_flutter.dart';

class DashboardsPage extends StatefulWidget {
  const DashboardsPage({super.key});

  @override
  State<DashboardsPage> createState() => _DashboardsPageState();
}

class _DashboardsPageState extends State<DashboardsPage>
    with AutomaticKeepAliveClientMixin {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  WebViewController? demoDashboardWebViewController;

   Map<String, List<TableauDashboardResponseItem>> map = {};

  String? errorMessage;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _reload();
      });
  }

  void _reload() {
    errorMessage = null;
    context.read<ProductsBloc>().add(const ProductsGetDashboardsEvent());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<ProductsBloc, ProductsState>(
      listenWhen: (previousState, nextState) => nextState is GetDashboardsBaseState,
      listener: (BuildContext context, state) {
        if (state is GetDashboardsSuccessState) {
          final List<TableauDashboardResponseItem> dashboards = state.data;

          for (int i = 0; i < dashboards.length; i++) {
            map[dashboards[i].domainId!] = [];
          }

          for (int i = 0; i < map.keys.length; i++) {
            map[map.keys.toList()[i]] = dashboards
                .where((e) => e.domainId == map.keys.toList()[i])
                .toList();
          }

          final entries = map.entries.toList()
            ..sort((a, b) {
              final int idxA = a.value.indexWhere((it) => kDomainOrderList.contains(it.domainId.toString()));
              final int idxB = b.value.indexWhere((it) => kDomainOrderList.contains(it.domainId.toString()));
              final int posA =
                  idxA == -1 ? kDomainOrderList.length : kDomainOrderList.indexOf(a.value[idxA].domainId.toString());
              final int posB =
                  idxB == -1 ? kDomainOrderList.length : kDomainOrderList.indexOf(b.value[idxB].domainId.toString());
              return posA.compareTo(posB);
            });

          map = Map.fromEntries(entries);
        } else if (state is GetDashboardsErrorState) {
          errorMessage = state.error;
        }
      },
      builder: (context, state) {
        if (state is GetDashboardsLoadingState || (map.keys.isEmpty && state is! GetDashboardsBaseState)) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (errorMessage != null) {
          return ErrorReloadPlaceholder(
            padding: const EdgeInsets.only(bottom: 100),
            error: errorMessage!,
            onReload: _reload,
          );
        }

        if (map.keys.isEmpty) {
          return const NoDataPlaceholder(padding: EdgeInsets.only(bottom: 150));
        }

        return MaintenanceChecker(
          checkPredicate: (MaintenanceStatus status) => status.modules.contains('dashboard_mobile'),
          child: ListView.builder(
            padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
            itemCount: map.keys.length,
            itemBuilder: (context, i) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: ExpandableWidget(
                  title:
                  HiveUtilsSettings.isLanguageEnglish
                      ? map[map.keys.toList()[i]]?.firstOrNull?.domainName ?? ''
                      : map[map.keys.toList()[i]]?.firstOrNull?.domainNameAr ?? '',
                  leadingIcon: SizedBox(
                    width: 40,
                    child: DomainIcon(
                      domainIdOrName: map[map.keys.toList()[i]]?.firstOrNull?.domainId,
                      size: 22,
                      color: isLightMode ? null : AppColors.white,
                    ),
                  ),
                  expandedChild: ListView.builder(
                    itemCount: map[map.keys.toList()[i]]!.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                      vertical: 14,
                      horizontal: 14,
                    ),
                    itemBuilder: (context, index) {
                      final TableauDashboardResponseItem dbs =
                          map[map.keys.toList()[i]]![index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: ExpandableWidgetListItem(
                          title: HiveUtilsSettings.isLanguageEnglish
                          ? dbs.name!
                          : dbs.nameAr!,
                          trailingIcon: Padding(
                            padding: const EdgeInsets.all(4),
                            child: Row(
                              children: [
                                IndicatorClassification(
                                  classificationLevel: 0,
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 12),
                                  child: SvgPicture.asset(
                                    AppImages.icLink,
                                    colorFilter: ColorFilter.mode(
                                      isLightMode
                                          ? AppColors.blueShade22
                                          : Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          onTap: () {
                            context.pushRoute(
                              DashboardWebViewPageRoute(
                                title: HiveUtilsSettings.isLanguageEnglish ? dbs.name! : dbs.nameAr!,
                                dashboard: dbs,
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
