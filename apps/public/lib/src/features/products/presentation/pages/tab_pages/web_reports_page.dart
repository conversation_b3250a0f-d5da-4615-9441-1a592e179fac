import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../../data/models/scad_api/web_report_response.dart';
import '../../bloc/products_bloc.dart';
import '../../widgets/web_report_item.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebReportsPage extends StatefulWidget {
  const WebReportsPage({super.key});

  @override
  State<WebReportsPage> createState() => _WebReportsPageState();
}

class _WebReportsPageState extends State<WebReportsPage>
    with AutomaticKeepAliveClientMixin {
  final bool isLightMode = HiveUtilsSettings.isLightMode;
  WebViewController? demoWebReportsWebViewController;
  ScrollController scrollController = ScrollController();

  List<ItemsWebReport> listWebReports = [];

  int? count;
  int pageNo = 0;
  bool isLoading = false;
  int apiRequestId = 0;

  String? errorMessage;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _pageResetPublication();
      _loadApi();
      scrollController.addListener(_scrollListener);
    });
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<ProductsBloc, ProductsState>(
      listenWhen: (_, state) => state is GetWebReportsBaseState,
      buildWhen: (_, state) => state is GetWebReportsBaseState,
      listener: (BuildContext context, state) {
        if (state is WebReportsErrorState) {
          errorMessage = state.error;
        } else if (state is WebReportsSuccessState) {
          if (state.apiRequestId != apiRequestId) return;

          listWebReports.addAll(
            (state.data.items ?? [])
              ..removeWhere(
                (element) => listWebReports
                    .any((element1) => element.title == element1.title),
              ),
          );

          for (final (_, item) in listWebReports.indexed) {
            for (final (_, content) in (item.contentFields ?? []).indexed) {
              if (content.label == 'iframeURL English') {
                item.dashboardEn = content.contentFieldValue?.data ?? '';
              } else if (content.label == 'iframeURL Arabic') {
                item.dashboardAr = content.contentFieldValue?.data ?? '';
              }
            }
          }

          count = state.data.totalCount;
          isLoading = false;
        }
      },
      builder: (context, state) {
        if (state is WebReportsLoadingState) {
          return const Center(child: CircularProgressIndicator());
        } else if (errorMessage != null) {
          return ErrorReloadPlaceholder(
            padding: const EdgeInsets.only(bottom: 100),
            error: errorMessage!,
            onReload: () {
              --pageNo;
              _loadApi();
            },
          );
        } else if (listWebReports.isEmpty) {
          return const NoDataPlaceholder(padding: EdgeInsets.only(bottom: 100));
        }

        return ListView.builder(
          controller: scrollController,
          shrinkWrap: true,
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
          itemCount: listWebReports.length,
          itemBuilder: (e, i) {
            return WebReportItem(webReport: listWebReports[i]);
          },
        );
      },
    );
  }

  void _pageResetPublication() {
    listWebReports = [];
    count = null;
    pageNo = 0;
  }

  void _scrollListener() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent) {
      if ((count != null && listWebReports.length >= count! * 5) || isLoading) {
        return;
      }
      _loadApi();
    }
  }

  void _loadApi() {
    errorMessage = null;
    apiRequestId = Random().nextInt(100000);
    if (pageNo < 0) pageNo = 0;
    context.read<ProductsBloc>().add(
          GetWebReportsEvent(
            apiRequestId: apiRequestId,
            pageNo: ++pageNo,
          ),
        );
  }

}
