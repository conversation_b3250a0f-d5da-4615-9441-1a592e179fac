import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../../main.dart';
import '../../../../../common/widgets/app_box_shadow.dart';
import '../../../../../common/widgets/bottom_sheet_top_notch.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../../../../common/widgets/rounded_dropdown_widget.dart';
import '../../../data/models/scad_api/publication_category_response.dart';
import '../../../data/models/scad_api/publication_domain_response.dart';
import '../../../data/models/scad_api/publication_theme_response.dart';
import '../../../data/models/scad_api/publication_year_response.dart';
import '../../../data/models/scad_api/publications_response.dart';
import '../../bloc/products_bloc.dart';
import '../../widgets/publication_list_item.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../../translations/locale_keys.g.dart';

class PublicationsPage extends StatefulWidget {
  const PublicationsPage({super.key});

  @override
  State<PublicationsPage> createState() => _PublicationsPageState();
}

class _PublicationsPageState extends State<PublicationsPage> with AutomaticKeepAliveClientMixin {
  final bool isLightMode = HiveUtilsSettings.isLightMode;
  ScrollController scrollControllerPublications = ScrollController();
  final TextEditingController _publicationsSearchTextController = TextEditingController();

  List<PublicationItem> listPublication = [];
  int? countPublication;

  int noOfEmptyItems = 0;
  int pageNoPublication = 0;
  bool publicationLoading = false;
  Map<String, dynamic> _publicationFilter = {};

  List<PublicationDomainItem> publicationsDomainList = [
    PublicationDomainItem(id: '0', name: LocaleKeys.all.tr()),
  ];
  List<PublicationThemeItem> publicationsThemeList = [
    PublicationThemeItem(id: '0', name: LocaleKeys.all.tr()),
  ];
  List<PublicationCategoryItem> publicationsCategoryList = [
    PublicationCategoryItem(id: '0', name: LocaleKeys.all.tr()),
  ];
  List<PublicationYearItem> publicationsYearsList = [
    PublicationYearItem(id: '0', name: LocaleKeys.all.tr()),
  ];

  int apiRequestId = 0;

  // PersistentBottomSheetController? controller;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _publicationFilter = {
      'sort': LocaleKeys.issueDate.tr(),
      'sort_order': 'desc',
      'domain': publicationsDomainList[0],
      'theme': publicationsThemeList[0],
      'category': publicationsCategoryList[0],
      'year': publicationsYearsList[0],
    };

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _pageResetPublication();
      _loadApi();
      scrollControllerPublications.addListener(_scrollListener);
    });
  }

  @override
  void dispose() {
    scrollControllerPublications.removeListener(_scrollListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<ProductsBloc, ProductsState>(
      listenWhen: (_, state) => state is PublicationBaseState,
      buildWhen: (_, state) => state is PublicationBaseState,
      listener: (BuildContext context, state) {
        if (state is PublicationSuccessState) {
          if (state.apiRequestId != apiRequestId) return;

          final List<PublicationItem> list = (state.data.items ?? []).map((e) {
            e.contentFields?.removeWhere(
              (e) => e.contentFieldValue?.document?.contentUrl == null,
            );
            return e;
          }).toList();

          final List<PublicationItem> removeItems = list.where((e) => (e.contentFields ?? []).isEmpty).toList();
          noOfEmptyItems += removeItems.length;

          list.removeWhere((e) => removeItems.any((e1) => e1.id == e.id));

          listPublication.addAll(
            list
              ..removeWhere(
                (element) => listPublication.any((element1) => element.id == element1.id),
              ),
          );

          countPublication = state.data.totalCount;
          publicationLoading = false;
          pageNoPublication = state.data.page ?? 1;
        }
      },
      builder: (context, state) {
        if (state is PublicationErrorState) {
          return ErrorReloadPlaceholder(
            error: state.error,
            onReload: () {
              --pageNoPublication;
              _loadApi();
            },
          );
        } else if (state is PublicationsLoadingState) {
          publicationLoading = true;
        }

        return Stack(
          children: [
            Column(
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: Container(
                          margin: HiveUtilsSettings.isLanguageEnglish
                              ? const EdgeInsets.fromLTRB(0, 8, 12, 8)
                              : const EdgeInsets.fromLTRB(12, 8, 0, 8),
                          decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(60),
                            ),
                            shadows: AppBox.shadow(),
                          ),
                          child: TextField(
                            controller: _publicationsSearchTextController,
                            onSubmitted: (value) {
                              _pageResetPublication();
                              _loadApi();
                            },
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.black,
                            ),
                            decoration: InputDecoration(
                              suffixIcon: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: SvgPicture.asset(
                                  AppImages.iconSearchBlack,
                                  width: 16,
                                  height: 16,
                                ),
                              ),
                              hintText: LocaleKeys.searchInPublications.tr(),
                              hintStyle: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w300,
                                color: AppColors.grey,
                              ),
                              isDense: true,
                              border: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: Colors.blueAccent,
                                  width: 32,
                                ),
                                borderRadius: BorderRadius.circular(25),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: Colors.white,
                                  width: 32,
                                ),
                                borderRadius: BorderRadius.circular(25),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: Colors.white,
                                  width: 32,
                                ),
                                borderRadius: BorderRadius.circular(
                                  25,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                            ),
                          ),
                        ),
                      ),
                      Material(
                        borderRadius: BorderRadius.circular(100),
                        color: isLightMode ? AppColors.blueLightOld : AppColors.blueLight,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(100),
                          onTap: _publicationFilterBottomSheet,
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: SvgPicture.asset(
                              AppImages.icFilters,
                              colorFilter: const ColorFilter.mode(
                                AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (listPublication.isEmpty)
                  state is PublicationsLoadingState
                      ? Padding(
                          padding: EdgeInsets.only(
                            top: MediaQuery.sizeOf(context).height * .25,
                          ),
                          child: const Center(child: CircularProgressIndicator()),
                        )
                      : Padding(
                          padding: EdgeInsets.only(
                            top: MediaQuery.sizeOf(context).height * .16,
                          ),
                          child: const NoDataPlaceholder(),
                        ),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    padding: const EdgeInsets.fromLTRB(24, 12, 24, 150),
                    itemCount: listPublication.length,
                    controller: scrollControllerPublications,
                    itemBuilder: (e, i) {
                      return PublicationListItem(
                        publicationItem: listPublication[i],
                      );
                    },
                  ),
                ),
              ],
            ),
            if (listPublication.isNotEmpty && publicationLoading)
              Positioned(
                bottom: 100,
                child: SizedBox(
                  width: MediaQuery.sizeOf(context).width,
                  child: const Center(child: CircularProgressIndicator()),
                ),
              ),
          ],
        );
      },
    );
  }

  void _pageResetPublication() {
    listPublication = [];
    countPublication = null;
    pageNoPublication = 0;
    noOfEmptyItems = 0;
  }

  void _scrollListener() {
    if (scrollControllerPublications.position.pixels >= scrollControllerPublications.position.maxScrollExtent) {
      if ((countPublication != null && (listPublication.length + noOfEmptyItems) >= countPublication!) ||
          publicationLoading) {
        return;
      }
      _loadApi();
    }
  }

  void _loadApi() {
    final List<String> params = ['search=${_publicationsSearchTextController.text}'];

    if (_publicationFilter['sort'] == LocaleKeys.issueDate.tr()) {
      params.add(
        'sort=customFields%2FUIIssueDate:${_publicationFilter['sort_order'] ?? 'desc'}',
      );
    } else if (_publicationFilter['sort'] == LocaleKeys.title.tr()) {
      params.add('sort=title:${_publicationFilter['sort_order'] ?? 'desc'}');
    }

    String? id;
    if (_publicationFilter['category'].id != '0') {
      id = _publicationFilter['category'].id.toString();
    } else if (_publicationFilter['theme'].id != '0') {
      id = _publicationFilter['theme'].id.toString();
    } else if (_publicationFilter['domain'].id != '0') {
      id = _publicationFilter['domain'].id.toString();
    }

    String param = '';
    if (id != null) {
      param = 'taxonomyCategoryIds/any(taxonomyCategoryId:taxonomyCategoryId eq $id)';
    }

    if (_publicationFilter['year'].id != '0') {
      if (param.isNotEmpty) {
        param += ' and ';
      }
      param += "customFields/ReferencePeriodYear eq '${_publicationFilter['year'].name}'";
    }

    if (param.isNotEmpty) {
      param = 'filter=$param';
      params.add(param);
    }

    apiRequestId = Random().nextInt(100000);
    if (pageNoPublication < 0) pageNoPublication = 0;
    context.read<ProductsBloc>().add(
          GetPublicationsEvent(
            apiRequestId: apiRequestId,
            pageNo: pageNoPublication + 1,
            paramList: params,
          ),
        );
  }

  void _publicationFilterBottomSheet() {
    FocusManager.instance.primaryFocus?.unfocus();

    String? sort = _publicationFilter['sort'].toString();
    String? sortOrder = _publicationFilter['sort_order'].toString();
    PublicationDomainItem domain = _publicationFilter['domain'] as PublicationDomainItem;
    PublicationThemeItem theme = _publicationFilter['theme'] as PublicationThemeItem;
    PublicationCategoryItem category = _publicationFilter['category'] as PublicationCategoryItem;
    PublicationYearItem year = _publicationFilter['year'] as PublicationYearItem;

    showModalBottomSheet<void>(
      context: context,
      useRootNavigator: true,
      backgroundColor: isLightMode ? AppColors.white : AppColors.blueShade32,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.filters.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 20),
              BlocConsumer<ProductsBloc, ProductsState>(
                listener: (context, state) {
                  if (state is PublicationDomainsSuccessState) {
                    publicationsDomainList = publicationsDomainList.take(1).toList()..addAll(state.data.items ?? []);

                    if (domain.id == '0') {
                      publicationsThemeList = publicationsThemeList.take(1).toList();
                      publicationsCategoryList = publicationsCategoryList.take(1).toList();
                    } else {
                      _loadFilterThemes(scadDomainId: domain.id ?? '');
                    }
                  } else if (state is PublicationThemesSuccessState) {
                    publicationsThemeList = publicationsThemeList.take(1).toList()..addAll(state.data.items ?? []);

                    if (theme.id == '0') {
                      publicationsCategoryList = publicationsCategoryList.take(1).toList();
                    } else {
                      _loadFilterCategories(scadThemeId: theme.id ?? '');
                    }
                  } else if (state is PublicationCategoriesSuccessState) {
                    publicationsCategoryList = publicationsCategoryList.take(1).toList()
                      ..addAll(state.data.items ?? []);
                  } else if (state is PublicationYearsSuccessState) {
                    publicationsYearsList = publicationsYearsList.take(1).toList()
                      ..addAll(
                        (state.data.items ?? [])
                          ..sort(
                            (a, b) => (int.tryParse('${b.name}') ?? 0).compareTo(
                              int.tryParse('${a.name}') ?? 0,
                            ),
                          ),
                      );
                  }
                },
                builder: (context, state) {
                  return Stack(
                    children: [
                      StatefulBuilder(
                        builder: (context, setState) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  Expanded(
                                    child: RoundedDropDownWidget<String>(
                                      width: MediaQuery.sizeOf(context).width / 2,
                                      title: LocaleKeys.sort.tr(),
                                      items: [
                                        LocaleKeys.issueDate.tr(),
                                        LocaleKeys.title.tr(),
                                      ],
                                      value: sort,
                                      leadingIcon: Padding(
                                        padding: const EdgeInsets.only(
                                          right: 4,
                                        ),
                                        child: RotatedBox(
                                          quarterTurns: sortOrder == 'desc' ? 0 : 2,
                                          child: SvgPicture.asset(
                                            AppImages.icArrowDown,
                                            height: 14,
                                          ),
                                        ),
                                      ),
                                      onChanged: (val) {
                                        if (sort == val) {
                                          sortOrder = sortOrder == 'desc' ? 'asc' : 'desc';
                                        } else {
                                          sort = val;
                                        }
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: RoundedDropDownWidget<PublicationDomainItem>(
                                      width: MediaQuery.sizeOf(context).width / 2,
                                      title: LocaleKeys.domain.tr(),
                                      items: publicationsDomainList,
                                      value: domain,
                                      onChanged: (val) {
                                        if (domain.id == val?.id) return;
                                        domain = val!;
                                        publicationsThemeList = publicationsThemeList.take(1).toList();
                                        publicationsCategoryList = publicationsCategoryList.take(1).toList();
                                        theme = publicationsThemeList[0];
                                        category = publicationsCategoryList[0];

                                        if (val.id == '0') {
                                          setState(() {});
                                        } else {
                                          _loadFilterThemes(
                                            scadDomainId: domain.id ?? '',
                                          );
                                        }
                                      },
                                      itemLabelBuilder: (PublicationDomainItem? item) {
                                        return item?.name ?? '-';
                                      },
                                      enabled: publicationsDomainList.length > 1,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  Expanded(
                                    child: RoundedDropDownWidget<PublicationThemeItem?>(
                                      width: MediaQuery.sizeOf(context).width / 2,
                                      constraintWidth: MediaQuery.sizeOf(context).width / 2,
                                      title: LocaleKeys.theme.tr(),
                                      items: publicationsThemeList,
                                      value: theme,
                                      onChanged: (val) {
                                        if (theme.id == val?.id) return;
                                        theme = val!;
                                        publicationsCategoryList = publicationsCategoryList.take(1).toList();
                                        category = publicationsCategoryList[0];

                                        if (val.id == '0') {
                                          setState(() {});
                                        } else {
                                          _loadFilterCategories(
                                            scadThemeId: theme.id ?? '',
                                          );
                                        }
                                      },
                                      itemLabelBuilder: (PublicationThemeItem? item) {
                                        return item?.name ?? '-';
                                      },
                                      enabled: publicationsThemeList.length > 1,
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  Expanded(
                                    child: RoundedDropDownWidget<PublicationCategoryItem?>(
                                      width: MediaQuery.sizeOf(context).width / 2,
                                      title: LocaleKeys.category.tr(),
                                      items: publicationsCategoryList,
                                      value: category,
                                      onChanged: (val) {
                                        category = val!;
                                        setState(() {});
                                      },
                                      itemLabelBuilder: (
                                        PublicationCategoryItem? item,
                                      ) {
                                        return item?.name ?? '-';
                                      },
                                      enabled: publicationsCategoryList.length > 1,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              RoundedDropDownWidget<PublicationYearItem?>(
                                title: LocaleKeys.year.tr(),
                                items: publicationsYearsList,
                                constraintWidth: MediaQuery.sizeOf(context).width / 2,
                                value: year,
                                onChanged: (val) {
                                  year = val!;
                                  setState(() {});
                                },
                                itemLabelBuilder: (PublicationYearItem? item) {
                                  return item?.name ?? '-';
                                },
                                enabled: publicationsYearsList.length > 1,
                              ),
                              const SizedBox(height: 25),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size.fromHeight(43),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                onPressed: () {
                                  context.maybePop();
                                  _publicationFilter['sort'] = sort;
                                  _publicationFilter['sort_order'] = sortOrder;
                                  _publicationFilter['domain'] = domain;
                                  _publicationFilter['theme'] = theme;
                                  _publicationFilter['category'] = category;
                                  _publicationFilter['year'] = year;
                                  _pageResetPublication();
                                  _loadApi();
                                  scrollControllerPublications.animateTo(
                                    0,
                                    duration: const Duration(
                                      milliseconds: 280,
                                    ),
                                    curve: Curves.linear,
                                  );
                                },
                                child: Text(
                                  LocaleKeys.done.tr(),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                      Positioned.fill(
                        child: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child: ((publicationsDomainList.length <= 1 &&
                                      publicationsThemeList.length <= 1 &&
                                      publicationsCategoryList.length <= 1 &&
                                      publicationsYearsList.length <= 1) ||
                                  state is PublicationsFilterLoadingState)
                              ? ColoredBox(
                                  color: isLightMode ? AppColors.white : AppColors.blueShade32,
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                )
                              : (state is PublicationDomainsErrorState)
                                  ? ColoredBox(
                                      color: isLightMode ? AppColors.white : AppColors.blueShade32,
                                      child: ErrorReloadPlaceholder(
                                        error: state.error,
                                        onReload: () {
                                          _loadFilterDomains();
                                        },
                                      ),
                                    )
                                  : const SizedBox(),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        );
      },
    );

    _loadFilterDomains();
    _loadFilterYears();
  }

  void _loadFilterDomains() {
    context.read<ProductsBloc>().add(const GetPublicationDomainsEvent());
  }

  void _loadFilterThemes({required String scadDomainId}) {
    context.read<ProductsBloc>().add(GetPublicationThemesEvent(scadDomainId: scadDomainId));
  }

  void _loadFilterCategories({required String scadThemeId}) {
    context.read<ProductsBloc>().add(GetPublicationCategoriesEvent(scadThemeId: scadThemeId));
  }

  void _loadFilterYears() {
    context.read<ProductsBloc>().add(const GetPublicationYearsEvent());
  }
}
