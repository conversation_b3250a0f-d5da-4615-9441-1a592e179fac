import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../../data/models/product_dashboard.dart';
import '../../../../../utils/app_utils/device_type.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import 'package:webview_flutter/webview_flutter.dart';

@RoutePage()
class DashboardWebViewPage extends StatefulWidget {
  const DashboardWebViewPage({
    required this.title,
    this.dashboard,
    super.key,
  });

  final String title;
  final TableauDashboardResponseItem? dashboard;

  @override
  State<DashboardWebViewPage> createState() => _DashboardWebViewPageState();
}

class _DashboardWebViewPageState extends State<DashboardWebViewPage> with SingleTickerProviderStateMixin {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  WebViewController? controller;
  final ValueNotifier<bool> _showLoading = ValueNotifier(true);

  late final _animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 300),
  );

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback(
      (_) {
        context.read<HomeBloc>().add(const CheckMaintenanceEvent());
        _initWebView();
      },
    );
  }

  Future<void> _initWebView() async {
    final controller = WebViewController();

    final embedCode = _getEmbeddedCode();
    await Future.wait([
      controller.setJavaScriptMode(JavaScriptMode.unrestricted),
      controller.addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: (_) {
          _animationController.forward().then((_) {
            _showLoading.value = false;
          });
        },
      ),
      controller.loadHtmlString(embedCode),
    ]);

    setState(() => this.controller = controller);
  }

  @override
  Widget build(BuildContext context) {
    final display = View.of(context).display;
    final isTab = DeviceType.isTab(display);
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;

    return RotatedBox(
        quarterTurns: isTab && isPortrait ? 1 : 0,
        child: AppDrawer(
          child: Scaffold(
            body: BlocListener<HomeBloc, HomeState>(
            listenWhen: (_, state) => state is CheckMaintenanceSuccessState,
            listener: (context, state) {
              if (state is! CheckMaintenanceSuccessState) return;
              final isDashboardMaintenanceActive =
                  state.data.active && state.data.modules.contains('dashboard_mobile');
              if (isDashboardMaintenanceActive) {
                context.back();
              }
            },
            child: Column(
              children: [
                FlatAppBar(
                  title: widget.title,
                  bottomPadding: 0,
                ),
                Expanded(
                  child: SafeArea(
                    child: Stack(
                      children: [
                        if (controller != null)
                          WebViewWidget(
                            controller: controller!,
                          ),
                        AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            final animation = _animationController.drive<double>(
                              Tween(begin: 1, end: 0)
                                ..chain(
                                  CurveTween(curve: Curves.fastOutSlowIn),
                                ),
                            );

                            return Opacity(
                              opacity: animation.value,
                              child: child,
                            );
                          },
                          child: ValueListenableBuilder(
                            valueListenable: _showLoading,
                            builder: (context, value, child) {
                              return _showLoading.value ? child! : const SizedBox.shrink();
                            },
                            child: Container(
                              padding: EdgeInsets.only(top: MediaQuery.sizeOf(context).width * .3),
                              color: AppColors.scaffoldBackground,
                              alignment: Alignment.topCenter,
                              child: Image.asset(
                                AppImages.animatedLogoLoading,
                                width: 150,
                                height: 150,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
                        ),
        ),
      ),
    );
  }

  String _getEmbeddedCode() {
    return '''
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0">
    <title></title>
    <script type='module' src='https://dv.scad.gov.ae/javascripts/api/tableau.embedding.3.latest.min.js'></script>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body>
    <tableau-viz id='tableauViz'
        src='$_dashboardUrl'
        onFirstInteractive="onFirstInteractiveHandler">
    </tableau-viz>
</body>

<script>
    function onFirstInteractiveHandler(event) {
        if (!window.FlutterChannel) {
            console.log('No flutter channel');
            return;
        }
        window.FlutterChannel.postMessage('page loaded');
    }
</script>
    
</html>
''';
  }

  String get _dashboardUrl {
    final rtl = DeviceType.isDirectionRTL(context);
    final dashboard = widget.dashboard;
    if (dashboard == null) return '';

    if (isLightMode && !rtl) {
      return dashboard.url ?? '';
    } else if (isLightMode && rtl) {
      return dashboard.urlAr ?? '';
    } else if (!isLightMode && !rtl) {
      return dashboard.urlDark ?? '';
    } else if (!isLightMode && rtl) {
      return dashboard.urlArDark ?? '';
    } else {
      return '';
    }
  }

  @override
  void dispose() {
    super.dispose();
    _animationController.dispose();
  }
}
