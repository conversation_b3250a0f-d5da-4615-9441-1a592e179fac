import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import '../../../../common/widgets/collapsable_text.dart';
import '../../../../common/widgets/indicator_classification.dart';
import '../../../../common/widgets/primary_icon_button.dart';
import '../../data/models/scad_api/web_report_response.dart';
import '../../../../utils/app_utils/app_message.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

class WebReportItem extends StatefulWidget {
  const WebReportItem({required this.webReport, super.key});

  final ItemsWebReport webReport;

  @override
  State<WebReportItem> createState() => _WebReportItemState();
}

class _WebReportItemState extends State<WebReportItem> {
  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Material(
        borderRadius: BorderRadius.circular(15),
        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: _launchLink,
          child: ExpandableNotifier(
            child: Padding(
              padding: const EdgeInsets.all(15),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: CollapsableText(
                      text: widget.webReport.title ?? '',
                    ),
                  ),
                  Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: IndicatorClassification(
                          classificationLevel: 0,
                        ),
                      ),
                      const SizedBox(width: 2),
                      PrimaryIconButton(
                        iconPath: AppImages.icLink,
                        iconColor: isLightMode ? AppColors.blueLightOld : Colors.white,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _launchLink() async {
    final path = HiveUtilsSettings.isLanguageEnglish ? widget.webReport.dashboardEn : widget.webReport.dashboardAr;
    try {
      final Uri launchUri = Uri.parse(path);
      await launchUrl(launchUri);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
    }
  }
}
