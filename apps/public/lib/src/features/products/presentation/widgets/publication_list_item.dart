import 'package:flutter/material.dart';
import '../../../../common/widgets/collapsable_text.dart';
import '../../../../common/widgets/indicator_classification.dart';
import '../../../../common/widgets/primary_icon_button.dart';
import '../../../../config/app_config/api_config.dart';
import '../../data/models/scad_api/publications_response.dart';
import '../sheets/download_as_bottom_sheet.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';

class PublicationListItem extends StatefulWidget {
  const PublicationListItem({
    required this.publicationItem,
    super.key,
  });

  final PublicationItem publicationItem;

  @override
  State<PublicationListItem> createState() => _PublicationListItemState();
}

class _PublicationListItemState extends State<PublicationListItem> {
  @override
  Widget build(BuildContext context) {
    final hasContentFields = widget.publicationItem.contentFields?.isNotEmpty ?? false;
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: CollapsableText(
              text: widget.publicationItem.title ?? '',
            ),
          ),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 2, 10, 2),
                child: IndicatorClassification(
                  classificationLevel: 0,
                ),
              ),
              if (hasContentFields)
                PrimaryIconButton(
                  iconPath: AppImages.icDownloadMini,
                  iconColor: isLightMode ? AppColors.blueLightOld : AppColors.white,
                  size: 14,
                  onTap: _onDownloadTapped,
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _onDownloadTapped() {
    final contentFields = widget.publicationItem.contentFields ?? [];
    final title = widget.publicationItem.title;
    final items = contentFields.map((e) {
      final fileName = '$title.${e.contentFieldValue?.document?.fileExtension}';
      final url = '${ApiConfig.scadApiPath}${e.contentFieldValue?.document?.contentUrl}';
      final extension = e.contentFieldValue?.document?.fileExtension ?? '';

      return DownloadItem(
        fileName: fileName,
        downloadUrl: url,
        type: DownloadFileType.fromExtension(extension),
      );
    });

    DownloadAsBottomSheet.show(
      context,
      items: items,
      folder: 'Publications',
      isAuthenticated: false,
    );
  }
}
