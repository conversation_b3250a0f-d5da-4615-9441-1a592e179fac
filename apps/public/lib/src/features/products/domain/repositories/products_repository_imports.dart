import '../../../../common/models/response_models/repo_response.dart';
import '../../data/models/product_dashboard.dart';
import '../../data/models/scad_api/publication_category_response.dart';
import '../../data/models/scad_api/publication_domain_response.dart';
import '../../data/models/scad_api/publication_theme_response.dart';
import '../../data/models/scad_api/publication_year_response.dart';
import '../../data/models/scad_api/publications_response.dart';
import '../../data/models/scad_api/web_report_response.dart';
import '../../../../utils/hive_utils/api_cache/api_cache.dart';

part 'products_repository.dart';
