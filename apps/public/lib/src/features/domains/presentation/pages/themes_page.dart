
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../../../common/constants.dart';
import '../../../../common/widgets/app_sliding_tab.dart';
import '../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../common/widgets/count_widget.dart';
import '../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../common/widgets/expandable_widget.dart';
import '../../../../common/widgets/expandable_widget_list_item.dart';
import '../../../../common/widgets/no_data_placeholder.dart';
import '../../../../common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import '../../../../common/widgets/user_guide/showcaseview_package/widget_extension.dart';
import '../../data/models/domain_classification_model.dart';
import '../../data/models/domain_model/domain_model.dart';
import '../../data/models/theme_subtheme_response.dart';
import '../bloc/domains_bloc.dart';
import 'theme_indicators_screen.dart';
import '../../../home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import '../../../products/data/models/scad_api/web_report_response.dart';
import '../../../products/presentation/widgets/web_report_item.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';

@RoutePage()
class ThemesPage extends StatefulWidget {
  const ThemesPage({
    this.domainId,
    super.key,
  });

  final String? domainId;

  @override
  State<ThemesPage> createState() => _ThemesPageState();
}

class _ThemesPageState extends State<ThemesPage> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final bool isLightMode = HiveUtilsSettings.isLightMode;

  List<DomainModel> domainList = [];
  List<DomainClassificationModel> classificationList = [];
  List<ThemeSubThemeResponse> officialThemeSubThemeList = [];

  int selectedDomainTabIndex = 0;

  ScrollController scrollController = ScrollController();
  num indicatorsRefreshedAt = 0;

  List<GlobalKey> steps = [];
  bool isThemeSubThemeLoading = true;

  DomainModel get _censusDomainModel => DomainModel(domainId: 'census', domainName: 'Census', domainNameAr: 'التعداد');

  String get _censusPortalUrl => 'https://census.scad.gov.ae';

  @override
  void initState() {
    super.initState();

    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains) {
      steps = [GlobalKey(debugLabel: 'domains-tab')];
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        ShowCaseWidget.of(context).startShowCase(steps);
      });
    }

    _initFetchData();
  }

  void _initFetchData() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.domainId != null) {
        context.read<DomainsBloc>().add(ThemesInitEvent(domainId: widget.domainId ?? ''));
      } else {
        context.read<DomainsBloc>().add(const DomainsInitEvent());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return widget.domainId != null ? AppDrawer(child: buildThemePage()) : buildThemePage();
  }

  Scaffold buildThemePage() {
    return Scaffold(
      body: Column(
        children: [
          FlatAppBar(
            mainScreen: widget.domainId == null,
            title: LocaleKeys.statisticalDomains.tr(),
            scrollController: scrollController,
          ),
          Expanded(
            child: BlocConsumer<DomainsBloc, DomainsState>(
              listener: _domainBlocListener,
              builder: (context, state) {
                if (state is DomainLoadingState) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is DomainErrorState) {
                  return ErrorReloadPlaceholder(
                    error: state.error,
                    onReload: _initFetchData,
                  );
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (domainList.isNotEmpty) _buildSlidingDomainTabs(),
                    const SizedBox(height: 20),
                    if (domainList.isNotEmpty)
                      Divider(
                        height: 1,
                        color: isLightMode ? AppColors.greyShade1 : AppColors.blackShade4,
                      ),
                    Expanded(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 100),
                        transitionBuilder: (child, anim) => FadeTransition(
                          opacity: anim,
                          child: child,
                        ),
                        child: _buildThemeList(),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _domainBlocListener(BuildContext context, DomainsState state) {
    switch (state) {
      case ThemeClassificationShowResponseState _:
        classificationList = state.list;
        domainList = state.domains;

        if (widget.domainId == null) {
          domainList.insert(0, _censusDomainModel);
        }

        // Sort domains in the order
        final List<String> orderList = kDomainOrderList;

        domainList.sort((a, b) {
          final i = orderList.indexOf(a.domainId.toString());
          final j = orderList.indexOf(b.domainId.toString());
          return (i == -1 ? orderList.length : i).compareTo(j == -1 ? orderList.length : j);
        });

        selectedDomainTabIndex = domainList.indexWhere(
          (element) => element.domainId == widget.domainId,
        );

        if (selectedDomainTabIndex == -1) selectedDomainTabIndex = 0;
        context.read<DomainsBloc>().add(
              GetThemesEvent(
                domainId: widget.domainId ?? (domainList.firstOrNull?.domainId ?? ''),
                officialClassificationId: '${classificationList.firstOrNull?.id}',
              ),
            );

        indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;

      case DomainShowResponseState _:
        context.read<DomainsBloc>().add(
              ThemesInitEvent(
                domainId: state.list.firstOrNull?.domainId ?? '',
              ),
            );

      case GetThemesSuccessState _:
        isThemeSubThemeLoading = false;
        officialThemeSubThemeList = state.officialThemes;
      case ThemeErrorState _:
        isThemeSubThemeLoading = false;
    }
  }

  Widget _buildThemeList() {
    if (domainList.isNotEmpty && domainList[selectedDomainTabIndex].domainId == _censusDomainModel.domainId) {
      return _censusDomainPage();
    }

    final List<ThemeSubThemeResponse> list = officialThemeSubThemeList;
    if (list.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 150),
          child: isThemeSubThemeLoading ? const CircularProgressIndicator() : const NoDataPlaceholder(),
        ),
      );
    }

    return ListView.builder(
      controller: scrollController,
      // shrinkWrap: true,
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final ThemeSubThemeResponse item = list[index];

        return Padding(
          padding: const EdgeInsets.only(bottom: 14),
          child: ExpandableWidget(
            title: item.name ?? '-',
            trailingIcon: CountWidget(count: item.subthemes!.length),
            expandedChild: ListView.builder(
              itemCount: item.subthemes!.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(
                vertical: 14,
                horizontal: 14,
              ),
              itemBuilder: (context, i) {
                final SubTheme subThemeItem = item.subthemes![i];

                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: ExpandableWidgetListItem(
                    title: subThemeItem.name ?? '-',
                    trailingIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.chevron_right_rounded,
                        size: 18,
                        color: isLightMode ? AppColors.blueShade22 : AppColors.white,
                      ),
                    ),
                    onTap: () {
                      pushScreen(
                        context,
                        withNavBar: true,
                        screen: ThemeIndicatorsScreen(
                          isFromMainScreen: widget.domainId != null,
                          title: subThemeItem.name!,
                          domain: domainList[selectedDomainTabIndex],
                          classification: classificationList.first,
                          subDomain: item,
                          subTheme: subThemeItem,
                          screenerConfiguration: classificationList.firstOrNull?.key == 'experimental_statistics'
                              ? item.screenerConfiguration
                              : subThemeItem.screenerConfiguration,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildSlidingDomainTabs() {
    final bool isRtl = HiveUtilsSettings.isLanguageArabic;
    return ValueListenableBuilder(
      valueListenable: HiveUtilsSettings.box.listenable(),
      builder: (context, _, __) {
        return AppSlidingTab(
          key: Key(
            'theme.domain.$indicatorsRefreshedAt.${HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains}',
          ),
          initialTabIndex: selectedDomainTabIndex,
          onTabChange: (int index) {
            selectedDomainTabIndex = index;

            if (domainList[selectedDomainTabIndex].domainId == _censusDomainModel.domainId) {
              setState(() {});
              return;
            }
            isThemeSubThemeLoading = true;
            officialThemeSubThemeList = [];
            context.read<DomainsBloc>().add(
                  GetThemesEvent(
                    domainId: '${domainList[selectedDomainTabIndex].domainId}',
                    officialClassificationId: '${classificationList.firstOrNull?.id}',
                  ),
                );
          },
          tabs: List.generate(domainList.length, (i) {
            return AppSlidingTabItem(
              label: isRtl ? domainList[i].domainNameAr ?? '-' : domainList[i].domainName ?? '-',
              assetPath: domainList[i].domainId == _censusDomainModel.domainId ? AppImages.icCensus : null,
              iconUrl: domainList[i].domainId == _censusDomainModel.domainId ? null : domainList[i].domainIcon,
            );
          }).toList(),
        ).introWidget(
          steps: steps,
          index: 0,
          title: LocaleKeys.domains.tr(),
          description: LocaleKeys.domainsDescription.tr(),
          arrowAlignment: Alignment.topLeft,
          crossAxisAlignment: CrossAxisAlignment.start,
          targetBorderRadius: 60,
          arrowPadding: MediaQuery.sizeOf(context).width * .2,
          childPadding: HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains
              ? const EdgeInsets.symmetric(vertical: 14)
              : null,
        );
      },
    );
  }

  Widget _censusDomainPage() {
    return ListView(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
      children: [
        WebReportItem(
          webReport: ItemsWebReport(
            dashboardEn: _censusPortalUrl,
            dashboardAr: _censusPortalUrl,
            title: LocaleKeys.censusPublicPortal.tr(),
          ),
        ),
      ],
    );
  }
}
