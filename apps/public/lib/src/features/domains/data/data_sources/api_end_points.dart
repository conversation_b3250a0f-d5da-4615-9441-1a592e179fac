import '../../../../config/app_config/api_config.dart';

class DomainsEndPoints extends ApiConfig {
  static String appPath = ApiConfig.appApiPath;
  static String ifpPath = ApiConfig.ifpApiPath;
  // static String getDomainList = '$ifpPath/content-type/domains';
  static String getDomainList = '$appPath/domain-icon/list';
  static String getDomainClassificationList =
      '$ifpPath/content-type/domains/classifications/{{domain_id}}';
  static String getThemeList =
      '$ifpPath/content-type/domains/filters/{{domain_id}}?classification={{classification_id}}';
  static String getThemeIndicatorList =
      '$ifpPath/content-type/domains/detailv2/{{domain_id}}?classification={{classification_id}}&subtheme={{sub_theme_id}}&subdomain={{sub_domain_id}}&page={{page}}&limit=20';
  static String getThemeExperimentalIndicatorList =
      '$ifpPath/content-type/{{classification}}/?page={{page}}&limit=20';
  static String getScreenerFilters(String classification, String screenerIndicator) =>
      '$ifpPath/content-type/$classification/filters/$screenerIndicator';
  static String getDomainListIfp = '$ifpPath/content-type/domains/navigation';

}
