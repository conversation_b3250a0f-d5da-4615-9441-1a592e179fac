import 'dart:async';

import '../../../../common/models/response_models/repo_response.dart';
import '../../../../common/types.dart';
import '../../../chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import '../data_sources/api_end_points.dart';
import '../models/domain_classification_model.dart';
import '../models/domain_model/domain_model.dart';
import '../models/experimental_filters_response_item.dart';
import '../models/experimental_indicator_list_response.dart';
import '../models/theme_indicator_list_response.dart';
import '../models/theme_subtheme_response.dart';
import '../../domain/repositories/domains_repository_imports.dart';
import '../../../../services/http_service_impl.dart';
import '../../../../services/http_services.dart';
import '../../../../utils/extentions/string_extentions.dart';
import '../../../../utils/hive_utils/hive_keys.dart';

class DomainsRepositoryImpl extends DomainsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<List<DomainModel>>> getDomainIconsList() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyDomainList);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        DomainsEndPoints.getDomainList,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => DomainModel.fromJson(e as JSONObject),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainListIfp() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyDomainListIfp);
    return fetchWithCache<DomainThemeSubThemeModelResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        DomainsEndPoints.getDomainListIfp,
        header: {'Accept-Version': '2.0'},
      ),
      parseResult: (json) => DomainThemeSubThemeModelResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<List<DomainClassificationModel>>> getDomainClassificationList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getDomainClassificationList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache<List<DomainClassificationModel>>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['classification'] as List<dynamic>;
        return data
            .map(
              (e) => DomainClassificationModel.fromJson(e as JSONObject),
            )
            .toList()
          ..removeWhere(
            (element) => ![
              'official_statistics',
              'experimental_statistics',
            ].contains(element.key),
          );
      },
    );
  }

  @override
  Future<RepoResponse<List<ThemeSubThemeResponse>>> getThemeList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getThemeList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => ThemeSubThemeResponse.fromJson(e as JSONObject),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<ThemeIndicatorListResponse>> getThemeIndicatorList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getThemeIndicatorList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache<ThemeIndicatorListResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) => ThemeIndicatorListResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<ExperimentalIndicatorListResponse>> getThemeExperimentalIndicatorList({
    required String classification,
    required int pageNo,
    required Map<String, dynamic> filters,
  }) async {
    final String endpoint = DomainsEndPoints.getThemeExperimentalIndicatorList.setUrlParams({
      'classification': classification,
      'page': '$pageNo',
    });

    final cacheKey = getCacheKey(endpoint, payload: filters);
    return fetchWithCache<ExperimentalIndicatorListResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        jsonPayloadMap: filters,
        server: ApiServer.ifp,
      ),
      parseResult: (json) => ExperimentalIndicatorListResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<List<ExperimentalFiltersResponseItem>>> getExperimentalFilters({
    required String classification,
    required String screenerIndicator,
  }) async {
    final endpoint = DomainsEndPoints.getScreenerFilters(classification, screenerIndicator);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache<List<ExperimentalFiltersResponseItem>>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => ExperimentalFiltersResponseItem.fromJson(
                e as JSONObject,
              ),
            )
            .toList();
      },
    );
  }
}
