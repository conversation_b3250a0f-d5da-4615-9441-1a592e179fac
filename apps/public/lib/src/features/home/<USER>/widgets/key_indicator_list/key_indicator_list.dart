import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import '../../../../../common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import '../../../../../common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import '../../../data/models/key_indicator_list/key_indicator_list_response.dart';
import '../../bloc/home_bloc/home_bloc.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';

class KeyIndicatorList extends StatefulWidget {
  const KeyIndicatorList({
    required this.parentScrollController,
    required this.stepKeys,
    super.key,
  });

  final ScrollController parentScrollController;
  final List<GlobalKey> stepKeys;

  @override
  State<KeyIndicatorList> createState() => _KeyIndicatorListState();
}

class _KeyIndicatorListState extends State<KeyIndicatorList> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String? _errorMessage;

  final _allKeyIndicatorList = <KeyIndicatorListResponseItem>[];

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((timestamp) {
      _loadIndicators();
    });
  }

  void _loadIndicators() {
    _errorMessage = null;
    context.read<HomeBloc>().add(const KeyIndicatorsEvent());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocConsumer<HomeBloc, HomeState>(
      listener: _homeBlocListener,
      builder: (context, state) {
        if (state is KeyIndicatorLoadingState || state is HomeInitial) {
          return const Padding(
            padding: EdgeInsets.only(top: 150),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (_errorMessage != null) {
          return ErrorReloadPlaceholder(
            padding: const EdgeInsets.only(top: 150),
            error: _errorMessage!,
            onReload: _loadIndicators,
          );
        }

        return LazyIndicatorListView<KeyIndicatorListResponseItem>(
          key: const Key('keyIndicatorListView'),
          items: _allKeyIndicatorList,
          parentScrollController: widget.parentScrollController,
          padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
          getId: (item) => item.nodeId!,
          getContentType: (item) => item.contentType!,
          itemBuilder: (context, index, item, overview) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 25),
              child: IndicatorCardV2(
                key: Key(
                  'home.keyIndicators.IndicatorCardV2-${item.nodeId}',
                ),
                id: item.nodeId!,
                contentType: item.contentType!,
                overView: overview,
                openButtonKey: index == 0 ? widget.stepKeys.elementAt(1) : null,
                onUserGuideBackFromDetailsPage: (isPreviousActionTriggered) {
                  if (!isPreviousActionTriggered) return;
                  if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
                    ShowCaseWidget.of(context).startShowCase(widget.stepKeys);
                    ShowCaseWidget.of(context).jumpToId(widget.stepKeys.length - 1);
                  }
                },
              ),
            );
          },
        );
      },
    );
  }

  void _homeBlocListener(BuildContext context, HomeState state) {
    switch (state) {
      case KeyIndicatorListState _:
        _allKeyIndicatorList
          ..clear()
          ..addAll(state.list);

      case KeyIndicatorErrorState _:
        _errorMessage = state.error;
    }
  }
}
