import 'dart:async';

import '../../../../common/models/response_models/repo_response.dart';
import '../../../../common/types.dart';
import '../data_sources/api_end_points.dart';
import '../models/key_indicator_list/key_indicator_list_response.dart';
import '../../domain/repositories/home_repository_imports.dart';
import '../../../../services/http_service_impl.dart';
import '../../../../utils/hive_utils/hive_keys.dart';

class HomeRepositoryImpl extends HomeRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<List<KeyIndicatorListResponseItem>>> keyIndicatorList() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyIndicatorList);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        HomeEndPoints.keyIndicatorList,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => KeyIndicatorListResponseItem.fromJson(
                e as JSONObject,
              ),
            )
            .toList();
      },
    );
  }
}
