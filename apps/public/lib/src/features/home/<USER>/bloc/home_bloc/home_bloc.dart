import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../../data/models/key_indicator_list/key_indicator_list_response.dart';
import '../../../data/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import '../../../domain/repositories/home_repository_imports.dart';
import '../../../../../utils/remote_config_utils/maintenance_status.dart';
import '../../../../../utils/remote_config_utils/remote_config_utils.dart';
import '../../../../../../translations/locale_keys.g.dart';

part 'home_event.dart';
part 'home_state.dart';

const lastKeyIndicatorRefreshKey = 'lastKeyIndicatorListRefreshTime';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc(this.homeRepository) : super(HomeInitial()) {
    on<HomeEvent>((event, emit) {});
    on<NavToHomeEvent>(_onNavToHome);
    on<KeyIndicatorsEvent>(keyIndicatorEventHandler);

    on<CheckMaintenanceEvent>(_onCheckDashboardMaintenance);
  }

  final HomeRepository homeRepository;

  FutureOr<void> _onNavToHome(
    NavToHomeEvent event,
    Emitter<HomeState> emit,
  ) {
    emit(NavToHomeState());
  }

  FutureOr<void> keyIndicatorEventHandler(
    KeyIndicatorsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(KeyIndicatorLoadingState());

      final RepoResponse<List<KeyIndicatorListResponseItem>> response =
          await servicelocator<HomeRepository>().keyIndicatorList();

      if (response.isSuccess) {
        emit(KeyIndicatorListState(list: response.response ?? []));
      } else {
        emit(
          KeyIndicatorErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(KeyIndicatorErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onCheckDashboardMaintenance(CheckMaintenanceEvent event, Emitter<HomeState> emit) async {
    final maintenance = await RemoteConfigUtils.getMaintenanceStatusSync(forceFetch: event.shouldFetch);
    emit(
      CheckMaintenanceSuccessState(maintenance),
    );
  }
}
