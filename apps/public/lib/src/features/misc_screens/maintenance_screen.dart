import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../route_manager/route_imports.gr.dart';
import '../../common/widgets/maintenance/maintenance_view.dart';
import '../../common/widgets/primary_button.dart';
import '../home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../utils/remote_config_utils/maintenance_status.dart';
import '../../../translations/locale_keys.g.dart';

@RoutePage()
class MaintenanceScreen extends StatefulWidget {
  const MaintenanceScreen({
    required this.data,
    super.key,
  });

  final MaintenanceStatus data;

  @override
  State<MaintenanceScreen> createState() => _MaintenanceScreenState();
}

class _MaintenanceScreenState extends State<MaintenanceScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state != AppLifecycleState.resumed) return;
    context.read<HomeBloc>().add(const CheckMaintenanceEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (_, state) => state is CheckMaintenanceSuccessState,
      listener: _maintenanceStateListener,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Center(
                    child: MaintenanceView(
                      data: widget.data,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24),
                  child: PrimaryButton(
                    text: LocaleKeys.close.tr(),
                    onTap: () {
                      if (context.router.canPop()) {
                        context.back();
                        return;
                      }

                      if (Platform.isAndroid) {
                        SystemNavigator.pop(animated: true);
                        return;
                      }

                      exit(0);
                    },
                    backgroundColor: AppColors.blueLightOld,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _maintenanceStateListener(BuildContext context, HomeState state) {
    if (state is! CheckMaintenanceSuccessState) return;

    final isUnderMaintenance = state.data.isFullMaintenance;
    if (isUnderMaintenance) return;

    unawaited(
      context.router.replaceAll([
        SplashPageRoute(),
      ]),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
