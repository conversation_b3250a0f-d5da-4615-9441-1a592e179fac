import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../common/widgets/appbar/flat_app_bar.dart';
import '../../common/widgets/drawer/app_drawer_part.dart';
import '../../common/widgets/error_reload_placeholder.dart';
import '../../common/widgets/primary_icon_button.dart';
import '../../config/theme_config/theme_constants.dart';
import '../../utils/app_utils/app_message.dart';
import '../../utils/app_utils/downloadHelper.dart';
import '../../utils/app_utils/download_service.dart';
import '../../utils/constants/asset_constants/image_constants.dart';
import '../../utils/constants/color_constants/color_constants.dart';
import '../../../translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xcl;

@RoutePage()
class ExcelPreviewScreen extends StatefulWidget {
  const ExcelPreviewScreen({
    required this.url,
    required this.title,
    super.key,
  });

  final String title;
  final String url;

  @override
  State<ExcelPreviewScreen> createState() => _ExcelPreviewScreenState();
}

class _ExcelPreviewScreenState extends State<ExcelPreviewScreen> {
  String? _filePath;
  String? _error;

  List<List<String>>? _tableData;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) => _downloadFile(),
    );
  }

  void _downloadFile() {
    try {
      DownloadService(
        widget.url,
        notify: false,
        directoryType: DownloadService.supportDir,
        folder: '.reports',
        onDownloadSuccess: (filename, filepath) {
          _postProcessFile(filepath);
        },
        onDownloadFailed: (error) {
          AppMessage.showOverlayNotificationError(
            message: LocaleKeys.unableToDownloadFile.tr(),
          );
        },
      ).start();
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      if (mounted) {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
        context.back();
      }
    }
  }

  void _postProcessFile(String filepath) {
    try {
      final bytes = File(filepath).readAsBytesSync();
      final excel = Excel.decodeBytes(bytes);

      final workbook = xcl.Workbook();

      final data = <List<String>>[];
      for (final sheet in excel.tables.keys) {
        print('_ExcelPreviewScreenState._postProcessFile: $sheet');

        final worksheet = workbook.worksheets[sheet];
        print('_ExcelPreviewScreenState._postProcessFile: $worksheet');

        final rows = excel.tables[sheet]?.rows;
        if (rows == null) continue;

        for (final row in rows) {
          final rowData = row
              .map(
                (cell) => cell?.value?.toString() ?? '',
              )
              .toList();
          data.add(rowData);
        }
      }

      setState(() {
        _tableData = data;
        _filePath = filepath;
      });
    } catch (e) {
      print('_ExcelPreviewScreenState._postProcessFile: $e');
      setState(
        () => _error = LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: FlatAppBar(
                    title: widget.title,
                    bottomPadding: 0,
                  ),
                ),
                if (kDebugMode)
                  PrimaryIconButton(
                    iconPath: AppImages.icDocument,
                    onTap: () => DownloadHelper.openFile(_filePath),
                  ),
              ],
            ),
            Expanded(
              child: Builder(
                builder: (context) {
                  if (_error != null) {
                    return ErrorReloadPlaceholder(error: _error!);
                  }

                  final tableData = _tableData;
                  if (tableData == null) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  tableData.forEach((e) {
                    print('_ExcelPreviewScreenState.build: ${e.length}');
                  });

                  return ColoredBox(
                    color: AppColors.white,
                    child: Theme(
                      data: AppThemeData.lightTheme,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SingleChildScrollView(
                          child: DataTable(
                            columns: tableData.first
                                .map(
                                  (header) => DataColumn(
                                    label: Text(
                                      header,
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                )
                                .toList(),
                            rows: tableData
                                .skip(1) // Skip header row for rows.
                                .map(
                                  (row) => DataRow(
                                    cells: row
                                        .map(
                                          (cell) => DataCell(
                                            Text(cell),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
