import 'dart:async';

import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../home/<USER>/models/update_request_model.dart';
import '../../../../home/<USER>/models/update_response_model.dart';
import '../../data_sources/api_end_points.dart';
import '../../../domain/repositories/settings/setting_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';
import '../../../../../services/http_services.dart';

class SettingRepositoryImpl implements SettingRepository {
  SettingRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<UpdateResponse>> checkForUpdate({
    required UpdateRequest requestBody,
  }) async {
    try {
      final response = await _httpService.postJson(
        SettingsEndPoints.checkAppVersion,
        jsonPayloadMap: requestBody.toJson(),
      );
      if (response.isSuccess) {
        return RepoResponse<UpdateResponse>.success(
          response: UpdateResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<UpdateResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<UpdateResponse>.error(
        errorMessage: e.toString(),
      );
    }
  }

}
