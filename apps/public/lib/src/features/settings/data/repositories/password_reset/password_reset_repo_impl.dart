import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import '../../../../../common/models/response_models/repo_response.dart';
import '../../data_sources/api_end_points.dart';
import '../../models/request/password_reset/password_reset_request.dart';
import '../../models/response/password_reset/password_reset_response.dart';
import '../../../domain/repositories/password_reset/password_reset_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';
import '../../../../../services/http_services.dart';
import '../../../../../../translations/locale_keys.g.dart';

class PasswordResetRepositoryImpl implements PasswordResetRepository {
  PasswordResetRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<PasswordResetResponseModel>> resetPassword(
    PasswordResetRequestModel request,
  ) async {
    try {
      final response = await _httpService.putJson(
        SettingsEndPoints.passwordReset,
        jsonPayload: request.toJson(),
      );
      if (response.isSuccess) {
        return RepoResponse<PasswordResetResponseModel>.success(
          response: PasswordResetResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<PasswordResetResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<PasswordResetResponseModel>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
