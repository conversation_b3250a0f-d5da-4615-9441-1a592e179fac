import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import '../../../../../common/models/response_models/repo_response.dart';
import '../../data_sources/api_end_points.dart';
import '../../models/response/profile_update/profile_pic_upload_response.dart';
import '../../../domain/repositories/profile_update/profile_update_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';
import '../../../../../services/http_services.dart';
import '../../../../../../translations/locale_keys.g.dart';

class NameUpdateRepositoryImpl implements ProfileUpdateRepository {
  NameUpdateRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<ProfilePicUpdateResponse>> uploadProfilePic({
    File? attachment,
  }) async {
    try {
      final response = await _httpService.postMultipart(
        SettingsEndPoints.profilePicUpdate,
        filePayload: attachment == null ? {} : {'profile_pic': attachment},
      );
      if (response.isSuccess) {
        return RepoResponse<ProfilePicUpdateResponse>.success(
          response: ProfilePicUpdateResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ProfilePicUpdateResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ProfilePicUpdateResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ProfilePicUpdateResponse>> deleteProfilePic() async {
    try {
      final response = await _httpService.delete(
        SettingsEndPoints.profilePicUpdate,
      );
      if (response.isSuccess) {
        return RepoResponse<ProfilePicUpdateResponse>.success(
          response: ProfilePicUpdateResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ProfilePicUpdateResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ProfilePicUpdateResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
