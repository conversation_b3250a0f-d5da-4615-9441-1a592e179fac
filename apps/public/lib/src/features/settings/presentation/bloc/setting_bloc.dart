import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../config/dependancy_injection/injection_container.dart';
import '../../../home/<USER>/models/update_request_model.dart';
import '../../../home/<USER>/models/update_response_model.dart';
import '../../data/models/response/password_reset/password_reset_response.dart';
import '../../data/models/response/profile_update/profile_pic_upload_response.dart';
import '../../data/models/response/setting/default_setting_response.dart';
import '../../data/models/response/setting/setting_response.dart';
import '../../domain/repositories/settings/setting_repository_imports.dart';
import '../../../../utils/app_utils/app_info.dart';
import '../../../../../translations/locale_keys.g.dart';

part 'setting_event.dart';
part 'setting_state.dart';

class SettingBloc extends Bloc<SettingEvent, SettingState> {
  SettingBloc() : super(SettingLoadingState()) {
    on<DefaultSettingLoadEvent>(_onLoadDefaultSetting);
    on<CheckForApkUpdateEvent>(_onCheckUpdate);
  }

  Future<void> _onLoadDefaultSetting(
    DefaultSettingLoadEvent event,
    Emitter<SettingState> emit,
  ) async {
    try {
      // HiveUtilsSettings.updateAppSettings();
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        SettingFailureState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  FutureOr<void> _onCheckUpdate(CheckForApkUpdateEvent event, Emitter<SettingState> emit) async {
    try {
      emit(const UpdateCheckingState());

      final AppInfo appInfo = await AppInfo.getAppInfo();
      final apkVersion = appInfo.version;
      final currentPlatform = Platform.isAndroid ? 'android' : 'ios';

      final response = await servicelocator<SettingRepository>().checkForUpdate(
        requestBody: UpdateRequest(
          currentAppVersion: apkVersion,
          currentPlatform: currentPlatform,
        ),
      );
      if (response.isSuccess) {
        emit(UpdateCheckSuccessState(data: response.response!));
      } else {
        emit(UpdateCheckFailed(error: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(UpdateCheckFailed(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }
}
