import 'dart:async';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../../../route_manager/route_imports.gr.dart';
import '../../../../common/widgets/app_radio_button.dart';
import '../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../common/widgets/bottom_sheet_top_notch.dart';
import '../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../config/app_config/secret.dart';
import '../../../domains/presentation/bloc/domains_bloc.dart';
import '../../../home/<USER>/models/update_response_model.dart';
import '../bloc/setting_bloc.dart';
import '../../../../utils/app_utils/app_update.dart';
import '../../../../utils/app_utils/device_type.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/extentions/widget_extensions.dart';
import '../../../../utils/hive_utils/api_cache/api_cache.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

@RoutePage()
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  ValueNotifier<PackageInfo?> packageInfo = ValueNotifier(null);

  bool isAppUpdateAvailable = false;
  UpdateResponse? appUpdateResponse;

  @override
  void initState() {
    super.initState();

    PackageInfo.fromPlatform().then((value) {
      packageInfo.value = value;
    });
    context.read<SettingBloc>().add(const DefaultSettingLoadEvent());

    _checkForUpdate();
  }

  Future<void> _checkForUpdate() async {
    AppUpdate.check(context);
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;
    return Scaffold(
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (appDrawerController.value.visible) {
            appDrawerController.hideDrawer();
          } else if (!didPop) {
            context.back();
          }
        },
        child: _body(context, isLightMode),
      ),
    );
  }

  Widget _body(BuildContext context, bool isLightMode) {
    return AppDrawer(
      child: Scaffold(
        body: ValueListenableBuilder(
          valueListenable: HiveUtilsSettings.textSizeFactorListenable,
          builder: (context, box, child) {
            return Column(
              children: [
                FlatAppBar(
                  title: LocaleKeys.settings.tr(),
                ),
                Expanded(
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: isLightMode ? AppColors.whiteOrBlack : AppColors.blueShade32,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          child: ListView(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            children: [
                              _item(
                                AppImages.icLanguage,
                                LocaleKeys.language.tr(),
                                context,
                                isLightMode,
                                () {
                                  _languageBottomSheet(context, isLightMode);
                                },
                              ),
                              _item(
                                AppImages.icTextSize,
                                LocaleKeys.textSize.tr(),
                                context,
                                isLightMode,
                                () {
                                  _textSizeBottomSheet(context, isLightMode);
                                },
                              ),
                              BlocConsumer<SettingBloc, SettingState>(
                                listener: (context, state) {
                                  if (state is UpdateCheckSuccessState) {
                                    appUpdateResponse = state.data;
                                    isAppUpdateAvailable = state.hasUpdate;
                                  }
                                },
                                builder: (context, state) {
                                  return _item(
                                    AppImages.updateicon,
                                    LocaleKeys.appUpdate.tr(),
                                    context,
                                    isLightMode,
                                    () {
                                      context.pushRoute(
                                        AppUpdateScreenRoute(
                                          isFromSettings: true,
                                          appUpdateResponse: appUpdateResponse,
                                        ),
                                      );
                                    },
                                    hasUpdate: isAppUpdateAvailable,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        ValueListenableBuilder(
                          valueListenable: packageInfo,
                          builder: (context, i, w) {
                            return Padding(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _versionString(),
                                    style: TextStyle(
                                      color: isLightMode ? AppColors.grey : AppColors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ).wrapInConstraintBox(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _item(
    String icon,
    String title,
    BuildContext context,
    bool isLightMode,
    VoidCallback? onTap, {
    bool hasUpdate = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: Center(
                      child: SvgPicture.asset(
                        icon,
                        colorFilter: const ColorFilter.mode(
                          AppColors.greyShade4,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                  Text(
                    title,
                    style: TextStyle(
                      color: isLightMode ? AppColors.blueGreyShade1 : AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (hasUpdate)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      height: 8,
                      width: 8,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.red,
                      ),
                    ),
                  const Spacer(),
                  RotatedBox(
                    quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                    child: SvgPicture.asset(
                      AppImages.icArrowRight,
                      colorFilter: ColorFilter.mode(
                        HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
                        BlendMode.srcIn,
                      ),
                      height: 14,
                      width: 14,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              color: !isLightMode ? AppColors.white : null,
              height: 1,
              indent: 24,
              endIndent: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _primarySecondary(
    String label,
    VoidCallback onTap, {
    bool? isLoading,
  }) {
    return ElevatedButton(
      onPressed: onTap,
      child: isLoading != null && isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                color: AppColors.white,
              ),
            )
          : Text(label),
    );
  }

  Widget _buttonSecondary(String label, VoidCallback onTap) {
    return TextButton(
      onPressed: onTap,
      child: Text(label),
    );
  }

  void _languageBottomSheet(BuildContext context, bool isLightMode) {
    final List<Locale> languages = [
      const Locale('en', ''),
      const Locale('ar', ''),
    ];

    int selected = languages.indexWhere(
      (element) => element.languageCode == context.locale.languageCode,
    );

    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Directionality(
              textDirection: ui.TextDirection.ltr,
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: isLightMode ? Colors.white : AppColors.blueShade32,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20),
                      topLeft: Radius.circular(20),
                    ),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(24, 10, 24, 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const BottomSheetTopNotch(),
                      const SizedBox(height: 15),
                      Align(
                        alignment: DeviceType.isDirectionRTL(context) ? Alignment.centerRight : Alignment.centerLeft,
                        child: Text(
                          LocaleKeys.language.tr(),
                          style: TextStyle(
                            color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      IntrinsicHeight(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              flex: 3,
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      selected = 0;
                                    });
                                  },
                                  borderRadius: BorderRadius.circular(10),
                                  child: Container(
                                    clipBehavior: Clip.antiAlias,
                                    decoration: ShapeDecoration(
                                      color: selected == 0
                                          ? isLightMode
                                              ? AppColors.blueShade24.withValues(alpha: .1)
                                              : AppColors.blueShade37
                                          : isLightMode
                                              ? AppColors.white
                                              : AppColors.blueShade36,
                                      shape: RoundedRectangleBorder(
                                        side: BorderSide(
                                          color: isLightMode
                                              ? selected == 0
                                                  ? AppColors.blueShade22
                                                  : AppColors.blueShade36
                                              : Colors.transparent,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          flex: 3,
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child: AppRadioButton(
                                                  key: UniqueKey(),
                                                  isChecked: selected == 0,
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 16,
                                              ),
                                              Text(
                                                'English',
                                                style: TextStyle(
                                                  color: isLightMode ? AppColors.grey : AppColors.white,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 12,
                                        ),
                                        Flexible(
                                          child: SvgPicture.asset(
                                            AppImages.icEn,
                                            colorFilter: ColorFilter.mode(
                                              isLightMode
                                                  ? selected == 0
                                                      ? AppColors.blueShade22
                                                      : AppColors.grey
                                                  : AppColors.white,
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 20,
                            ),
                            Flexible(
                              flex: 3,
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      selected = 1;
                                    });
                                  },
                                  borderRadius: BorderRadius.circular(10),
                                  child: Container(
                                    clipBehavior: Clip.antiAlias,
                                    decoration: ShapeDecoration(
                                      color: selected == 1
                                          ? Color(
                                              isLightMode ? 0xFFE9F3FF : 0x4C2587FC,
                                            )
                                          : isLightMode
                                              ? AppColors.white
                                              : AppColors.blueShade36,
                                      shape: RoundedRectangleBorder(
                                        side: BorderSide(
                                          color: isLightMode
                                              ? selected == 1
                                                  ? AppColors.blueShade22
                                                  : AppColors.greyShade1
                                              : Colors.transparent,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          flex: 3,
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.stretch,
                                            children: [
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child: AppRadioButton(
                                                  key: UniqueKey(),
                                                  isChecked: selected == 1,
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 12,
                                              ),
                                              Text(
                                                'عربي',
                                                style: TextStyle(
                                                  color: isLightMode ? AppColors.grey : AppColors.white,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  height: 0,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 12,
                                        ),
                                        Flexible(
                                          child: SvgPicture.asset(
                                            AppImages.icAr,
                                            colorFilter: ColorFilter.mode(
                                              isLightMode
                                                  ? selected == 1
                                                      ? AppColors.blueShade22
                                                      : AppColors.grey
                                                  : AppColors.white,
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 45,
                      ),
                      _primarySecondary(LocaleKeys.done.tr(), () async {
                        if (!context.mounted) return;

                        await context.setLocale(languages[selected]);
                        await HiveUtilsSettings.setAppLanguage(languages[selected].languageCode);
                        await HiveApiCacheBox.instance.clearTmp();

                        if (context.mounted) {
                          // context.read<DomainsBloc>().add(const DomainsInitEvent());
                          await Navigator.maybePop(context);
                          unawaited(AutoRouter.of(context).replaceAll(
                            [HomeNavigationRoute(), const SettingsScreenRoute()],
                            updateExistingRoutes: false,
                          ));
                        }
                      }),
                      const SizedBox(
                        height: 20,
                      ),
                      _buttonSecondary(LocaleKeys.cancel.tr(), () {
                        Navigator.maybePop(context);
                      }),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _textSizeBottomSheet(
    BuildContext context,
    bool isLightMode,
  ) async {
    double value = HiveUtilsSettings.textSizeFactor;

    if (mounted) {
      await showModalBottomSheet<void>(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: isLightMode ? Colors.white : AppColors.blueShade32,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20),
                      topLeft: Radius.circular(20),
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 10),
                    const BottomSheetTopNotch(),
                    const SizedBox(height: 15),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                      ),
                      child: Text(
                        LocaleKeys.textSize.tr(),
                        style: TextStyle(
                          color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    Stack(
                      children: [
                        Positioned.fill(
                          child: Center(
                            child: Container(
                              height: 15,
                              margin: const EdgeInsets.symmetric(horizontal: 16),
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: ShapeDecoration(
                                color: isLightMode ? const Color(0xFFEAF3FF) : AppColors.blackShade4,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: Row(
                                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    height: 8,
                                    width: 8,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: AppColors.blue,
                                    ),
                                  ),
                                  const Spacer(),
                                  Container(
                                    height: 8,
                                    width: 8,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: AppColors.blue,
                                    ),
                                  ),
                                  const Spacer(),
                                  Container(
                                    height: 8,
                                    width: 8,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: AppColors.blue,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SfSliderTheme(
                          data: const SfSliderThemeData(
                            activeLabelStyle: TextStyle(color: Colors.transparent),
                            inactiveLabelStyle: TextStyle(
                              color: Colors.transparent,
                            ),
                          ),
                          child: SfSlider(
                            min: 0.7,
                            max: 1.3,
                            value: value,
                            interval: .3,
                            stepSize: .3,
                            activeColor: Colors.transparent,
                            inactiveColor: Colors.transparent,
                            thumbIcon: SizedBox(
                              height: 26,
                              width: 26,
                              child: FittedBox(
                                child: SvgPicture.asset(
                                  AppImages.icRadioOn,
                                  colorFilter: isLightMode
                                      ? const ColorFilter.mode(
                                          AppColors.blueShade22,
                                          BlendMode.srcIn,
                                        )
                                      : null,
                                ),
                              ),
                            ),
                            onChanged: (dynamic v) {
                              value = double.parse('$v');
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            LocaleKeys.Ab.tr(),
                            style: TextStyle(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          Text(
                            LocaleKeys.Ab.tr(),
                            style: TextStyle(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                              fontSize: 17,
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          Text(
                            LocaleKeys.Ab.tr(),
                            style: TextStyle(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                              fontSize: 21,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 50),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _primarySecondary(LocaleKeys.done.tr(), () async {
                            await HiveUtilsSettings.setTextSizeFactor(value);
                            if (context.mounted) {
                              await Navigator.maybePop(context);
                            }
                          }),
                          const SizedBox(
                            height: 20,
                          ),
                          _buttonSecondary(LocaleKeys.cancel.tr(), () {
                            Navigator.maybePop(context);
                          }),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              );
            },
          );
        },
      );
    }

    setState(() {});
  }

  String _versionString() {
    String version = '${packageInfo.value?.appName ?? '-'}  |  V${packageInfo.value?.version ?? '-'}';

    if (!Secret.isProduction) {
      version += ' | ${Secret.appInstance.displayName}';
    }

    return version;
  }
}
