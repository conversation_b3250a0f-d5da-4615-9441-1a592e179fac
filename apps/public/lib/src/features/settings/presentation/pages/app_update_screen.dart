
import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../../common/widgets/appbar/slim_appbar.dart';
import '../../../../common/widgets/error_reload_placeholder.dart';
import '../../../home/<USER>/models/update_response_model.dart';
import '../../../home/<USER>/bloc/home_bloc/home_bloc.dart';
import '../bloc/setting_bloc.dart';
import '../../../../utils/app_utils/app_message.dart';
import '../../../../utils/app_utils/app_update.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/extentions/date_time_extensions.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../utils/styles/app_text_styles.dart';
import '../../../../../translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class AppUpdateScreen extends StatefulWidget {
  const AppUpdateScreen({
    required this.isFromSettings,
    required this.appUpdateResponse,
    super.key,
  });

  final bool isFromSettings;
  final UpdateResponse? appUpdateResponse;


  @override
  State<AppUpdateScreen> createState() => _AppUpdateScreenState();

  static Future<void> navToStore() async {
    try {
      Uri? storeUri;
      if (Platform.isIOS) {
        storeUri = Uri.parse('https://apps.apple.com/us/app/bayaan-app/id6499339305');
      } else if (Platform.isAndroid) {
        storeUri = Uri.parse('https://play.google.com/store/apps/details?id=ae.gov.scad.bayaan.open');
      }
      if (storeUri != null && await canLaunchUrl(storeUri)) {
        unawaited(launchUrl(storeUri));
      } else {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
      }
    } catch (e) {
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
    }
  }
}

class _AppUpdateScreenState extends State<AppUpdateScreen> {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  bool? isUpdateAvailable;
  PackageInfo? packageInfo;

  UpdateResponse? appUpdateResponse;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if(widget.appUpdateResponse != null) {
        appUpdateResponse = widget.appUpdateResponse;
      }
      isUpdateAvailable = widget.appUpdateResponse?.error == null;

      packageInfo = await PackageInfo.fromPlatform();
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          SlimAppBar(
            title: LocaleKeys.appUpdate.tr(),
            onBack: context.maybePop,
          ),
          if (packageInfo != null)
            Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child:
              BlocConsumer<SettingBloc, SettingState>(
                listener: (context, state) {
                  if (state is UpdateCheckSuccessState) {
                    appUpdateResponse = state.data;
                    if (state.hasUpdate) {
                      isUpdateAvailable = true;
                    } else {
                      isUpdateAvailable = null;
                      setState(() { });
                    }
                  }
                },
                builder: (context, state) {
                  if (state is UpdateCheckingState) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is UpdateCheckFailed) {
                    return ErrorReloadPlaceholder(
                      error: state.error,
                      onReload: _checkForAppUpdate,
                    );
                  }

                  if (isUpdateAvailable == true) {
                    return _updateAvailable();
                  } else {
                    return _noUpdateAvailable();
                  }
                },
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _button(),
    );
  }

  Widget _button(){
    if(isUpdateAvailable == null) {
      return const SizedBox();
    }

    return Visibility(
      visible: isUpdateAvailable != null,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
        child: BlocBuilder<HomeBloc, HomeState>(
          builder: (context, state) {
            return ElevatedButton(
              onPressed: () {
                if (isUpdateAvailable!) {
                  AppUpdateScreen.navToStore();
                } else {
                  _checkForAppUpdate();
                }
              },
              child: Text(
                isUpdateAvailable!
                    ? LocaleKeys.updateNow.tr()
                    : LocaleKeys.checkForUpdates.tr(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _updateAvailable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 30,vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.greyShade1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                flex: 2,
                child: _versionChip(
                  title: LocaleKeys.currentVersion.tr(),
                  version: '${packageInfo?.version}',
                ),
              ),
              Expanded(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 30),
                    child: RotatedBox(
                      quarterTurns:
                          HiveUtilsSettings.isLanguageArabic ? 2 : 0,
                      child: SvgPicture.asset(AppImages.rightboldArrow,
                        colorFilter: isLightMode
                            ? null
                            : const ColorFilter.mode(
                                AppColors.blueShade7,
                                BlendMode.srcIn,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: _versionChip(
                  title: LocaleKeys.newVersion.tr(),
                  version: appUpdateResponse?.appVersion ?? '',
                  bgColor: AppColors.blueShade24.withValues(alpha: .1),
                  textColor: isLightMode
                  ?AppColors.blueShade31
                  :AppColors.blueShade7,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        Text(
          LocaleKeys.releaseDate.tr(),
          style: AppTextStyles.s14w5cBlack
              .copyWith(color:
          isLightMode
              ? AppColors.blackTextTile
              : AppColors.white,),
        ),
        const SizedBox(height: 8),
        Text(
          DateTime.tryParse('${appUpdateResponse?.releaseDate}')
                  ?.toFormattedDateTimeString('dd-MMM-yyyy') ??
              appUpdateResponse?.releaseDate ??
              '-',
          style: AppTextStyles.s12w4cGreyShade4.copyWith(color:
          isLightMode
              ? AppColors.grey
              : AppColors.greyShade1,
          ),
        ),
        const SizedBox(height: 18),
        Text(
          LocaleKeys.whatsNew.tr(),
          style: AppTextStyles.s14w5cBlack
              .copyWith(color: isLightMode
              ? AppColors.blackTextTile
              : AppColors.greyShade1,),
        ),
        const SizedBox(height: 10),
        Expanded(
          child: SingleChildScrollView(
            child: HtmlWidget(
              appUpdateResponse?.changeLog ?? '-',
              textStyle: AppTextStyles.s12w4cGreyShade4.copyWith(
                color: isLightMode ? AppColors.grey : AppColors.greyShade1,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _noUpdateAvailable() {
    final isRtl = HiveUtilsSettings.isLanguageArabic;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Align(
          alignment: isRtl ? Alignment.centerRight : Alignment.centerLeft,
          child: _versionChip(
            title: LocaleKeys.currentVersion.tr(),
            version: '${packageInfo?.version}',
            crossAxisAlignment: CrossAxisAlignment.start,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          LocaleKeys.releaseDate.tr(),
          style: AppTextStyles.s14w5cBlack
              .copyWith(color:
          isLightMode
              ? AppColors.blackTextTile
              : AppColors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          DateTime.tryParse('${widget.appUpdateResponse?.releaseDate}')
              ?.toFormattedDateTimeString('dd-MMM-yyyy') ??
              widget.appUpdateResponse?.releaseDate ??
              '-',
          style: AppTextStyles.s12w4cGreyShade4.copyWith(color: AppColors.grey),
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 100),
              child: Column(
                children: [
                  SvgPicture.asset(AppImages.updateDialogeImage),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    LocaleKeys.yourVersionIsUpToDate.tr(),
                    style: isLightMode
                        ? AppTextStyles.s12w4cGreyShade4
                            .copyWith(color: AppColors.grey)
                        : AppTextStyles.s12w4cGreyShade4,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _versionChip({
    required String title,
    required String version,
    Color? bgColor = Colors.transparent,
    Color? textColor,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return Column(
      crossAxisAlignment: crossAxisAlignment,
      children: [
        Text(
          title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.s14w5cBlack.copyWith(
            color: isLightMode ? AppColors.blackTextTile : AppColors.white,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: bgColor,
            border: Border.all(color: AppColors.greyShade1),
            borderRadius: BorderRadius.circular(70),
          ),
          child: Text(
            'V $version',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppTextStyles.s12w4cGreyShade4.copyWith(
              color: textColor ?? (isLightMode?AppColors.grey:AppColors.greyShade1),
            ),
          ),
        ),
      ],
    );
  }

  void _checkForAppUpdate() {
    AppUpdate.check(context);
  }

}
