import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import '../../../../common/models/response_models/repo_response.dart';
import '../../../products/data/models/product_dashboard.dart';
import '../data_sources/api_end_points.dart';
import '../models/search_ifp_response.dart';
import '../../domain/repositories/search_repository_imports.dart';
import '../../../../services/http_service_impl.dart';
import '../../../../services/http_services.dart';
import '../../../../../translations/locale_keys.g.dart';

class SearchRepositoryImpl implements SearchRepository {
  SearchRepositoryImpl() {
    _httpService = HttpServiceRequests();
  }

  late HttpService _httpService;

  @override
  Future<RepoResponse<List<TableauDashboardResponseItem>>> searchDashboardQuery({
    required String query,
  }) async {
    try {
      final response = await _httpService.get(
        SearchEndPoints.searchDashboards + query,
      );

      if (response.isSuccess) {
        return RepoResponse<List<TableauDashboardResponseItem>>.success(
          response: (response.response['data'] as List<dynamic>)
              .map(
                (e) => TableauDashboardResponseItem.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
        );
      } else {
        return RepoResponse<List<TableauDashboardResponseItem>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<List<TableauDashboardResponseItem>>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<SearchIfpResponse>> searchIfpQuery({
    required String query,
  }) async {
    try {
      final response = await _httpService.postJson(
        SearchEndPoints.searchIfp,
        jsonPayloadMap: {'query': query},
        header: {'Accept-Version':'2.0'},
      );

      if (response.isSuccess) {
        return RepoResponse<SearchIfpResponse>.success(
          response: SearchIfpResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<SearchIfpResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<SearchIfpResponse>.error(errorMessage:  LocaleKeys.somethingWentWrong.tr());
    }
  }
}
