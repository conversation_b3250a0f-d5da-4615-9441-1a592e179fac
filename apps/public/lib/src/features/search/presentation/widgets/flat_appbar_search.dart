import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../utils/app_utils/device_type.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../utils/styles/app_text_styles.dart';
import '../../../../../translations/locale_keys.g.dart';

class FlatAppBarSearch extends StatefulWidget {
  const FlatAppBarSearch({
    required this.textController,
    required this.onSubmitted,
    super.key,
  });

  final TextEditingController textController;
  final void Function(String) onSubmitted;

  @override
  State<FlatAppBarSearch> createState() => _FlatAppBarSearchState();
}

class _FlatAppBarSearchState extends State<FlatAppBarSearch> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.isLightMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.paddingOf(context).top + 8, bottom: 10),
                child: RotatedBox(
                      quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                      child: appDrawerController.drawerButton(
                        lightIcon: isLightMode ? false : true,
                      ),
                    ),
              ),
            ],
          ),
          Row(
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).maybePop();
                },
                borderRadius: BorderRadius.circular(8),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14,
                    vertical: 14,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      RotatedBox(
                        quarterTurns:
                            DeviceType.isDirectionRTL(context) ? 2 : 0,
                        child: SvgPicture.asset(
                          AppImages.icArrowLeft,
                          colorFilter: ColorFilter.mode(
                            isLightMode ? AppColors.blueLight : AppColors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),

                      // Text(
                      //   LocaleKeys.back.tr(),
                      //   style: AppTextStyles.s14w4cBlue,
                      //   textScaler:
                      //       TextScaler.linear(textScaleFactor.value),
                      // ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Hero(
                  tag: 'search_box',
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        color: AppColors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(60),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 2,
                              ),
                              child: TextField(
                                autofocus: true,
                                controller: widget.textController,
                                onSubmitted: widget.onSubmitted,
                                style: const TextStyle(
                                  color: AppColors.black,
                                ),
                                decoration: InputDecoration(
                                  fillColor: AppColors.white,
                                  filled: true,
                                  hintText: LocaleKeys.searchHere.tr(),
                                  hintStyle: AppTextStyles.s14w3cHintColor,
                                ),
                              ),
                            ),
                          ),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(100),
                              onTap: () {
                                widget.onSubmitted(
                                  widget.textController.text,
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 8,
                                ),
                                child: SizedBox(
                                  width: 27,
                                  height: 27,
                                  child: Padding(
                                    padding: const EdgeInsets.all(3),
                                    child: SvgPicture.asset(
                                      AppImages.iconSearchBlack,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
