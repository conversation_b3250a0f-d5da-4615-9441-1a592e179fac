import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../common/models/response_models/repo_response.dart';
import '../../../../config/dependancy_injection/injection_container.dart';
import '../../../products/data/models/product_dashboard.dart';
import '../../../products/data/models/scad_api/publications_response.dart';
import '../../../products/data/models/scad_api/web_report_response.dart';
import '../../../products/domain/repositories/products_repository_imports.dart';
import '../../data/models/search_ifp_response.dart';
import '../../domain/repositories/search_repository_imports.dart';
import '../../../../../translations/locale_keys.g.dart';

part 'search_event.dart';
part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  SearchBloc() : super(SearchInitial()) {
    on<SearchInitialEvent>(_onSearchInitialEvent);
    on<OnSearchDashboardEvent>(_onSearchDashboard);
    on<OnSearchIfpEvent>(_onSearchIfp);
    on<OnSearchWebReportsEvent>(_onSearchWebReports);
    on<OnSearchPublicationsEvent>(_onSearchPublications);
    on<OnSearchDashboardToggleEvent>(_onDashboardToggle);
    on<OnSearchIfpToggleEvent>(_onIfpToggle);
    on<OnSearchPublicationsToggleEvent>(_onPublicationsToggle);
    on<OnSearchWebReportsToggleEvent>(_onWebReportsToggle);
  }

  void _onSearchInitialEvent(
    SearchInitialEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(SearchInitial());
  }

  Future<void> _onSearchDashboard(
    OnSearchDashboardEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<List<TableauDashboardResponseItem>> response =
          await servicelocator<SearchRepository>()
              .searchDashboardQuery(query: event.query);

      if (response.isSuccess) {
        emit(SearchDashboardSuccessState(searchResult: response.response!));
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onSearchIfp(
    OnSearchIfpEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<SearchIfpResponse> response =
          await servicelocator<SearchRepository>()
              .searchIfpQuery(query: event.query);

      if (response.isSuccess) {
        emit(
          SearchIfpSuccessState(
            searchResult: response.response?.result?.contentTypes ?? [],
          ),
        );
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onSearchPublications(
    OnSearchPublicationsEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());

      final RepoResponse<PublicationsResponse> response =
      await servicelocator<ProductsRepository>().scadApiPublications(
        paramList: ['search=${event.query}'],
        pageNo: event.pageNo,
        pageSize: 10,
      );

      if (response.isSuccess) {
        emit(
          SearchPublicationsSuccessState(searchResult: response.response),
        );
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onSearchWebReports(
    OnSearchWebReportsEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      emit(SearchLoadingBeginState());
      final RepoResponse<WebReports> response =
          await servicelocator<ProductsRepository>().scadApiWebReports(
        query: event.query,
        pageNo: event.pageNo,
        pageSize: event.pageSize,
      );

      if (response.isSuccess) {
        emit(
          SearchWebReportsSuccessState(searchResult: response.response),
        );
      } else {
        emit(
          SearchErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(SearchErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    } finally {
      emit(SearchLoadingEndState());
    }
  }

  Future<void> _onDashboardToggle(
    OnSearchDashboardToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchDashboardToggleState(seeMore: event.seeMore));
  }

  Future<void> _onIfpToggle(
    OnSearchIfpToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchIfpToggleState(seeMoreStatusList: event.seeMoreStatusList));
  }

  Future<void> _onPublicationsToggle(
    OnSearchPublicationsToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchPublicationsToggleState(seeMore: event.seeMore));
  }

  Future<void> _onWebReportsToggle(
    OnSearchWebReportsToggleEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(SearchDashboardToggleState(seeMore: event.seeMore));
  }
}
