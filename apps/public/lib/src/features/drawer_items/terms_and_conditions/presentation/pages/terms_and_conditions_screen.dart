import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import '../../../../../common/widgets/app_switcher_tab.dart';
import '../../../../../common/widgets/appbar/common_app_bar.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../data/models/t_and_c_model/response/t_and_c_response.dart';
import '../bloc/t_and_c_bloc.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../../translations/locale_keys.g.dart';

@RoutePage()
class TermsAndConditionsScreen extends StatefulWidget {
  const TermsAndConditionsScreen({
    this.showAppDrawer = false,
    super.key,
  });

  final bool showAppDrawer;

  @override
  State<TermsAndConditionsScreen> createState() => _TermsAndConditionsScreenState();
}

class _TermsAndConditionsScreenState extends State<TermsAndConditionsScreen> {
  final PageController _pageController = PageController();

  final AppSwitcherTabController _appSwitcherTabController = AppSwitcherTabController();

  List<TAndCResponseModel> _tAndCList = [];

  final rtl = HiveUtilsSettings.isLanguageArabic;
  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  void initState() {
    super.initState();
    _loadTermsAndConditions();
  }

  void _loadTermsAndConditions() {
    context.read<TAndCBloc>().add(const TAndCLoadingEvent());
  }

  @override
  Widget build(BuildContext context) {
    return !widget.showAppDrawer ? AppDrawer(child: _body()) : _body();
  }

  Widget _body() {
    return Scaffold(
      body: Column(
        children: [
          CommonAppBar(
            title: LocaleKeys.termsAndConditions.tr(),
            showButtons: !widget.showAppDrawer,
          ),
          Expanded(
            child: BlocConsumer<TAndCBloc, TAndCState>(
              listener: (context, state) {
                if (state is TAndCSuccessState) {
                  _tAndCList = state.tAndCList;
                }
              },
              builder: (context, state) {
                if (state is TAndCLoadingState) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is TAndCFailureState) {
                  return Center(
                    child: ErrorReloadPlaceholder(error: state.error, onReload: _loadTermsAndConditions),
                  );
                } else if (_tAndCList.isEmpty) {
                  return const Center(child: NoDataPlaceholder());
                } else {
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: AppSwitcherTab(
                          controller: _appSwitcherTabController,
                          tabs: _tAndCList
                              .map(
                                (e) => AppSwitcherTabItem(
                                  label: (HiveUtilsSettings.isLanguageEnglish ? e.title : e.titleAr) ?? '',
                                ),
                              )
                              .toList(),
                          onChanged: (index) {
                            _pageController.animateToPage(index,
                                duration: const Duration(milliseconds: 200), curve: Curves.linear);
                          },
                        ),
                      ),
                      Expanded(
                        child: PageView(
                          onPageChanged: _appSwitcherTabController.jumpToTab,
                          controller: _pageController,
                          children: _tAndCList
                              .map(
                                (e) => _buildTAndCContent(
                                  rtl ? e.textContentAr ?? '' : e.textContent ?? '',
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    ],
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  SingleChildScrollView _buildTAndCContent(String content) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HtmlWidget(
              content,
              textStyle: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: isLightMode ? AppColors.grey : AppColors.greyShade4,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
