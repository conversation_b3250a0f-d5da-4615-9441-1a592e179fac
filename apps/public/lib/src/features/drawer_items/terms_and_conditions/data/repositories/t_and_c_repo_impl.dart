import 'dart:async';

import '../../../../../common/models/response_models/repo_response.dart';
import '../data_sources/api_end_points.dart';
import '../models/t_and_c_model/response/t_and_c_list_response.dart';
import '../../domain/repositories/t_and_c_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';

class TAndCRepositoryImpl extends TAndCRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<TAndCListResponseModel>> getTAndCList() async {
    final endpoint = TAndCEndPoints.termsAndConditions;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => TAndCListResponseModel.fromJson(json),
    );
  }
}
