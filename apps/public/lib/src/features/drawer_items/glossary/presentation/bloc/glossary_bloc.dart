import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../data/models/glossary_filter/glossary_filter_domain_model.dart';
import '../../data/models/glossary_filter/glossary_filter_response_model.dart';
import '../../data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
import '../../data/models/glossary_response_model.dart';
import '../../domain/repositories/glossary_repository_imports.dart';
import '../../../../../../translations/locale_keys.g.dart';

part 'glossary_event.dart';
part 'glossary_state.dart';

class GlossaryBloc extends Bloc<GlossaryEvent, GlossaryState> {
  GlossaryBloc(this.glossaryRepository) : super(const GlossaryLoadingState()) {
    on<GlossaryEvent>((event, emit) {});
    on<GlossaryLoadFiltersEvent>(_onGetFilters);
    on<GlossaryLoadDataEvent>(_onGetGlossary);
    // on<GlossaryFilterDomainDropdownEvent>(_onDomainDropdown);
    // on<GlossaryFilterSubDomainDropdownEvent>(_onSubDomainDropdown);
    // on<GlossaryAlphabetSelectedEvent>(_onAplphabetSelection);
    on<GlossaryDoneButtonEvent>(_onFilterDone);
    // on<GlossaryLanguageToggleEvent>(_onLanguageToggle);
    on<GlossaryFilterResetEvent>(_onResetFilterEvent);
    // on<GlossaryFilterDomainDropdownValueUpdateEvent>(_onDomainDropdownValueUpdate);
    // on<GlossaryFilterSubDomainDropdownValueUpdateEvent>(_onSubDomainDropdownUpdateValue);
  }

  ///constructors
  final GlossaryRepository glossaryRepository;

  /// for loading filter list
  Future<void> _onGetGlossary(
    GlossaryLoadDataEvent event,
    Emitter<GlossaryState> emit,
  ) async {
    emit(const GlossaryLoadingState());

    try {
      final RepoResponse<GlossaryResponseModel> response =
          await servicelocator<GlossaryRepository>()
              .getGlossaryList(page: event.page);

      if (response.isSuccess) {
        emit(
          GlossaryDataState(
            glossaryResponse: response.response,
          ),
        );
      } else {
        emit(
          GlossaryErrorState(
            errorText: response.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(GlossaryErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onGetFilters(
    GlossaryLoadFiltersEvent event,
    Emitter<GlossaryState> emit,
  ) async {
    emit(const GlossaryLoadingState());

    try {
      final RepoResponse<GlossaryFilterResponseModel> response =
          await servicelocator<GlossaryRepository>().glossaryFilter();

      if (response.isSuccess) {
        emit(
          GlossaryLoadFiltersSuccessState(
            glossaryFilterResponse: response.response,
          ),
        );
      } else {
        emit(
          GlossaryErrorState(
            errorText: response.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(GlossaryErrorState(errorText:  LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for using the domain dropdown
  // Future<void> _onDomainDropdown(
  //   GlossaryFilterDomainDropdownEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //   final List<GlossaryFilterDomainModel> dataList = event.domainItems;
  //
  //   final List<GlossaryFilterSubDomainModel> subDomainList = [];
  //
  //   String selectedDomains = '';
  //   String selectedSubDomains = '';
  //
  //   if (dataList.isNotEmpty) {
  //     dataList[event.index].isSelected = !dataList[event.index].isSelected;
  //
  //     // Update subDomainList for all selected domains
  //     for (final element in dataList) {
  //       if (element.isSelected) {
  //         subDomainList.addAll(element.items ?? []);
  //         selectedDomains += '${element.name ?? ''}, ';
  //
  //         // Update selectedSubDomains for the selected domain
  //         selectedSubDomains += (element.items ?? [])
  //             .where((subDomain) => subDomain.isSelected)
  //             .map((subDomain) => '${subDomain.name ?? ''}, ')
  //             .join();
  //       } else {
  //         // Unselecting the domain, clear its associated subdomains
  //         for (final subDomain in element.items ?? []) {
  //           subDomain.isSelected = false;
  //         }
  //       }
  //     }
  //
  //     /// Remove the trailing comma and space if any
  //     selectedDomains = selectedDomains.isNotEmpty
  //         ? selectedDomains.substring(0, selectedDomains.length - 2)
  //         : '';
  //     selectedSubDomains = selectedSubDomains.isNotEmpty
  //         ? selectedSubDomains.substring(0, selectedSubDomains.length - 2)
  //         : '';
  //   }
  //
  //   emit(
  //     GlossaryFilterDomainDropdownState(
  //       domainItems: event.domainItems,
  //       selectedDomains: selectedDomains,
  //       subDomainItems: subDomainList,
  //       selectedSubDomains: selectedSubDomains,
  //     ),
  //   );
  // }

  /// function for using the sub domain dropdown
  // Future<void> _onSubDomainDropdown(
  //   GlossaryFilterSubDomainDropdownEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //   final List<GlossaryFilterSubDomainModel> dataList = event.subDomainItems;
  //
  //   String selectedSubDomains = '';
  //
  //   if (dataList.isNotEmpty) {
  //     dataList[event.index].isSelected = !dataList[event.index].isSelected;
  //     for (final element in dataList) {
  //       if (element.isSelected) {
  //         selectedSubDomains += '${element.name ?? ''}, ';
  //       }
  //     }
  //
  //     // Remove the trailing comma and space if any
  //     selectedSubDomains = selectedSubDomains.isNotEmpty
  //         ? selectedSubDomains.substring(0, selectedSubDomains.length - 2)
  //         : '';
  //   }
  //
  //   emit(
  //     GlossaryFilterSubDomainDropdownState(
  //       subDomainList: event.subDomainItems,
  //       selectedValue: selectedSubDomains,
  //     ),
  //   );
  // }

  /// function for using the domain dropdown
  // Future<void> _onDomainDropdownValueUpdate (
  //   GlossaryFilterDomainDropdownValueUpdateEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //
  //
  //   emit(
  //     GlossaryFilterDomainDropdownValueUpdatesState(
  //       domainItems: event.domainItems,
  //       subDomainItems: event.subDomainItems,
  //     ),
  //   );
  // }

  /// function for using the sub domain dropdown
  // Future<void> _onSubDomainDropdownUpdateValue(
  //   GlossaryFilterSubDomainDropdownValueUpdateEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //
  //   emit(
  //     GlossaryFilterSubDomainDropdownValueUpdateState(
  //       subDomainList: event.subDomainItems,
  //       selectedValue: event.selected,
  //     ),
  //   );
  // }

  // /// for selecting the alphabet
  // Future<void> _onAplphabetSelection(
  //   GlossaryAlphabetSelectedEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //   final String selectedAlphabet = event.selectedAlphabet;

  //   emit(const GlossaryLoadingState());

  //   emit(
  //     GlossaryAlphabetSelectedState(
  //       selectedAlphabet: selectedAlphabet,
  //     ),
  //   );
  // }

  /// for sending filter payload
  Future<void> _onFilterDone(
    GlossaryDoneButtonEvent event,
    Emitter<GlossaryState> emit,
  ) async {
    final List<GlossaryFilterDomainModel> domains = event.selectedDomains ?? [];
    final List<GlossaryFilterSubDomainModel> subDomains =
        event.selectedSubDomains ?? [];

    List<String> domainsToApi = [];
    List<String> subDomainsToApi = [];

    if (domains.isNotEmpty) {
      domainsToApi = domains
          .where((domain) => domain.isSelected)
          .map((domain) => domain.name ?? '')
          .toList();
    }

    if (subDomains.isNotEmpty) {
      subDomainsToApi = subDomains
          .where((subDomain) => subDomain.isSelected)
          .map((subDomain) => subDomain.name ?? '')
          .toList();
    }

    try {
      emit(const GlossaryLoadingState());
      final RepoResponse<GlossaryResponseModel> response =
          await servicelocator<GlossaryRepository>().getGlossaryList(
        page: event.page,
        domainList: domainsToApi,
        subDomainList: subDomainsToApi,
        alphabetList:
            event.selectedAlphabet != null ? [event.selectedAlphabet!] : null,
        isAscending: event.isAscending,
        term: event.term,
      );

      if (response.isSuccess) {
        for (final element
            in event.selectedDomains ?? <GlossaryFilterDomainModel>[]) {
          for (final data
              in element.items ?? <GlossaryFilterSubDomainModel>[]) {
            if (!element.isSelected) {
              data.isSelected = false;
            }
          }
        }

        emit(
          GlossaryFilterDoneButtonState(
            selectedDomains: event.selectedDomains,
            selectedSubDomains: event.selectedSubDomains,
            selectedAlphabet: event.selectedAlphabet,
            isAscending: event.isAscending,
            glossaryResponse: response.response!,
            term: event.term ?? '',
          ),
        );
      } else {
        emit(
          GlossaryErrorState(
            errorText: response.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(GlossaryErrorState(errorText:  LocaleKeys.somethingWentWrong.tr()));
    }

  }

  /// for toggling b/w arabic and english
  // Future<void> _onLanguageToggle(
  //   GlossaryLanguageToggleEvent event,
  //   Emitter<GlossaryState> emit,
  // ) async {
  //   final List<GlossaryModel> updatedList = event.updatedList;
  //
  //   updatedList[event.index].isArabic = !updatedList[event.index].isArabic;
  //
  //   emit(const GlossaryLoadingState());
  //
  //   emit(
  //     GlossaryLanguageToggleState(
  //       updatedList: updatedList,
  //     ),
  //   );
  // }

  /// for resetting the filter
  Future<void> _onResetFilterEvent(
    GlossaryFilterResetEvent event,
    Emitter<GlossaryState> emit,
  ) async {
    emit(
      GlossaryFilterResetState(
          selectedAlphabet: event.selectedAlphabet,
          selectedDomain: event.selectedDomain,
          selectedSubDomain: event.selectedSubDomain,
          term: event.term),
    );
  }
}
