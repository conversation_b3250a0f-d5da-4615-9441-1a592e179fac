import 'dart:async';
import 'dart:convert';

import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import '../../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../data/models/glossary_filter/glossary_filter_domain_model.dart';
import '../../data/models/glossary_filter/glossary_filter_response_model.dart';
import '../../data/models/glossary_filter/glossary_filter_sub_domain_model.dart';
import '../../data/models/glossary_model.dart';
import '../../data/models/glossary_response_model.dart';
import '../bloc/glossary_bloc.dart';
import '../widgets/domain_bottom_sheet.dart';
import '../widgets/sub_domain_bottom_sheet.dart';
import '../../../../../utils/app_utils/device_type.dart';
import '../../../../../utils/constants/asset_constants/animation_asset.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../utils/styles/app_text_styles.dart';
import '../../../../../../translations/locale_keys.g.dart';

@RoutePage()
class GlossaryScreen extends StatefulWidget {
  const GlossaryScreen({this.initialSearchTerm = '', super.key});

  final String initialSearchTerm;

  @override
  State<GlossaryScreen> createState() => _GlossaryScreenState();
}

class _GlossaryScreenState extends State<GlossaryScreen> {
  final TextEditingController searchController = TextEditingController();
  ScrollController scrollController = ScrollController();
  List<GlossaryModel> glossaryList = [];
  GlossaryResponseModel? glossaryData;
  GlossaryFilterResponseModel? glossaryFilter;

  bool isAscending = true;
  String? selectedAlphabet;

  String initialSearchTerm = '';
  bool apiLoading = false;
  bool filter = false;
  List<GlossaryFilterDomainModel>? selectedDomains;
  List<GlossaryFilterSubDomainModel>? selectedSubDomains;

  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  void initState() {
    super.initState();
    initialSearchTerm = widget.initialSearchTerm;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController.addListener(_loadData);
      _loadGlossaryFilters();
    });
  }

  void _listScrollUp() {
    try {
      scrollController.animateTo(
        scrollController.position.minScrollExtent,
        duration: const Duration(milliseconds: 200),
        curve: Curves.fastOutSlowIn,
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      //error
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Column(
            children: [
              FlatAppBar(
                title: LocaleKeys.glossary.tr(),
                scrollController: scrollController,
                bottomPadding: 4,
              ),
              Expanded(
                child: BlocConsumer<GlossaryBloc, GlossaryState>(
                  listener: _glossaryBlocListener,
                  builder: (context, state) {
                    if (state is GlossaryLoadingState && glossaryList.isEmpty && !filter) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (state is GlossaryListErrorState) {
                      return Center(
                        child: Text(
                          state.errorText ?? LocaleKeys.somethingWentWrong.tr(),
                        ),
                      );
                    }

                    if (state is GlossaryFilterErrorState) {
                      return Center(
                        child: ErrorReloadPlaceholder(
                          error: state.errorText ?? LocaleKeys.somethingWentWrong.tr(),
                          onReload: _loadGlossaryFilters,
                        ),
                      );
                    }

                    if (state is GlossaryErrorState) {
                      return Center(
                        child: ErrorReloadPlaceholder(
                          error: state.errorText ?? LocaleKeys.somethingWentWrong.tr(),
                          onReload: _loadGlossaryFilters,
                        ),
                      );
                    }

                    return Column(
                      children: [
                        _buildSearchTextField(),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: isLightMode ? AppColors.white : AppColors.blueShade32,
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(20),
                                topLeft: Radius.circular(20),
                              ),
                            ),
                            padding: const EdgeInsets.only(top: 24),
                            child: Column(
                              children: [
                                _buildFilterRow(),
                                const SizedBox(height: 20),
                                _buildAlphabetFilter(),
                                Expanded(
                                  child: Builder(
                                    builder: (context) {
                                      if (state is GlossaryLoadingState && filter) {
                                        return const Center(
                                          child: CircularProgressIndicator(),
                                        );
                                      }

                                      if ((glossaryData?.results ?? []).isEmpty) {
                                        return const Center(
                                          child: NoDataPlaceholder(),
                                        );
                                      }

                                      // return GridView.builder(
                                      //   itemCount: glossaryList.length,
                                      //   controller: scrollController,
                                      //   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                      //     crossAxisCount: DeviceType.isTab() ? 2 : 1,
                                      //     mainAxisSpacing: 16,
                                      //     crossAxisSpacing: 16,
                                      //     mainAxisExtent: 150,
                                      //   ),
                                      //   padding: const EdgeInsets.symmetric(
                                      //     horizontal: 16,
                                      //     vertical: 16,
                                      //   ),
                                      //   itemBuilder: _buildItem,
                                      // );

                                      return ListView.builder(
                                        shrinkWrap: true,
                                        itemCount: glossaryList.length,
                                        controller: scrollController,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 16,
                                        ),
                                        itemBuilder: _buildItem,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (state is GlossaryLoadingState && !filter)
                          const Padding(
                            padding: EdgeInsets.all(8),
                            child: CircularProgressIndicator(),
                          ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchTextField() {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: DeviceType.isTab() ? kTabWidthBreakpoint : double.infinity,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            TextField(
              controller: searchController,
              style: const TextStyle(
                color: AppColors.black,
                fontSize: 16,
              ),
              onChanged: (value) {},
              onSubmitted: (v) {
                _onSearchSubmit();
              },
              decoration: InputDecoration(
                suffixIcon: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: _onSearchSubmit,
                  child: SizedBox.square(
                    dimension: 27,
                    child: Padding(
                      padding: const EdgeInsets.all(14),
                      child: SvgPicture.asset(
                        AppImages.iconSearchBlack,
                      ),
                    ),
                  ),
                ),
                hintText: LocaleKeys.search.tr(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 24),
                hintStyle: TextStyle(
                  color: AppColors.grey,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                isDense: true,
                border: OutlineInputBorder(
                  borderSide: const BorderSide(
                    color: Colors.blueAccent,
                    width: 32,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    color: Colors.white,
                    width: 32,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    color: Colors.white,
                    width: 32,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shape: const RoundedRectangleBorder(
                        side: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                    onPressed: !_isFilterApplied()
                        ? null
                        : () {
                      context.read<GlossaryBloc>().add(
                        const GlossaryFilterResetEvent(
                          selectedDomain: '',
                          selectedSubDomain: '',
                          selectedAlphabet: '',
                          term: '',
                        ),
                      );
                      glossaryList = [];
                      glossaryData = null;
                      selectedDomains = [];
                      selectedSubDomains = [];
                      selectedAlphabet = '';
                      for (final element in glossaryFilter?.domains ?? []) {
                        element.isSelected = false;
                      }

                      context.read<GlossaryBloc>().add(
                        GlossaryDoneButtonEvent(
                          page: (glossaryData?.page ?? 0) + 1,
                          selectedDomains: selectedDomains,
                          selectedSubDomains: selectedSubDomains,
                          selectedAlphabet: selectedAlphabet,
                          isAscending: isAscending,
                          term: '',
                        ),
                      );
                      apiLoading = true;
                    },
                    child: Row(
                      children: [
                        Text(
                          LocaleKeys.reset.tr(),
                        ),
                        const SizedBox(width: 8),
                        if (!_isFilterApplied())
                          SvgPicture.asset(
                            'assets/images/ic_reset.svg',
                            colorFilter: ColorFilter.mode(
                              _isFilterApplied() ? AppColors.blueLight : AppColors.grey.withValues(alpha: 0.6),
                              BlendMode.srcIn,
                            ),
                          )
                        else
                          Lottie.asset(
                            isLightMode ? AnimationAsset.animationSync : AnimationAssetDark.animationSync,
                            width: 16,
                            height: 16,
                            fit: BoxFit.cover,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      constraints: BoxConstraints(
        maxWidth: DeviceType.isTab() ? 400 : double.infinity,
      ),
      child: Row(
        children: [
          BlocBuilder<GlossaryBloc, GlossaryState>(
            builder: (context, state) {
              return InkWell(
                onTap: () {
                  isAscending = !isAscending;
                  filter = true;
                  context.read<GlossaryBloc>().add(
                    GlossaryDoneButtonEvent(
                      page: 1,
                      selectedDomains: glossaryFilter?.domains,
                      selectedSubDomains: selectedSubDomains,
                      selectedAlphabet: selectedAlphabet,
                      isAscending: isAscending,
                      term: searchController.text,
                    ),
                  );
                  apiLoading = true;
                },
                child: SvgPicture.asset(
                  isAscending
                      ? isLightMode
                      ? 'assets/images/sort-enable-light.svg'
                      : 'assets/images/ic-sorting-dark-light.svg'
                      : isLightMode
                      ? AppImages.icSorting
                      : 'assets/images/ic-sorting-dark.svg',
                  height: 36,
                ),
              );
            },
          ),
          const SizedBox(width: 14),
          Expanded(
            child: InkWell(
              onTap: () {
                if (searchController.text.isNotEmpty) {
                  for (final GlossaryFilterDomainModel item in glossaryFilter?.domains ?? []) {
                    item.isSelected = false;
                  }
                }
                showModalBottomSheet<dynamic>(
                  backgroundColor: Colors.transparent,
                  context: context,
                  builder: (BuildContext context) {
                    return DomainBottomSheet(
                      oldSubDomainItems: List.generate(
                          selectedSubDomains?.length ?? 0,
                              (index) => GlossaryFilterSubDomainModel.fromJson(
                              jsonDecode(jsonEncode(selectedSubDomains?[index])) as Map<String, dynamic>)),
                      domainList: List.generate(
                          glossaryFilter?.domains?.length ?? 0, (index) => glossaryFilter!.domains![index]),
                    );
                  },
                );
              },
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(
                  horizontal: 15,
                ),
                decoration: BoxDecoration(
                  color: isLightMode ? AppColors.white : AppColors.blueShade36,
                  borderRadius: BorderRadius.circular(70),
                  border: Border.all(
                    color: isLightMode ? AppColors.greyShade1 : AppColors.blackShade8,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        (selectedDomains ?? [])
                            .firstWhere(
                              (element) => element.isSelected,
                          orElse: () => GlossaryFilterDomainModel(),
                        )
                            .name ??
                            LocaleKeys.select.tr(),
                        style: TextStyle(
                          color: isLightMode
                              ? !(selectedDomains?.any((element) => element.isSelected) ?? false)
                              ? AppColors.greyShade4
                              : AppColors.black
                              : AppColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Opacity(
              opacity: (selectedSubDomains ?? []).isNotEmpty ? 1 : 0.4,
              child: InkWell(
                onTap: () {
                  if ((selectedSubDomains ?? []).isNotEmpty) {
                    if (searchController.text.isNotEmpty) {
                      for (final GlossaryFilterSubDomainModel item in selectedSubDomains ?? []) {
                        item.isSelected = false;
                      }
                    }
                    showModalBottomSheet<dynamic>(
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (BuildContext context) {
                        return SubDomainBottomSheet(
                          domainList: selectedDomains ?? [],
                          subDomainList: List.generate(
                            (selectedSubDomains ?? []).length,
                                (index) => GlossaryFilterSubDomainModel.fromJson(
                              jsonDecode(
                                jsonEncode(
                                  selectedSubDomains?[index],
                                ),
                              ) as Map<String, dynamic>,
                            ),
                          ),
                        );
                      },
                    );
                  }
                },
                child: Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15,
                  ),
                  decoration: BoxDecoration(
                    color: isLightMode ? AppColors.white : AppColors.blueShade36,
                    borderRadius: BorderRadius.circular(70),
                    border: Border.all(
                      color: isLightMode ? AppColors.greyShade1 : AppColors.blackShade8,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          (selectedSubDomains ?? [])
                              .firstWhere(
                                (element) => element.isSelected,
                            orElse: () => GlossaryFilterSubDomainModel(),
                          )
                              .name ??
                              LocaleKeys.select.tr(),
                          style: TextStyle(
                            color: isLightMode
                                ? !(selectedSubDomains?.any((element) => element.isSelected) ?? false)
                                ? AppColors.greyShade4
                                : AppColors.black
                                : AppColors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlphabetFilter() {
    return SizedBox(
      height: 30,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: glossaryData?.alphabets?.keys.map((e) {
            final bool isSelectable = glossaryData?.alphabets?[e] != 0;
            return Padding(
              padding: EdgeInsets.only(
                left: 24,
                right: e == glossaryData?.alphabets?.keys.last ? 24 : 0,
              ),
              child: InkWell(
                onTap: () {
                  if (isSelectable) {
                    context.read<GlossaryBloc>().add(
                      GlossaryAlphabetSelectedEvent(
                        selectedAlphabet: e,
                      ),
                    );

                    if (selectedAlphabet == e) {
                      filter = true;
                      glossaryList = [];
                      context.read<GlossaryBloc>().add(
                        GlossaryDoneButtonEvent(
                          page: 1,
                          selectedDomains: selectedDomains,
                          selectedSubDomains: selectedSubDomains,
                          isAscending: isAscending,
                          term: searchController.text,
                        ),
                      );
                      apiLoading = true;
                    } else {
                      filter = true;
                      glossaryList = [];
                      context.read<GlossaryBloc>().add(
                        GlossaryDoneButtonEvent(
                          page: 1,
                          selectedDomains: selectedDomains,
                          selectedSubDomains: selectedSubDomains,
                          selectedAlphabet: e,
                          isAscending: isAscending,
                          term: searchController.text,
                        ),
                      );
                      apiLoading = true;
                    }
                  }
                },
                child: Container(
                  height: 30,
                  width: 30,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: selectedAlphabet == e ? AppColors.blueLight : Colors.transparent,
                  ),
                  child: Text(
                    e,
                    style: AppTextStyles.s12w4cblueGreyShade1.copyWith(
                      color: selectedAlphabet == e
                          ? AppColors.white
                          : (isSelectable
                          ? isLightMode
                          ? AppColors.blueGreyShade1
                          : AppColors.white
                          : isLightMode
                          ? AppColors.greyShade1
                          : AppColors.greyShade4),
                    ),
                  ),
                ),
              ),
            );
          }).toList() ??
              [],
        ),
      ),
    );
  }

  void _loadData() {
    if (scrollController.position.pixels < scrollController.position.maxScrollExtent) {
      return;
    }

    if (apiLoading) return;

    if (glossaryList.length >= (glossaryData?.totalCount ?? 0)) return;

    context.read<GlossaryBloc>().add(
      GlossaryDoneButtonEvent(
        page: (glossaryData?.page ?? 0) + 1,
        selectedDomains: selectedDomains,
        selectedSubDomains: selectedSubDomains,
        selectedAlphabet: selectedAlphabet,
        isAscending: isAscending,
        term: '',
      ),
    );
    apiLoading = true;
  }

  Widget _buildItem(BuildContext context, int index) {
    return Container(
      decoration: BoxDecoration(
        color: !isLightMode ? AppColors.blueShade36 : null,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: isLightMode ? AppColors.greyShade1 : Colors.transparent,
        ),
      ),
      margin: const EdgeInsets.only(bottom: 14),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  HiveUtilsSettings.isLanguageArabic
                      ? glossaryList[index].titleAr ?? ''
                      : glossaryList[index].titleEn ?? '',
                  style: AppTextStyles.s16w5cBlackShade1.copyWith(
                    color: !isLightMode ? Colors.white : null,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          ExpandableNotifier(
            child: ScrollOnExpand(
              scrollOnCollapse: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ExpandablePanel(
                    theme: const ExpandableThemeData(
                      headerAlignment: ExpandablePanelHeaderAlignment.center,
                      tapBodyToCollapse: true,
                      hasIcon: false,
                    ),
                    header: const SizedBox(),
                    collapsed: Row(
                      children: [
                        Expanded(
                          child: Text(
                            HiveUtilsSettings.isLanguageArabic
                                ? glossaryList[index].descriptionAr ?? ''
                                : glossaryList[index].descriptionEn ?? '',
                            softWrap: true,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.s14w4cblackShade4.copyWith(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                            ),
                          ),
                        ),
                      ],
                    ),
                    expanded: Text(
                      HiveUtilsSettings.isLanguageArabic
                          ? glossaryList[index].descriptionAr ?? ''
                          : glossaryList[index].descriptionEn ?? '',
                      maxLines: 100,
                      style: AppTextStyles.s14w4cblackShade4.copyWith(
                        color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 6),
                  if (getLineLength(
                    context,
                    HiveUtilsSettings.isLanguageArabic
                        ? glossaryList[index].descriptionAr ?? ''
                        : glossaryList[index].descriptionEn ?? '',
                    HiveUtilsSettings.isLanguageArabic,
                  ) >
                      2)
                    Builder(
                      builder: (context) {
                        final controller = ExpandableController.of(
                          context,
                          required: true,
                        )!;
                        return InkWell(
                          onTap: controller.toggle,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                controller.expanded ? LocaleKeys.showLess.tr() : LocaleKeys.showMore.tr(),
                                style: AppTextStyles.s14w4cBlue
                                    .copyWith(color: !isLightMode ? AppColors.blueLightOld : null),
                                overflow: TextOverflow.ellipsis,
                              ),
                              Icon(
                                controller.expanded
                                    ? Icons.keyboard_arrow_up_rounded
                                    : Icons.keyboard_arrow_down_rounded,
                                size: 18,
                                color: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _glossaryBlocListener(BuildContext context, GlossaryState state) {
    if (state is GlossaryLoadFiltersSuccessState) {
      if (filter) {
        filter = false;
        glossaryList = [];
      }
      glossaryFilter = state.glossaryFilterResponse;

      if (initialSearchTerm.isNotEmpty) {
        searchController.text = initialSearchTerm;

        context.read<GlossaryBloc>().add(
          GlossaryDoneButtonEvent(
            page: 1,
            selectedDomains: const [],
            selectedSubDomains: const [],
            selectedAlphabet: selectedAlphabet,
            isAscending: isAscending,
            term: searchController.text,
          ),
        );
        initialSearchTerm = '';
      } else {
        context.read<GlossaryBloc>().add(
          GlossaryDoneButtonEvent(
            page: 1,
            selectedDomains: selectedDomains,
            selectedSubDomains: selectedSubDomains,
            isAscending: isAscending,
          ),
        );
      }
      apiLoading = true;
    }

    if (state is GlossaryDataState) {
      if (filter) {
        filter = false;
        glossaryList = [];
      }
      glossaryData = state.glossaryResponse;

      if (glossaryData?.page == 1) {
        glossaryList = [];
        glossaryList.addAll(glossaryData?.results ?? []);
      } else {
        (glossaryData?.results ?? []).removeWhere(
              (element) => glossaryList.any((e) => element.titleEn == e.titleEn),
        );

        glossaryList.addAll(glossaryData?.results ?? []);
      }
    } else if (state is GlossaryAlphabetSelectedState) {
      selectedAlphabet = state.selectedAlphabet;
    } else if (state is GlossaryFilterDoneButtonState) {
      if (filter) {
        filter = false;
        glossaryList = [];
        _listScrollUp();
      }
      selectedAlphabet = state.selectedAlphabet;
      isAscending = state.isAscending ?? true;
      glossaryData = state.glossaryResponse;
      if (glossaryData?.page == 1) {
        glossaryList = glossaryData?.results ?? [];
      } else {
        (glossaryData?.results ?? []).removeWhere(
              (element) => glossaryList.any((e) => element.titleEn == e.titleEn),
        );
        glossaryList.addAll(glossaryData?.results ?? []);
      }
      searchController.text = state.term;

      selectedDomains = state.selectedDomains;
      selectedSubDomains = state.selectedSubDomains;
      apiLoading = false;
    } else if (state is GlossaryFilterResetState) {
      // selectedDomain = state.selectedDomain;
      selectedAlphabet = state.selectedAlphabet;
      searchController.text = state.term;
      // subDomainItems?.clear();
      glossaryList = [];
      selectedDomains = [];

      for (final element in glossaryFilter?.domains ?? <GlossaryFilterDomainModel>[]) {
        element.isSelected = false;

        for (final data in element.items ?? <GlossaryFilterSubDomainModel>[]) {
          data.isSelected = false;
        }
      }

      selectedSubDomains = [];
    }
  }

  int getLineLength(BuildContext context, String text, bool isArabic) {
    final span = TextSpan(text: text, style: AppTextStyles.s14w4cblackShade4);
    final tp = TextPainter(
      text: span,
      textScaler: TextScaler.linear(HiveUtilsSettings.textSizeFactor),
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 76);
    final numLines = tp.computeLineMetrics().length;
    return numLines;
  }

  bool _isFilterApplied() {
    return (selectedDomains ?? []).any((e) => e.isSelected) ||
        (selectedSubDomains ?? []).any((e) => e.isSelected) ||
        (selectedAlphabet ?? '').isNotEmpty ||
        searchController.text.isNotEmpty ||
        (glossaryFilter?.domains ?? []).any((element) => element.isSelected);
  }

  void _loadGlossaryFilters() {
    context.read<GlossaryBloc>().add(const GlossaryLoadFiltersEvent());
  }

  void _onSearchSubmit() {
    context.read<GlossaryBloc>()
      ..add(
        GlossaryFilterResetEvent(
          selectedDomain: '',
          selectedSubDomain: '',
          selectedAlphabet: '',
          term: searchController.text,
        ),
      )
      ..add(
        GlossaryDoneButtonEvent(
          page: 1,
          selectedDomains: const [],
          selectedSubDomains: const [],
          selectedAlphabet: selectedAlphabet,
          isAscending: isAscending,
          term: searchController.text,
        ),
      );
    apiLoading = true;
  }
}
