import 'package:json_annotation/json_annotation.dart';
import 'glossary_model.dart';

part 'glossary_response_model.g.dart';

@JsonSerializable()
class GlossaryResponseModel {
  GlossaryResponseModel({
    this.totalCount,
    this.page,
    this.limit,
    this.alphabets,
    this.results,
  });

  factory GlossaryResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryResponseModelFromJson(json);

  @Json<PERSON>ey(name: 'totalCount')
  int? totalCount;
  @<PERSON><PERSON><PERSON>ey(name: 'page')
  int? page;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'limit')
  int? limit;
  @<PERSON>son<PERSON>ey(name: 'alphabets')
  Map<String, int>? alphabets;
  @<PERSON>son<PERSON>ey(name: 'results')
  List<GlossaryModel>? results;

  Map<String, dynamic> toJson() => _$GlossaryResponseModelToJson(this);
}
