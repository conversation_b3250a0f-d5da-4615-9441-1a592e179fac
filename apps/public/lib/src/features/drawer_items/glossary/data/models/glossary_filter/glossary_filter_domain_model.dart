import 'package:json_annotation/json_annotation.dart';
import 'glossary_filter_sub_domain_model.dart';

part 'glossary_filter_domain_model.g.dart';

@JsonSerializable()
class GlossaryFilterDomainModel {
  GlossaryFilterDomainModel({
    this.name,
    this.items,
    this.isSelected = false,
  });

  factory GlossaryFilterDomainModel.fromJson(Map<String, dynamic> json) =>
      _$GlossaryFilterDomainModelFromJson(json);

  @Json<PERSON>ey(name: 'name')
  String? name;
  @<PERSON>sonKey(name: 'items')
  List<GlossaryFilterSubDomainModel>? items;
  @J<PERSON><PERSON><PERSON>(name: 'is_selected')
  bool isSelected;

  Map<String, dynamic> toJson() => _$GlossaryFilterDomainModelToJson(this);
}
