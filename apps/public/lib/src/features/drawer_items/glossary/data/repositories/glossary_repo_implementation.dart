import 'dart:async';

import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../../common/types.dart';
import '../datasources/glossary_end_points.dart';
import '../models/glossary_filter/glossary_filter_response_model.dart';
import '../models/glossary_filter_payload_model.dart';
import '../models/glossary_response_model.dart';
import '../../domain/repositories/glossary_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';
import '../../../../../utils/extentions/string_extentions.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';

class GlossaryRepoImplementation extends GlossaryRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<GlossaryResponseModel>> getGlossaryList({
    required int page,
    List<String>? domainList,
    List<String>? subDomainList,
    List<String>? alphabetList,
    bool? isAscending = true,
    String? term,
  }) async {
    JSONObject filter = {
      'TOPIC_EN': domainList ?? [],
      'THEME_EN': subDomainList ?? [],
      'TITLE_EN': alphabetList ?? [],
    };
    if (HiveUtilsSettings.isLanguageArabic) {
      filter = {
        'TOPIC_AR': domainList ?? [],
        'THEME_AR': subDomainList ?? [],
        'TITLE_AR': alphabetList ?? [],
      };
    }
    final GlossaryFilterPayloadModel payload = GlossaryFilterPayloadModel(
      filters: filter,
      sortBy: {'alphabetical': isAscending == true ? 'ASC' : 'DESC'},
    );

    final endpoint = '${GlossaryEndPoints.glossaryListEndPoint.setUrlParams({
          'page': '$page',
        })}${(term ?? '').isNotEmpty ? '&term=$term' : ''}';

    final cacheKey = getCacheKey(
      endpoint,
      payload: payload.toJson(),
    );

    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        jsonPayloadMap: payload.toJson(),
      ),
      parseResult: (json) => GlossaryResponseModel.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<GlossaryFilterResponseModel>> glossaryFilter() async {
    final endpoint = GlossaryEndPoints.glossaryFilterEndPoint;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => GlossaryFilterResponseModel.fromJson(json),
    );
  }
}
