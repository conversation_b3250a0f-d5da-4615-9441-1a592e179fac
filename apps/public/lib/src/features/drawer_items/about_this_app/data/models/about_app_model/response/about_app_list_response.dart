import 'package:json_annotation/json_annotation.dart';
import 'about_app_response.dart';

part 'about_app_list_response.g.dart';

@JsonSerializable()
class AboutAppListResponseModel {
  AboutAppListResponseModel({
    this.aboutAppListResponse,
  });

  factory AboutAppListResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AboutAppListResponseModelFromJson(json);

  @Json<PERSON>ey(name: 'data')
  List<AboutAppResponseModel>? aboutAppListResponse;

  Map<String, dynamic> toJson() => _$AboutAppListResponseModelToJson(this);
}
