import 'dart:async';

import '../../../../../common/models/response_models/repo_response.dart';
import '../data_sources/api_end_points.dart';
import '../models/about_app_model/response/about_app_list_response.dart';
import '../../domain/repositories/about_app_repository_imports.dart';
import '../../../../../services/http_service_impl.dart';

class AboutAppRepositoryImpl extends AboutAppRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<AboutAppListResponseModel>> aboutAppData() async {
    final endpoint = AboutAppEndPoints.aboutUs;
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        AboutAppEndPoints.aboutUs,
      ),
      parseResult: (json) => AboutAppListResponseModel.fromJson(json),
    );
  }
}
