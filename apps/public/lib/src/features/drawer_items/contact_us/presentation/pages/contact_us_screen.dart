import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../../../../../common/widgets/appbar/common_app_bar.dart';
import '../../../../../common/widgets/contact_card.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../utils/app_utils/app_message.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../../translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen>
    with TickerProviderStateMixin {
  static const String contactUsPhone = '800 555';
  static const String contactUsEmail = '<EMAIL>';

  @override
  Widget build(BuildContext context) {
    final bool isLightMode =
        HiveUtilsSettings.isLightMode;
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            CommonAppBar(title: LocaleKeys.contactSCAD.tr()),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Row(
                      children: [
                        Expanded(
                          child: ContactCard(
                            iconPath: isLightMode
                                ? AppImages.icCall
                                : AppImages.icCallDark,
                            contact: contactUsPhone,
                            onTap: () async {
                              await launchToCall(
                                'tel',
                                contactUsPhone.replaceAll(' ', ''),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          child: ContactCard(
                            iconPath: isLightMode
                                ? AppImages.icMail
                                : AppImages.icMailDark,
                            contact: contactUsEmail,
                            onTap: () async {
                              await launchToMail(
                                'mailto',
                                contactUsEmail,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> launchToCall(String scheme, String path) async {
    try {
      final Uri launchUri = Uri(
        scheme: scheme,
        path: path,
      );
      await canLaunchUrl(launchUri).then((bool result) async {
        if (result) {
          await launchUrl(launchUri);
        }
      });
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
    }
  }

  Future<void> launchToMail(String scheme, String path) async {
    try {
      final Uri emailLaunchUri = Uri(
        scheme: scheme,
        path: path,
      );
      await launchUrl(emailLaunchUri);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
    }
  }

}
