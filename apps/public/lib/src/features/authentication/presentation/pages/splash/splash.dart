import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mute/flutter_mute.dart' as fm;
import 'package:lottie/lottie.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:real_volume/real_volume.dart';
import 'package:sound_mode/sound_mode.dart';
import 'package:sound_mode/utils/ringer_mode_statuses.dart';

import '../../../../../../route_manager/route_imports.gr.dart';
import '../../../../../config/app_config/secret.dart';
import '../../../../../utils/app_utils/app_cache.dart';
import '../../../../../utils/app_utils/secure_device_utils.dart';
import '../../../../../utils/constants/asset_constants/animation_asset.dart';
import '../../../../../utils/constants/asset_constants/audio_assets.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/api_cache/api_cache.dart';
import '../../../../../utils/hive_utils/hive_utils_persistent.dart';
import '../../../../../utils/remote_config_utils/remote_config_utils.dart';

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({this.toAnimateLogo = true, super.key});

  final bool toAnimateLogo;

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  ValueNotifier<PackageInfo?> packageInfo = ValueNotifier(null);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await HiveUtilsPersistent.initUuid();

      unawaited(
        PackageInfo.fromPlatform().then((value) {
          packageInfo.value = value;
        }),
      );

      if (await SecureDeviceUtils.isSecureDevice(context)) {
        await HiveApiCacheBox.instance.checkAndClearCache();
        await HiveApiCacheBox.instance.clearTmp();
        await AppCache.clearAll();

        if (mounted) {
          if (widget.toAnimateLogo) {
            unawaited(
              Future<void>.delayed(const Duration(milliseconds: 200)).then((value) {
                if (mounted && context.routeData.name == SplashPageRoute.name) {
                  playAudio();
                }
              }),
            );
          }

          final maintenance = await RemoteConfigUtils.getMaintenanceStatusSync(forceFetch: true);
          unawaited(
            Future<void>.delayed(const Duration(seconds: 5)).then((value) {
              if (mounted) {
                if(maintenance.isFullMaintenance) {
                  AutoRouter.of(context).replaceAll([MaintenanceScreenRoute(data: maintenance)]);
                } else {
                  AutoRouter.of(context).replaceAll([HomeNavigationRoute()]);
                }
              }
            }),
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.maxFinite,
        height: double.maxFinite,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: <Color>[
              AppColors.blueShade3,
              AppColors.blueShade2,
            ],
          ),
        ),
        child: Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Positioned.fill(
              child: Image(
                height: MediaQuery.sizeOf(context).height * .25,
                fit: BoxFit.fitWidth,
                alignment: Alignment.bottomCenter,
                image: const AssetImage(AppImages.bgSplashBottom),
              ),
            ),
            Center(
              child: Lottie.asset(
                AnimationAsset.animationBayaanLogo,
                repeat: false,
              ),
            ),
            Positioned(
              bottom: 0,
              child: ValueListenableBuilder(
                valueListenable: packageInfo,
                builder: (context, i, w) {
                  return Padding(
                    padding: const EdgeInsets.all(24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          ' v${packageInfo.value?.version ?? ''}'
                          '${Secret.isProduction ? '' : ' ${Secret.appInstance.displayName}'}',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _getRingerStatusNormal() async {
    final fm.RingerMode ringerMode = await fm.FlutterMute.getRingerMode();

    return ringerMode == fm.RingerMode.normal;

    RingerModeStatus ringerStatus = RingerModeStatus.unknown;

    await Future.delayed(const Duration(seconds: 1), () async {
      try {
        ringerStatus = await SoundMode.ringerModeStatus;
      } catch (err) {
        ringerStatus = RingerModeStatus.unknown;
      }
    });

    return ringerStatus == RingerModeStatus.normal;
  }

  Future<void> playAudio() async {
    try {
      if (!await _getRingerStatusNormal()) {
        return;
      }

      double? vol = await RealVolume.getCurrentVol(StreamType.MUSIC);

      if (vol == null) {
        return;
      } else if (vol >= 0.8) {
        vol = .05;
      } else if (vol >= 0.5) {
        vol = .2;
      } else if (vol >= 0.3) {
        vol = .4;
      } else if (vol >= 0.2) {
        vol = 1;
      }
      await AudioPlayer().play(AssetSource(AudioAsset.startupSound), volume: vol);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
