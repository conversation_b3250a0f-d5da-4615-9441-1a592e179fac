import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';

import '../../../../../translations/locale_keys.g.dart';
import '../../../../common/models/response_models/repo_response.dart';
import '../../../../services/http_service_impl.dart';
import '../../../../utils/extentions/string_extentions.dart';
import '../../domain/repositories/chat_with_sme_repository_imports.dart';
import '../datasources/chat_with_sme_end_points.dart';
import '../models/chat_thread/chat_thread_create_model.dart';
import '../models/chat_thread/chat_thread_list_response.dart';
import '../models/faq/faq_list_model.dart';
import '../models/message/feedback_response_model.dart';
import '../models/message/message_list_model.dart';
import '../models/message/send_message_model.dart';

class ChatWithSmeRepoImplementation extends ChatWithSmeRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<FaqListModel>> getFaqList({
    required String domainFilter,
  }) async {
    final endpoint = ChatWithSmeEndPoints.faqListEndPoint.setUrlParams({'domain_filter': domainFilter});
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => FaqListModel.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<ChatThreadCreateModel>> createChatThread({
    required String domain,
    required int domainId,
    required String theme,
    required String subTheme,
    required String subject,
  }) async {
    try {
      final response = await _httpService.postJson(
        ChatWithSmeEndPoints.createChatThreadEndPoint,
        jsonPayloadMap: {
          'domain': domain,
          'domain_id': domainId,
          'theme': theme,
          'sub_theme': subTheme,
          'subject': subject,
        },
      );
      if (response.isSuccess) {
        return RepoResponse<ChatThreadCreateModel>.success(
          response: ChatThreadCreateModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<ChatThreadCreateModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ChatThreadCreateModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ChatThreadListResponse>> getChatThreadList({required String offset}) async {
    try {
      final response = await _httpService.get(
        ChatWithSmeEndPoints.chatThreadListEndPoint.setUrlParams(
          {'offset': offset},
        ),
      );
      if (response.isSuccess) {
        return RepoResponse<ChatThreadListResponse>.success(
          response: ChatThreadListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ChatThreadListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ChatThreadListResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<MessageListModel>> getMessageList({
    required String chatThreadId,
  }) async {
    final String endPoint = ChatWithSmeEndPoints.messageListEndPoint.setUrlParams(
      {'chatThreadId': chatThreadId},
    );

    try {
      final response = await _httpService.get(endPoint);
      if (response.isSuccess) {
        return RepoResponse<MessageListModel>.success(
          response: MessageListModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<MessageListModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<MessageListModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<SendMessageModel>> sendMessage({
    required String chatThreadId,
    required Map<String, dynamic> messageData,
    File? attachment,
  }) async {
    final String endPoint = ChatWithSmeEndPoints.sendMessageEndPoint.setUrlParams(
      {'chatThreadId': chatThreadId},
    );

    try {
      final response = await _httpService.postMultipart(
        endPoint,
        formDataPayload: messageData,
        filePayload: attachment == null ? {} : {'attachment': attachment},
      );
      if (response.isSuccess) {
        return RepoResponse<SendMessageModel>.success(
          response: SendMessageModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<SendMessageModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<SendMessageModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<FeedbackResponseModel>> sendFeedback({
    required String messageId,
    required Map<String, dynamic> messageData,
  }) async {
    final String endPoint = ChatWithSmeEndPoints.sendFeedbackEndPoint.setUrlParams(
      {'messageId': messageId},
    );

    try {
      final response = await _httpService.postJson(
        endPoint,
        jsonPayloadMap: messageData,
      );
      if (response.isSuccess) {
        return RepoResponse<FeedbackResponseModel>.success(
          response: FeedbackResponseModel.fromJson(response.response),
        );
      } else {
        return RepoResponse<FeedbackResponseModel>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<FeedbackResponseModel>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
