import 'package:json_annotation/json_annotation.dart';

import 'faq_model.dart';

part 'faq_list_model.g.dart';

@JsonSerializable()
class FaqListModel {
  FaqListModel({
    this.faqList,
  });

  factory FaqListModel.fromJson(Map<String, dynamic> json) =>
      _$FaqListModelFromJson(json);

  @JsonKey(name: 'data')
  List<FaqModel>? faqList;

  Map<String, dynamic> toJson() => _$FaqListModelToJson(this);
}
