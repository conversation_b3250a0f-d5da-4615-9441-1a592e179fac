import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../translations/locale_keys.g.dart';
import '../../../../common/models/response_models/repo_response.dart';
import '../../../../config/dependancy_injection/injection_container.dart';
import '../../../../utils/app_utils/app_message.dart';
import '../../../domains/data/models/domain_model/domain_model.dart';
import '../../../domains/domain/repositories/domains_repository_imports.dart';
import '../../data/models/chat_thread/chat_thread_create_model.dart';
import '../../data/models/chat_thread/chat_thread_list_response.dart';
import '../../data/models/domain_theme_sub_theme_model.dart';
import '../../data/models/faq/faq_list_model.dart';
import '../../data/models/faq/faq_model.dart';
import '../../data/models/message/feedback_response_model.dart';
import '../../data/models/message/message_list_model.dart';
import '../../data/models/message/message_model.dart';
import '../../data/models/message/send_message_model.dart';
import '../../domain/repositories/chat_with_sme_repository_imports.dart';

part 'chat_with_sme_event.dart';
part 'chat_with_sme_state.dart';

class ChatWithSmeBloc extends Bloc<ChatWithSmeEvent, ChatWithSmeState> {
  ChatWithSmeBloc() : super(const GetChatThreadListLoadingState()) {
    on<ChatWithSmeEvent>((event, emit) {});

    on<GetDomainListEvent>(_onGetDomainListEvent);
    on<GetFaqListEvent>(_onGetFaqListEvent);

    on<GetChatThreadListEvent>(_onGetChatThreadListEvent);
    on<NewChatThreadEvent>(_onChatThreadCreate);


    on<FaqLoadDomainDataEvent>(_onGetDomainDataFaq);
    on<ChatWithSmeThemeDropdownEvent>(_onThemeDropdown);
    on<ChatWithSmeSubThemeDropdownEvent>(_onSubThemeDropdown);
    on<Max60CharacterEvent>(_onMax60Char);
    on<ChatWithSmeLoadInboxEvent>(_onInboxLoad);
    on<ChatWithSmeSendMessageEvent>(_onMessageSent);
    on<ChatWithSmeSendFeedbackEvent>(_onFeedbackSent);
  }

  Future<void> _onGetDomainListEvent(
      GetDomainListEvent event,
      Emitter<ChatWithSmeState> emit,
      ) async {
    emit(const GetDomainListLoadingState());
    try {
      final RepoResponse<List<DomainModel>> response =
      await servicelocator<DomainsRepository>().getDomainIconsList();
      if (response.isSuccess) {
        final List<DomainModel> domainList = [];

        for (final DomainModel element in response.response ?? []) {
          domainList.add(element);
        }

        emit(
          ChatWithSmeDomainDataState(
            domainList: domainList,
          ),
        );
      } else {
        emit(
          FaqDomainListErrorState(error: response.errorMessage),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        FaqDomainListErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }


  Future<void> _onGetFaqListEvent(
    GetFaqListEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(GetFaqLoadingState(domainId: event.domainId));

    try {
      final RepoResponse<FaqListModel> response =
          await servicelocator<ChatWithSmeRepository>()
              .getFaqList(domainFilter: event.domainId);

      if (response.isSuccess) {
        emit(
          GetFaqSuccessState(
            domainId: event.domainId,
            faqList: response.response?.faqList ?? [],
          ),
        );
      } else {
        emit(
          GetFaqErrorState(
            domainId: event.domainId,
            error: response.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(GetFaqErrorState(domainId: event.domainId, error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onGetChatThreadListEvent(
    GetChatThreadListEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const GetChatThreadListLoadingState());

    try {
      final RepoResponse<ChatThreadListResponse> response =
          await servicelocator<ChatWithSmeRepository>().getChatThreadList(offset: event.offset);

      if (response.isSuccess) {
        emit(
          GetChatThreadListSuccessState(
            data: response.response,
          ),
        );
      } else {
        emit(
          GetChatThreadListErrorState(
            error: response.errorMessage,
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(GetChatThreadListErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }


  Future<void> _onChatThreadCreate(
      NewChatThreadEvent event,
      Emitter<ChatWithSmeState> emit,
      ) async {
    try {
      emit(const NewChatThreadLoadingState());

      final RepoResponse<ChatThreadCreateModel> response =
      await servicelocator<ChatWithSmeRepository>().createChatThread(
        domain: event.domain,
        domainId: event.domainId,
        theme: event.theme,
        subTheme: event.subTheme,
        subject: event.subject,
      );

      if (response.isSuccess) {
        emit(
          NewChatThreadSuccessState(
            chatThreadId: response.response?.uuid,
            domain: event.domain,
            domainId: event.domainId,
            theme: event.theme,
            subTheme: event.subTheme,
            subject: event.subject,
            ticketId: response.response?.ticketId,
            chatThreadClosed: response.response?.chatThreadClosed,
            chatDisabled: response.response?.chatDisabled,
          ),
        );
      } else {
        emit(
          NewChatThreadErrorState(
            error: response.errorMessage,
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(NewChatThreadErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for fetching the domain list
  Future<void> _onGetDomainDataFaq(
    FaqLoadDomainDataEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const FaqDomainLoadingState());
    try {
        final List<RepoResponse<dynamic>> responses = await Future.wait([
          servicelocator<DomainsRepository>().getDomainIconsList(),
        ]);
        if (responses.every((element) => element.isSuccess)) {
          final RepoResponse<
              List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
          final List<DomainModel> domainList = response1.response!;

          final List<DomainModel> domainListCreated = [];

            for(final DomainModel domain in domainList) {
                domainListCreated.add(domain);
            }
          emit(
            FaqDomainDataState(
              domainList: domainListCreated,
            ),
          );
        } else {
          emit(
            FaqDomainErrorState(error: responses
                .firstWhere((element) => !element.isSuccess)
                .errorMessage,),
          );

        }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        FaqDomainErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  /// function for using the theme dropdown in creating chat thread
  Future<void> _onThemeDropdown(
    ChatWithSmeThemeDropdownEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    final List<Subdomain> subDomainList = event.subDomainList;

    final List<Subtheme> subThemeList = [];

    for (final element in subDomainList) {
      if (element == event.selectedValue) {
        subThemeList.addAll(element.subthemes ?? []);
      }
    }

    emit(
      ChatWithSmeThemeDropdownState(
        selectedValue: event.selectedValue,
        subThemeList: subThemeList,
      ),
    );
  }

  /// function for using the sub theme dropdown in creating chat thread
  Future<void> _onSubThemeDropdown(
    ChatWithSmeSubThemeDropdownEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(ChatWithSmeSubThemeDropdownState(selectedValue: event.selectedValue));
  }

  /// function for checking max 60 char while creating chat thread
  Future<void> _onMax60Char(
    Max60CharacterEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(
      Max60CharacterState(
        count: event.value?.length.toString() ?? '0',
        actualText: event.value,
      ),
    );
  }


  /// for loading inbox data
  Future<void> _onInboxLoad(
    ChatWithSmeLoadInboxEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeInboxLoadingState());

    try {
      final RepoResponse<MessageListModel> response =
          await servicelocator<ChatWithSmeRepository>()
              .getMessageList(chatThreadId: event.chatThreadId);

      if (response.isSuccess) {
        emit(
          ChatWithSmeInboxDataState(
            messageList: response.response?.messageList ?? [],
            chatThreadId: event.chatThreadId,
          ),
        );
      } else {
        emit(
          ChatWithSmeChatInboxErrorState(
            errorText: response.errorMessage,
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(ChatWithSmeChatInboxErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for sending the message
  Future<void> _onMessageSent(
    ChatWithSmeSendMessageEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    emit(const ChatWithSmeSendLoadingMessageState(isLoading: true));
    try {
      final RepoResponse<SendMessageModel> response =
          await servicelocator<ChatWithSmeRepository>().sendMessage(
        chatThreadId: event.chatThreadId,
        messageData: {
          'data': json.encode({'message': event.message}),
        },
        attachment: event.attachment,
      );

      if (response.isSuccess) {
        final RepoResponse<MessageListModel> chatResponse =
            await servicelocator<ChatWithSmeRepository>()
                .getMessageList(chatThreadId: event.chatThreadId);
        {
          if (chatResponse.isSuccess) {
            emit(
              ChatWithSmeSendMessageState(
                messageList:
                    chatResponse.response?.messageList?.reversed.toList() ?? [],
                uuid: event.chatThreadId,
                chatDisabled: response.response?.chatDisabled ?? false,
                chatThreadClosed: response.response?.chatThreadClosed ?? false,
              ),
            );
            emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));
          }
        }
      } else {
        emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));

        AppMessage.showOverlayNotificationError(message: response.errorMessage);
        // emit(
        //   ChatWithSmeChatInboxErrorState(
        //     errorText: response.errorMessage,
        //   ),
        // );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(const ChatWithSmeSendLoadingMessageState(isLoading: false));

      AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
      // emit(ChatWithSmeChatInboxErrorState(errorText: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  /// function for sending the feedback
  Future<void> _onFeedbackSent(
    ChatWithSmeSendFeedbackEvent event,
    Emitter<ChatWithSmeState> emit,
  ) async {
    try {
      final RepoResponse<FeedbackResponseModel> response =
          await servicelocator<ChatWithSmeRepository>().sendFeedback(
        messageId: event.messageId,
        messageData: event.comment != null
            ? {
                'satisfied': event.isSatisfied,
                'comments': event.comment,
              }
            : {
                'satisfied': event.isSatisfied,
              },
      );

      if (response.isSuccess) {
        emit(
          ChatWithSmeSendFeedbackState(
            isSuccess: response.isSuccess,
            text: response.response?.message ?? '',
            chatDisabled: response.response?.chatDisabled ?? false,
            chatThreadClosed: response.response?.chatThreadClosed ?? false,
          ),
        );
      } else {
        emit(
          ChatWithSmeSendFeedbackState(
            isSuccess: false,
            text: LocaleKeys.somethingWentWrong.tr(),
            chatDisabled: false,
            chatThreadClosed: false,
          ),
        );
      }
    } catch (e,s) {
      Completer<dynamic>().completeError(e,s);
      emit(
        ChatWithSmeSendFeedbackState(
          isSuccess: false,
          text: LocaleKeys.somethingWentWrong.tr(),
          chatDisabled: false,
          chatThreadClosed: false,
        ),
      );
    }
  }
}
