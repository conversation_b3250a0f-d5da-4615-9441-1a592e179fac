import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/no_data_placeholder.dart';
import '../../../../../common/widgets/rounded_dropdown_widget.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../domains/data/models/domain_model/domain_model.dart';
import '../../../data/models/faq/faq_model.dart';
import '../../bloc/chat_with_sme_bloc.dart';
import 'faq_list_widget.dart';

class FaqListPage extends StatefulWidget {
  const FaqListPage({required this.isListAllFaqs, super.key});

  final bool isListAllFaqs;

  @override
  State<FaqListPage> createState() => _FaqListPageState();
}

class _FaqListPageState extends State<FaqListPage>  with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  List<DomainModel> domainList = [];
  ValueNotifier<DomainModel?> selectedDomain = ValueNotifier(null);

  List<FaqModel> faqList = [];

  @override
  void initState() {
    super.initState();

    if(widget.isListAllFaqs){
      selectedDomain.value = DomainModel(domainId: 'general', domainName: 'general', domainIcon: '');
      _loadFaqs();
    } else {
      _getDomainList();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Column(
      children: [
        if (!widget.isListAllFaqs)
          Align(
            alignment: HiveUtilsSettings.isLanguageEnglish
                ? Alignment.centerLeft
                : Alignment.centerRight,
            child: ValueListenableBuilder(
              valueListenable: selectedDomain,
              builder: (context, i, w) {
                return domainList.isEmpty
                    ? const SizedBox()
                    : SizedBox(
                  width: MediaQuery.sizeOf(context).width * .5,
                  child: Column(
                    children: [
                      RoundedDropDownWidget<DomainModel>(
                        boxShadow: true,
                        showBorder: false,
                        isSquare: true,
                        title: LocaleKeys.selectDomain.tr(),
                        items: domainList,
                        value: selectedDomain.value,
                        onChanged: (val) {
                          if (selectedDomain.value == val){
                            return;
                          }
                          selectedDomain.value = val;
                          _loadFaqs();
                        },
                      ),
                      const SizedBox(height: 25),
                    ],
                  ),
                );
              },
            ),
          ),

        Expanded(
          child: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
            listener: (context, state) {
              if (state is GetFaqSuccessState && state.domainId == (selectedDomain.value?.domainId ?? '')) {
                faqList = state.faqList;
              } else if(state is FaqDomainDataState){
                domainList = state.domainList;
                selectedDomain.value = domainList.firstOrNull;
                _loadFaqs();
              }
            },
            builder: (context, state) {
              if (state is FaqDomainLoadingState ||
                  (state is GetFaqLoadingState &&
                      state.domainId ==
                          (selectedDomain.value?.domainId ?? ''))) {
                return const Center(child: CircularProgressIndicator());
              } else if(state is FaqDomainErrorState && selectedDomain.value == null) {
                return ErrorReloadPlaceholder(
                  error: state.error,
                  onReload: _getDomainList,
                );
              } else if (state is GetFaqErrorState &&
                  state.domainId == (selectedDomain.value?.domainId ?? '')) {
                return ErrorReloadPlaceholder(
                  error: state.error,
                  onReload: _loadFaqs,
                );
              }
              return faqList.isEmpty
                  ? const Center(
                child: Padding(
                  padding: EdgeInsets.only(bottom: 150),
                  child: NoDataPlaceholder(),
                ),
              )
                  : SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 150),
                child: FaqListWidget(faqList: faqList),
              );
            },
          ),
        ),

      ],
    );
  }

  void _loadFaqs() {
    context
        .read<ChatWithSmeBloc>()
        .add(GetFaqListEvent(domainId: selectedDomain.value?.domainId ?? ''));
  }

  void _getDomainList() {
    context.read<ChatWithSmeBloc>().add(const FaqLoadDomainDataEvent());
  }
}
