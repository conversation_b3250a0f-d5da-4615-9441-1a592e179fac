import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../../../translations/locale_keys.g.dart';
import '../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../home/<USER>/widgets/home_page_list_toggler/home_page_list_toggler.dart';
import '../widgets/landing/faq_list_page.dart';

@RoutePage()
class FaqPage extends StatefulWidget {
  const FaqPage({
    super.key,
  });


  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage> {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  ValueNotifier<int> faqTabIndex = ValueNotifier(1);
PageController pageController = PageController(initialPage: 1);

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(title: LocaleKeys.faq.tr(),bottomPadding: 0,),
            Expanded(child: _body()),
          ],
        ),
      ),
    );
  }
  Widget _body() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          ValueListenableBuilder(
            valueListenable: faqTabIndex,
            builder: (context, i, w) {
              return Container(
                decoration: BoxDecoration(
                  color: isLightMode
                      ? AppColors.blueShadeTabInset
                      : AppColors.blueShade32,
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: HomePageListToggler(
                        title: LocaleKeys.general.tr(),
                        isSelected: faqTabIndex.value == 0,
                        onTap: () {
                          faqTabIndex.value = 0;
                          pageController.jumpToPage(0);
                        },
                      ),
                    ),
                    Expanded(
                      child: HomePageListToggler(
                        title: LocaleKeys.domains.tr(),
                        isSelected: faqTabIndex.value == 1,
                        onTap: () {
                          faqTabIndex.value = 1;
                          pageController.jumpToPage(1);
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 25),
          Expanded(
            child: PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: pageController,
              children: const [
                FaqListPage(key:Key('general-faq'), isListAllFaqs: true,),
                FaqListPage(key:Key('domain-faq'), isListAllFaqs: false,),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
