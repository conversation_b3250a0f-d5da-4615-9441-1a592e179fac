import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../utils/constants/asset_constants/image_constants.dart';
import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../translations/locale_keys.g.dart';

class IndicatorTypeTag extends StatelessWidget {
  const IndicatorTypeTag.official({super.key})
      : lightAssetName = AppImages.icOfficial2Dark,
        darkAssetName = AppImages.icOfficial2Dark,
        labelLocaleKey = LocaleKeys.officialStatistics;

  const IndicatorTypeTag.experimental({super.key})
      : lightAssetName = AppImages.icExperimental2Light,
        darkAssetName = AppImages.icExperimental2Dark,
        labelLocaleKey = LocaleKeys.experimentalStatistics;

  const IndicatorTypeTag.compare({super.key})
      : lightAssetName = AppImages.icOfficial2Dark,
        darkAssetName = AppImages.icOfficial2Dark,
        labelLocaleKey = LocaleKeys.compareStatistics;

  const IndicatorTypeTag.compute({super.key})
      : lightAssetName = AppImages.icOfficial2Dark,
        darkAssetName = AppImages.icOfficial2Dark,
        labelLocaleKey = LocaleKeys.computeDataBadge;

  const IndicatorTypeTag.insightDiscovery({super.key})
      : lightAssetName = AppImages.icInsightDiscoveryLight,
        darkAssetName = AppImages.icInsightDiscoveryDark,
        labelLocaleKey = LocaleKeys.insightsDiscovery;

  const IndicatorTypeTag.scenarioDrivers({super.key})
      : lightAssetName = AppImages.icScenarioDriver2,
        darkAssetName = AppImages.icScenarioDriver2,
        labelLocaleKey = LocaleKeys.scenarioDrivers;

  const IndicatorTypeTag.forecast({super.key})
      : lightAssetName = AppImages.icForecast2,
        darkAssetName = AppImages.icForecast2,
        labelLocaleKey = LocaleKeys.forecasts;

  final String lightAssetName;
  final String darkAssetName;
  final String labelLocaleKey;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.greyF3F4F6 : AppColors.blueShade36,
        borderRadius: BorderRadius.circular(50),
        border: Border.all(color: isLightMode ? AppColors.greyShade1 : AppColors.grey),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            isLightMode ? lightAssetName : darkAssetName,
            colorFilter: ColorFilter.mode(
              isLightMode ? AppColors.grey : AppColors.white,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            labelLocaleKey.tr(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
