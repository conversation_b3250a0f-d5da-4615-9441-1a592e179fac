import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../../../../common/check_box_text_row.dart';
import '../../../../common/widgets/bottom_sheet_top_notch.dart';
import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../base/constants.dart';
import '../../base/mixin/indicator_filter_adapter_mixin.dart';
import 'expandable_filter_dropdown.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';

class IndicatorFilterBottomSheet extends StatefulWidget {
  const IndicatorFilterBottomSheet._({
    required this.initialFilter,
    required this.filterPanel,
    required this.onFilterApplied,
  });

  final SelectedFilterMap initialFilter;
  final FilterPanel filterPanel;
  final ValueChanged<SelectedFilterMap> onFilterApplied;

  static Future<void> show(
    BuildContext context, {
    required SelectedFilterMap initialFilter,
    required FilterPanel filterPanel,
    required ValueChanged<SelectedFilterMap> onFilterApplied,
  }) async {
    return showModalBottomSheet<dynamic>(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      context: context,
      builder: (BuildContext context) => IndicatorFilterBottomSheet._(
        initialFilter: initialFilter,
        filterPanel: filterPanel,
        onFilterApplied: onFilterApplied,
      ),
    );
  }

  @override
  State<IndicatorFilterBottomSheet> createState() => _IndicatorFilterBottomSheetState();
}

class _IndicatorFilterBottomSheetState extends State<IndicatorFilterBottomSheet> {
  final isLightMode = HiveUtilsSettings.isLightMode;

  final ValueNotifier<int> _selectedDropdownIndexNotifier = ValueNotifier<int>(-1);

  final _selectedFilterMap = <String, List<String>>{};

  @override
  void initState() {
    super.initState();

    _selectedFilterMap
      ..clear()
      ..addAll(widget.initialFilter);
  }

  void _onDropdownToggled(int index) {
    if (_selectedDropdownIndexNotifier.value == index) {
      _selectedDropdownIndexNotifier.value = -1;
    } else {
      _selectedDropdownIndexNotifier.value = index;
    }
  }

  void _onRadioTapped(Properties filter, int index) {
    final options = filter.options;
    final path = filter.path;
    if (options == null || path == null) return;

    setState(
      () => _selectedFilterMap[path] = [options[index]],
    );
  }

  void _onCheckBoxTapped(Properties filter, int index) {
    final options = filter.options;
    final path = filter.path;
    if (options == null || path == null) return;

    final selectedOptions = _selectedFilterMap[path] ?? [];
    final option = options[index];
    if (selectedOptions.contains(option)) {
      selectedOptions.remove(option);
    } else {
      selectedOptions.add(option);
    }

    if (selectedOptions.isEmpty) {
      selectedOptions.add(filter.defaultVal ?? options.first);
    }

    // TODO(Jerin): Handle the case when all options are selected
    // if (path == kObsDt) {
    //   final isAllOptionTapped = kAllOptions.contains(option);
    //   final isAllOptionUnSelected = selectedOptions.length == 1 && kAllOptions.contains(selectedOptions.first);
    //
    //   if (isAllOptionTapped) {
    //     selectedOptions
    //       ..clear()
    //       ..addAll(filter.options ?? []);
    //   } else if (isAllOptionUnSelected) {
    //     selectedOptions
    //       ..clear()
    //       ..addAll(filter.options ?? []);
    //   } else {
    //     final otherOptions = filter.options?.where((e) => !kAllOptions.contains(e)).toList() ?? []
    //       ..sort();
    //     selectedOptions.sort();
    //
    //     final isOtherOptionsExceptAllSelected = jsonEncode(otherOptions) == jsonEncode(selectedOptions);
    //     if (isOtherOptionsExceptAllSelected) {
    //       selectedOptions
    //         ..clear()
    //         ..addAll(filter.options ?? []);
    //     } else {
    //       selectedOptions.removeWhere(
    //         (e) => kAllOptions.contains(e),
    //       );
    //     }
    //   }
    // }

    setState(
      () => _selectedFilterMap[path] = selectedOptions,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const BottomSheetTopNotch(),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.filters.tr(),
            style: TextStyle(
              color: isLightMode ? AppColors.blackShade1 : AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 20),
          Flexible(
            child: SingleChildScrollView(
              child: ValueListenableBuilder(
                valueListenable: _selectedDropdownIndexNotifier,
                builder: (context, selectedDropdownIndex, child) {
                  return Column(
                    children: (widget.filterPanel.properties ?? [])
                        .indexed
                        .map(
                          (entry) => AnimatedSize(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.fastLinearToSlowEaseIn,
                            child: Center(
                              child: _buildExpandableDropdown(entry.$1, entry.$2),
                            ),
                          ),
                        )
                        .toList(),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 24),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildExpandableDropdown(int index, Properties filter) {
    if (!filter.isAllDependencySatisfied(_selectedFilterMap)) {
      return const SizedBox.shrink();
    }

    final path = filter.path ?? '';
    final selectedFilterOptions = _selectedFilterMap[path] ?? [];

    String displayText;
    if (path == kObsDt) {
      final index = selectedFilterOptions.indexWhere(
        (e) => kAllOptions.contains(e.toLowerCase()),
      );
      if (index != -1) {
        displayText = selectedFilterOptions[index];
      } else {
        displayText = selectedFilterOptions.join(', ');
      }
    } else {
      displayText = selectedFilterOptions.join(', ');
    }

    final multiSelectedFilterPath = _selectedFilterMap.entries
        .where(
          (e) => e.value.length > 1 && e.key != kObsDt,
        )
        .firstOrNull
        ?.key;

    final isSingleSelectFilter =
        multiSelectedFilterPath == null ? filter.type == 'radio' : multiSelectedFilterPath != path && path != kObsDt;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: ExpandableFilterDropdown(
        width: (MediaQuery.sizeOf(context).width - 64) / 2.0,
        isSelected: _selectedDropdownIndexNotifier.value == index,
        label: filter.label ?? '',
        displayText: displayText,
        onTap: () => _onDropdownToggled(index),
        children: filter.optionList.indexed.map((entry) {
          final optionIndex = entry.$1;
          final item = entry.$2;
          final isSelected = selectedFilterOptions.contains(item.title);

          return CheckBoxTextRow(
            title: item.title ?? '',
            titleColor: isLightMode ? AppColors.blackTextTile : AppColors.white,
            isSelected: isSelected,
            isRadioButton: isSingleSelectFilter,
            onChanged: isSingleSelectFilter
                ? () => _onRadioTapped(filter, optionIndex)
                : () => _onCheckBoxTapped(filter, optionIndex),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildApplyButton() {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        minimumSize: const Size.fromHeight(43),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      onPressed: () {
        widget.onFilterApplied.call(_selectedFilterMap);
        Navigator.pop(context);
      },
      child: Text(
        LocaleKeys.apply.tr(),
        style: const TextStyle(
          color: AppColors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
