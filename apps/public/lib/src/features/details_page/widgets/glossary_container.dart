import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../main.dart';
import '../../../../route_manager/route_imports.gr.dart';
import '../../../common/widgets/card_custom_clipper.dart';
import '../../../utils/app_utils/device_type.dart';
import '../../../utils/constants/asset_constants/image_constants.dart';
import '../../../utils/constants/color_constants/color_constants.dart';
import '../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../translations/locale_keys.g.dart';

class GlossaryContainer extends StatelessWidget {
  const GlossaryContainer({super.key});

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final rtl = DeviceType.isDirectionRTL(context);

    return Stack(
      clipBehavior: Clip.none,
      children: [
        ClipPath(
          clipper: CardCustomClipper(isRtl: rtl),
          child: Container(
            decoration: BoxDecoration(
              color: !isLightMode
                  ? AppColors.blueShade12.withValues(alpha: 1)
                  : AppColors.blueShade22.withValues(alpha: .8),
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
            child: Row(
              children: [
                SvgPicture.asset(AppImages.icGlossary2),
                const SizedBox(width: 30),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Opacity(
                      opacity: 0.90,
                      child: Text(
                        LocaleKeys.glossary.tr(),
                        style: const TextStyle(
                          color: AppColors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          right: DeviceType.isDirectionRTL(context) ? null : -8.5,
          left: DeviceType.isDirectionRTL(context) ? (isLightMode ? -8.5 : -22) : null,
          top: isLightMode
              ? -5
              : DeviceType.isDirectionRTL(context)
                  ? -5
                  : -20,
          height: isLightMode ? 40 : 60,
          width: isLightMode ? 40 : 60,
          child: InkWell(
            onTap: () => AutoRouter.of(context).push(GlossaryScreenRoute()),
            child: RotatedBox(
              quarterTurns: DeviceType.isDirectionRTL(context) ? -1 : 0,
              child: SvgPicture.asset(
                isLightMode ? 'assets/images/arrow_button_light.svg' : 'assets/images/arrow_button_dark.svg',
              ),
            ),
          ),
        ),
      ],
    );
  }
}
