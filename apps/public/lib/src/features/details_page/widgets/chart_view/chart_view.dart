import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../route_manager/route_imports.dart';
import '../../../../common/functions/indicator_date_setting.dart';
import '../../../../common/types.dart';
import '../../../../common/widgets/charts/column_chart.dart';
import '../../../../common/widgets/charts/spline_chart.dart';
import '../../../../common/widgets/charts/treemap_chart.dart';
import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../config/dependancy_injection/injection_container.dart';
import '../../base/constants.dart';
import '../bottom_sheet/select_data_frequency_bottom_sheet.dart';
import '../bottom_sheet/select_data_presentation_bottom_sheet.dart';
import '../data_table/custom_data_table.dart';
import '../data_table/table_data_grid_source.dart';
import '../../../../utils/app_utils/device_type.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/extentions/string_extentions.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';

class ChartSeriesData {
  ChartSeriesData({
    required this.series,
    this.forecast,
    this.seriesMetaList = const [],
    this.tableFields,
  });

  final SeriesDataList series;
  final SeriesDataList? forecast;
  final List<SeriesMeta> seriesMetaList;
  final List<TableFields>? tableFields;
}

class ChartView extends StatelessWidget {
  ChartView({
    required this.chartSeriesData,
    required this.frequency,
    required this.chartRepresentation,
    this.yAxisLabel,
    this.isFullScreenView = false,
    this.showTooltips = false,
    this.showForecast = false,
    this.enforceLightMode,
    this.isTreemapData = false,
    this.isVertical = true,
    super.key,
  });

  final ChartSeriesData chartSeriesData;
  final ChartDataFrequency frequency;
  final ChartDataRepresentation chartRepresentation;
  final bool isFullScreenView;
  final bool showTooltips;
  final bool showForecast;
  final bool? enforceLightMode;
  final String? yAxisLabel;
  final bool isTreemapData;
  final bool isVertical;

  late final _isLightMode = enforceLightMode ?? HiveUtilsSettings.isLightMode;

  List<Map<String, dynamic>> get _currentTableList => chartSeriesData.series[_tableIndex.value];
  int get _tableListLength => chartSeriesData.series.length;
  final ValueNotifier<int> _tableIndex = ValueNotifier(0);

  @override
  Widget build(BuildContext context) {
    switch (chartRepresentation) {
      case ChartDataRepresentation.line:
        return _buildSplineChart();
      case ChartDataRepresentation.bar:
        return _buildColumnChart();
      case ChartDataRepresentation.table:
        return _buildTableView();
      case ChartDataRepresentation.treemap:
        return _buildTreemap();
    }
  }

  List<List<SplineChartData>> _getSeriesData() {
    return chartSeriesData.series
        .map(
          (e) => e
              .map(
                (e1) => SplineChartData(
                  e1['OBS_DT'].toString(),
                  num.parse('${e1['VALUE'] ?? '0'}'),
                ),
              )
              .toList(),
        )
        .toList();
  }

  (List<List<SplineChartData>>?, List<List<SplineChartData>>?) _getForecastData() {
    final forecast = chartSeriesData.forecast;
    if (forecast == null || forecast.isEmpty) {
      return (null, null);
    }

    final forecastData = <List<SplineChartData>>[];
    final areaForecastData = <List<SplineChartData>>[];

    for (final seriesData in forecast) {
      final List<SplineChartData> low = [];
      final List<SplineChartData> up = [];
      final List<SplineChartData> fc = [];

      for (final e in seriesData) {
        if (e['VALUE_LL'] != null) {
          low.add(
            SplineChartData(
              e['OBS_DT'].toString(),
              num.parse('${e['VALUE'] ?? '0'}'),
              y: num.parse('${e['VALUE_LL'] ?? '0'}'),
            ),
          );
        }
        if (e['VALUE_UL'] != null) {
          up.add(
            SplineChartData(
              e['OBS_DT'].toString(),
              num.parse('${e['VALUE'] ?? '0'}'),
              y: num.parse('${e['VALUE_UL'] ?? '0'}'),
            ),
          );
        }
        fc.add(
          SplineChartData(
            e['OBS_DT'].toString(),
            num.parse('${e['VALUE'] ?? '0'}'),
          ),
        );
      }

      forecastData.add(fc);
      areaForecastData
        ..add(low)
        ..add(up);
    }

    return (forecastData, areaForecastData);
  }

  Widget _buildSplineChart() {
    final seriesChartData = _getSeriesData();

    List<List<SplineChartData>>? forecastChartData;
    List<List<SplineChartData>>? areaForecastChartData;

    final forecast = chartSeriesData.forecast;
    if (forecast != null && forecast.isNotEmpty) {
      final forecastData = _getForecastData();
      forecastChartData = forecastData.$1;
      areaForecastChartData = forecastData.$2;
    }

    try {
      seriesChartData
          .map((e) => e.sort((a, b) => DateTime.parse(a.label!).compareTo(DateTime.parse(b.label!))))
          .toList();
    } finally {}

    return SplineChart(
      showMarker: showTooltips,
      frequency: frequency.en,
      isLightMode: _isLightMode,
      chartDataList: seriesChartData,
      forecastChartDataList: forecastChartData ?? [],
      areaHighlightChartData: areaForecastChartData ?? [],
      showForecast: showForecast,
      yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
    );
  }

  Widget _buildColumnChart() {
    final bool isGrouped = chartSeriesData.series.length > 1;
    double? height;
    if (isGrouped) {
      final context = servicelocator<AppRouter>().navigatorKey.currentContext!;
      if (isVertical) {
        height = MediaQuery.sizeOf(context).height * .4;
      } else {
        height = MediaQuery.sizeOf(context).width - 96;
      }
      final double heightWithTooltip = (chartSeriesData.series.length * 12) + 200;

      if (heightWithTooltip > height) {
        height = heightWithTooltip;
      }
    }
    return SizedBox(
      height: height,
      width: double.infinity,
      child: ColumnChart(
        showMarker: showTooltips,
        frequency: frequency.en,
        isLightMode: _isLightMode,
        isFullscreen: isFullScreenView,
        chartDataList: chartSeriesData.series
            .map(
              (e) => e
                  .map(
                    (e1) => ColumnChartData(
                      e1['OBS_DT'].toString(),
                      double.parse('${e1['VALUE'] ?? '0'}'),
                      null,
                      null,
                    ),
                  )
                  .toList(),
            )
            .toList(),
        yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
      ),
    );
  }

  Widget _buildTableView() {
    return ValueListenableBuilder(
      valueListenable: _tableIndex,
      builder: (context, _, __) {
        SeriesDataList seriesData = [];
        if (isTreemapData) {
          for (final value in chartSeriesData.seriesMetaList) {
            if (double.parse('${value.data?.firstOrNull?['VALUE_PERC_ECO'] ?? '0'}') == 0) {
              continue;
            }
            seriesData.add(value.data?.toList() ?? []);
          }
        } else {
          seriesData = showForecast
              ? [
                  ...[_currentTableList],
                  ...chartSeriesData.forecast ?? [],
                ]
              : [_currentTableList];
        }

        final seriesDataList = List<SeriesData>.from(seriesData)
          ..removeWhere(
            (element) => element.isEmpty,
          );

        final List<TableFields> tableFields = [];

        for (final element in chartSeriesData.tableFields ?? <TableFields>[]) {
          if (seriesDataList.firstOrNull?.firstOrNull?.containsKey(element.path) ?? false) {
            tableFields.add(element);
          }
        }

        if (tableFields.length == 1 && tableFields.first.path == 'OBS_DT') {
          tableFields.clear();
        }

        List<String> keys = [];
        if (tableFields.isEmpty) {
          keys = seriesDataList.firstOrNull?.firstOrNull?.keys
                  .where(
                    (e) => tableHeaderTranslations.containsKey(e),
                  )
                  .toList() ??
              [];
        }

        final filteredTempData = <JSONObject>[];

        for (final seriesData in seriesDataList) {
          for (final data in seriesData) {
            final map = JSONObject.from(data);
            map.forEach((key, value) {
              if (value is double) {
                map[key] = value.toStringAsFixed(3);
              }
            });

            map[kObsDt] = IndicatorDateSetting.setupNameAll(frequency.en, data[kObsDt] as String? ?? '');
            filteredTempData.add(map);
          }
        }

        final dataGridSource = TableDataGridSource(
          tableData: filteredTempData.isNotEmpty ? filteredTempData : (seriesDataList.firstOrNull ?? []),
          columnNames: tableFields.isNotEmpty ? tableFields.map((e) => e.path ?? '').toList() : keys,
        );

        for (int i = 0; i < keys.length; i++) {
          final key = keys[i];
          keys[i] = tableHeaderTranslations.containsKey(key) ? tableHeaderTranslations[key].toString() : key;
        }

        return Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: SizedBox(
                height: 300,
                width: double.maxFinite,
                child: CustomDataTable(
                  headerCells: tableFields.isNotEmpty ? tableFields.map((e) => e.label).toList() : keys,
                  rowsCells: dataGridSource.rows.toList(),
                  isLightMode: _isLightMode,
                  isLandscape: isFullScreenView,
                ),
              ),
            ),
            _buildTableNavigator(),
          ],
        );
      },
    );
  }

  Widget _buildTableNavigator() {
    return ValueListenableBuilder(
      valueListenable: _tableIndex,
      builder: (context, _, __) {
        if (_tableListLength > 1) {
          return Padding(
            padding: const EdgeInsets.all(4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _tableIndex.value <= 0
                        ? null
                        : () {
                            _tableIndex.value = _tableIndex.value - 1;
                          },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(14),
                      child: RotatedBox(
                        quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                        child: SvgPicture.asset(
                          AppImages.icArrowLeft,
                          colorFilter: ColorFilter.mode(
                            _tableIndex.value <= 0
                                ? AppColors.greyShade13
                                : _isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 12,
                ),
                Text(
                  '${_tableIndex.value + 1} / $_tableListLength',
                ),
                const SizedBox(
                  width: 12,
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _tableIndex.value >= _tableListLength - 1
                        ? null
                        : () {
                            _tableIndex.value = _tableIndex.value + 1;
                          },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(14),
                      child: RotatedBox(
                        quarterTurns: DeviceType.isDirectionRTL(context) ? 0 : 2,
                        child: SvgPicture.asset(
                          AppImages.icArrowLeft,
                          colorFilter: ColorFilter.mode(
                            _tableIndex.value >= _tableListLength - 1
                                ? AppColors.greyShade13
                                : _isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  Widget _buildTreemap() {
    return TreemapChart(
      key: Key('treemap.${chartSeriesData.seriesMetaList.map((e) => e.color).join()}'),
      chartSeriesData: chartSeriesData.seriesMetaList,
    );
  }
}
