import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image/image.dart' as img;
import 'package:lottie/lottie.dart';
import '../../../../../route_manager/route_imports.gr.dart';
import '../../../../common/functions/indicator_date_setting.dart';
import '../../../../common/types.dart';
import '../../../../common/widgets/chart_legend.dart';
import '../../../../common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../base/constants.dart';
import '../../base/mixin/indicator_filter_adapter_mixin.dart';
import '../bottom_sheet/select_data_frequency_bottom_sheet.dart';
import '../../../../utils/app_utils/app_message.dart';
import '../../../../utils/app_utils/app_permission.dart';
import '../../../../utils/app_utils/file_utils.dart';
import '../../../../utils/constants/asset_constants/animation_asset.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../utils/styles/app_text_styles.dart';
import '../../../../../translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Border, Column, Row;

part 'download_as_delegates.dart';
part 'download_as_preview_dialog.dart';
part 'worker/download_image.dart';
part 'worker/download_pdf.dart';
part 'worker/download_xls.dart';

class DownloadAsV2 extends StatelessWidget {
  const DownloadAsV2({
    required this.title,
    required this.description,
    required this.seriesList,
    required this.isRestricted,
    required this.isTableView,
    required this.chart,
    required this.legendList,
    IndicatorType? indicatorType,
    this.filter,
    this.tableFieldList,
    this.frequency,
    super.key,
  }) : indicatorType = indicatorType ?? IndicatorType.other;

  const DownloadAsV2.restricted({super.key})
      : title = '',
        description = '',
        seriesList = const [],
        isRestricted = true,
        isTableView = false,
        chart = const SizedBox(),
        legendList = const [],
        tableFieldList = null,
        filter = null,
        frequency = null,
        indicatorType = IndicatorType.other;

  final String title;
  final String description;
  final bool isTableView;
  final Widget chart;
  final List<ChartLegend> legendList;
  final IndicatorType indicatorType;
  final FilterPreview? filter;
  final List<TableFields>? tableFieldList;
  final ChartDataFrequency? frequency;
  final SeriesDataList seriesList;
  final bool isRestricted;

  @override
  Widget build(BuildContext context) {
    if (isRestricted) {
      return const _DownloadRestrictedDelegate();
    }

    final bool hasData = seriesList.expand((e) => e).isNotEmpty;

    return _DownloadEnabledDelegate(
      title: title,
      description: description,
      chart: chart,
      legendList: legendList,
      filter: filter,
      seriesList: seriesList,
      indicatorType: indicatorType,
      tableFieldList: tableFieldList,
      frequency: frequency,
      isTableView: isTableView,
      hasData: hasData,
    );
  }
}
