
import 'package:flutter/material.dart';
import '../../../../common/widgets/domain_icon.dart';
import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';


class DomainAndPersonalizeControls extends StatelessWidget {
  const DomainAndPersonalizeControls({
    required this.indicatorDetails,
    required this.contentType,
    this.hideNotification = false,
    super.key,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final String contentType;
  final bool hideNotification;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Row(
      children: [
        DomainIcon(
          domainIdOrName: indicatorDetails.domainId,
          color: isLightMode ? AppColors.black : AppColors.white,
          padding: const EdgeInsets.all(6),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            indicatorDetails.domain ?? '',
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }
}
