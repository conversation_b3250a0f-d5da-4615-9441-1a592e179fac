import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../common/widgets/bottom_sheet_top_notch.dart';
import '../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../translations/locale_keys.g.dart';

enum ChartDataRepresentation {
  line(LocaleKeys.lineChart, AppImages.icLineChart),
  bar(LocaleKeys.barChart, AppImages.icBarChart),
  table(LocaleKeys.tableView, AppImages.icTableView),
  treemap(LocaleKeys.treeMap, AppImages.icTreeMap);

  const ChartDataRepresentation(this.localeKey, this.imagePath);

  final String localeKey;
  final String imagePath;
}

class SelectDataPresentationBottomSheet extends StatefulWidget {
  const SelectDataPresentationBottomSheet._({
    required this.initialRepresentation,
    required this.onPreview,
    required this.options,
  });

  final List<ChartDataRepresentation> options;
  final ChartDataRepresentation initialRepresentation;
  final ValueChanged<ChartDataRepresentation> onPreview;

  static Future<dynamic> show(
    BuildContext context, {
    required List<ChartDataRepresentation> options,
    required ChartDataRepresentation initialRepresentation,
    required ValueChanged<ChartDataRepresentation> onPreview,
    required ValueChanged<ChartDataRepresentation> onDone,
  }) async {
    final result = await showModalBottomSheet<ChartDataRepresentation>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SelectDataPresentationBottomSheet._(
        options: options,
        initialRepresentation: initialRepresentation,
        onPreview: onPreview,
      ),
    );

    onDone.call(result ?? initialRepresentation);
  }

  @override
  State<SelectDataPresentationBottomSheet> createState() => _DataPresentationBottomSheetState();
}

class _DataPresentationBottomSheetState extends State<SelectDataPresentationBottomSheet> {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  late ChartDataRepresentation _selectedType = widget.initialRepresentation;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const BottomSheetTopNotch(),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.changeDataPresentation.tr(),
            style: TextStyle(
              color: isLightMode ? AppColors.blackShade1 : AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 30),
          Wrap(
            spacing: 70,
            runSpacing: 25,
            children: widget.options
                .map(
                  (chartRepresentation) => _buildOptionTile(chartRepresentation),
                )
                .toList(),
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(43),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            onPressed: () => Navigator.maybePop(context, _selectedType),
            child: Text(
              LocaleKeys.done.tr(),
              style: const TextStyle(
                color: AppColors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile(ChartDataRepresentation chartRepresentation) {
    final isSelected = _selectedType == chartRepresentation;

    return InkWell(
      onTap: () {
        widget.onPreview(chartRepresentation);
        setState(
          () => _selectedType = chartRepresentation,
        );
      },
      child: Column(
        children: [
          Container(
            width: 61,
            height: 61,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: isSelected
                  ? isLightMode
                      ? AppColors.blueShade22
                      : AppColors.blueLightOld
                  : Colors.transparent,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.transparent : AppColors.greyShade1,
              ),
            ),
            child: SvgPicture.asset(
              chartRepresentation.imagePath,
              colorFilter: ColorFilter.mode(
                isSelected ? AppColors.white : AppColors.greyShade4,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            chartRepresentation.localeKey.tr(),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
