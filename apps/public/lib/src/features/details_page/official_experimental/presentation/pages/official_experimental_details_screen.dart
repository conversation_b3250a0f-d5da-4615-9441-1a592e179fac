import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../../route_manager/route_imports.gr.dart';
import '../../../../../common/functions/indicator_date_setting.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../../common/widgets/chart_legend.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/icon_and_title_widget.dart';
import '../../../../../common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import '../../../../../common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../common/widgets/indicator_value_v2.dart';
import '../../../../../common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import '../../../../../common/widgets/user_guide/showcaseview_package/src/enum.dart';
import '../../../../../common/widgets/user_guide/showcaseview_package/src/scroll_helper.dart';
import '../../../../../common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import '../../../base/bloc/details_base_bloc.dart';
import '../../../base/constants.dart';
import '../../../base/mixin/chart_period_filter_mixin.dart';
import '../../../base/mixin/indicator_details_bloc_mixin.dart';
import '../bloc/official_experimental_details_bloc.dart';
import '../../../widgets/bottom_sheet/select_compute_operation_bottom_sheet.dart';
import '../../../widgets/bottom_sheet/select_data_frequency_bottom_sheet.dart';
import '../../../widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import '../../../widgets/chart_action_button.dart';
import '../../../widgets/chart_period_options.dart';
import '../../../widgets/chart_view/chart_view.dart';
import '../../../widgets/chart_view/no_chart_data.dart';
import '../../../widgets/domain_and_personalize_controls/domain_and_personalize_controls.dart';
import '../../../widgets/download_as/download_as.dart';
import '../../../widgets/full_screen_chart_view/full_screen_chart_view.dart';
import '../../../widgets/glossary_container.dart';
import '../../../widgets/indicator_chart_header.dart';
import '../../../widgets/indicator_filter/indicator_filter_button.dart';
import '../../../widgets/indicator_type_tag.dart';
import '../../../widgets/meta_data_widget.dart';
import '../../../../../utils/app_utils/device_type.dart';
import '../../../../../utils/constants/asset_constants/image_constants.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/extentions/date_time_extensions.dart';
import '../../../../../utils/extentions/string_extentions.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../../translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

@RoutePage()
class OfficialExperimentalDetailsScreen extends StatefulWidget {
  const OfficialExperimentalDetailsScreen({
    required this.id,
    required this.contentType,
    required this.title,
    this.screenerPayload,
    super.key,
  });

  final String id;
  final String contentType;
  final String title;
  final JSONObject? screenerPayload;

  @override
  State<OfficialExperimentalDetailsScreen> createState() => _OfficialExperimentalDetailsScreenState();
}

class _OfficialExperimentalDetailsScreenState extends State<OfficialExperimentalDetailsScreen> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  IndicatorDetailsResponse? get _indicatorDetails => context.read<OfficialExperimentalDetailsBloc>().indicatorDetails;

  VisualizationsMeta? get _selectedVisualizationMeta =>
      context.read<OfficialExperimentalDetailsBloc>().selectedVisualizationMeta;

  String? get yAxisLabel => _selectedVisualizationMeta?.yAxisLabel;

  final ScrollController _scrollController = ScrollController();
  final ScreenshotController _screenshotController = ScreenshotController();

  final GlobalKey changeFrequencyButtonKey = GlobalKey(debugLabel: 'changeFrequencyButtonKey');
  final GlobalKey changePresentationButtonKey = GlobalKey(debugLabel: 'changePresentationButtonKey');
  final GlobalKey compareIndicatorsButtonKey = GlobalKey(debugLabel: 'compareIndicatorsButtonKey');
  final GlobalKey computeDataButtonKey = GlobalKey(debugLabel: 'computeDataButtonKey');
  final GlobalKey downloadAsButtonKey = GlobalKey(debugLabel: 'downloadAsButtonKey');
  List<GlobalKey> steps = [];
  BuildContext? myContext;
  bool disableAppbarSlide = HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home;

  SeriesDataList _seriesList = [];

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;

  final _chartRepresentationPreviewNotifier = ValueNotifier<ChartDataRepresentation?>(null);

  final _availablePeriodOptions = <ChartPeriodOption>[];
  final _selectedPeriodOption = ValueNotifier<ChartPeriodOption?>(null);

  final ValueNotifier<bool> _isFullScreenChartViewEnabled = ValueNotifier(true);

  @override
  void initState() {
    super.initState();

    context.read<OfficialExperimentalDetailsBloc>().resetBlocVariables();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      steps = [
        changeFrequencyButtonKey,
        changePresentationButtonKey,
        compareIndicatorsButtonKey,
        computeDataButtonKey,
        downloadAsButtonKey,
      ];
      _loadIndicatorDetails();
    });
  }

  void _loadIndicatorDetails() {
    context.read<OfficialExperimentalDetailsBloc>().add(
          GetIndicatorDetailsEvent(
            id: widget.id,
            contentType: widget.contentType,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: ShowCaseWidget(
          builder: Builder(
            builder: (context) {
              myContext = context;
              disableAppbarSlide = HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home;
              return Stack(
                children: [
                  Column(
                    children: [
                      FlatAppBar(
                        title: widget.title,
                        bottomPadding: 0,
                        isDetailsPage: disableAppbarSlide ? false : true,
                        scrollController: disableAppbarSlide ? null : _scrollController,
                        indicatorType: _indicatorDetails?.getIndicatorType(),
                      ),
                      Expanded(
                        child: BlocConsumer<OfficialExperimentalDetailsBloc, DetailsBaseState>(
                          listenWhen: (_, state) => state is GetIndicatorDetailsSuccessState,
                          buildWhen: (_, state) => state is GetIndicatorDetailsBaseState,
                          listener: (context, state) async {
                            if (state is GetIndicatorDetailsSuccessState) {
                              context.read<OfficialExperimentalDetailsBloc>().initializeDefaultFilter();
                              _setPeriodFilter();

                              WidgetsBinding.instance.addPostFrameCallback((_) async {
                                if (mounted) {
                                  if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
                                    ShowCaseWidget.of(myContext!).startShowCase(steps);
                                    await _scrollController.scrollToWidget(
                                      context,
                                      changePresentationButtonKey,
                                      () => setState(() {}),
                                    );
                                  }
                                }
                              });
                            }
                          },
                          builder: (context, state) {
                            if (state is GetIndicatorDetailsErrorState) {
                              return Center(
                                child: ErrorReloadPlaceholder(error: state.error, onReload: _loadIndicatorDetails),
                              );
                            }

                            if (state is GetIndicatorDetailsLoadingState || _indicatorDetails == null) {
                              return const Center(child: CircularProgressIndicator());
                            }

                            return Column(
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: _bodyContent(state),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  if (disableAppbarSlide && ShowCaseWidget.of(myContext!).activeWidgetId == null)
                    Positioned.fill(child: Container(color: Colors.black45)),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _bodyContent(DetailsBaseState state) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Container(
        constraints: BoxConstraints(minHeight: MediaQuery.sizeOf(context).height),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: DomainAndPersonalizeControls(
                indicatorDetails: _indicatorDetails!,
                contentType: widget.contentType,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 18),
              decoration: BoxDecoration(
                color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 10),
                  _buildChartHeader(),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildIndicatorValues(),
                        const SizedBox(height: 15),
                        Screenshot(
                          key: Key('official.experimental.details.chart.${widget.id}'),
                          controller: _screenshotController,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(minHeight: 200),
                            child: _buildChartAndLegends(),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildPeriodFilter(),
                            const Spacer(),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _buildActionButtons(),
                        _buildDownloadAs(),
                        const SizedBox(height: 30),
                        _buildMetaData(),
                        _buildUpdatedOnAndSource(),
                      ],
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: GlossaryContainer(),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartHeader() {
    final indicatorType = _indicatorDetails?.getIndicatorType();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: IndicatorChartHeader(
        filterBuilder: (context) {
          final bloc = context.read<OfficialExperimentalDetailsBloc>();
          final filterPanel = bloc.filterPanel;

          if (filterPanel is! FilterPanel) {
            return const SizedBox.shrink();
          }

          return IndicatorFilterButton(
            adapter: bloc,
            onFilterApplied: (filterMap) {
              context.read<OfficialExperimentalDetailsBloc>().add(
                    ApplyOfficialExperimentalFilterEvent(payload: filterMap),
                  );
            },
          );
        },
        security: _indicatorDetails?.security,
        tag: indicatorType == IndicatorType.official
            ? const IndicatorTypeTag.official()
            : const IndicatorTypeTag.experimental(),
        fullScreenButtonBuilder: (context) => ValueListenableBuilder(
          valueListenable: _isFullScreenChartViewEnabled,
          builder: (context, isEnabled, child) {
            return Opacity(
              opacity: isEnabled ? 1 : 0.5,
              child: FullScreenExpandButton(
                onTap: isEnabled
                    ? () => FullScreenChartView.show(
                          context: context,
                          title: widget.title,
                          chartBuilder: (context) => _buildChart(isFullScreen: true),
                          legendBuilder: (context) => _buildLegends(),
                        )
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }

  OverView? _overview;

  Widget _buildIndicatorValues() {
    return BlocBuilder<OfficialExperimentalDetailsBloc, DetailsBaseState>(
      buildWhen: (_, state) => state is GetIndicatorOverviewSuccessState || state is GetIndicatorDetailsSuccessState,
      builder: (context, state) {
        if (state is GetIndicatorDetailsSuccessState) {
          context.read<OfficialExperimentalDetailsBloc>().add(
                GetIndicatorOverviewEvent(
                  ids: [widget.id],
                  type: widget.contentType,
                ),
              );
        }

        if (state is GetIndicatorOverviewSuccessState && state.overview.value != null) {
          if (!state.event.ids.contains(widget.id)) {
            _overview = null;
          } else {
            _overview = state.overview;
          }
        }

        return AnimatedSize(
          curve: Curves.fastLinearToSlowEaseIn,
          duration: const Duration(milliseconds: 400),
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: IndicatorValueV2(
              key: UniqueKey(),
              isCardView: true,
              indicatorDetails: _indicatorDetails,
              overView: _overview,
            ),
          ),
        );
      },
    );
  }

  void _setPeriodFilter() {
    _setSeriesList();

    final availableOptions = _indicatorDetails?.indicatorFilters?.firstOrNull?.options
        ?.map(
          (e) => ChartPeriodOption.fromOption(e),
        )
        .toList();

    if (_seriesList.any((e) => e.length > kRecentLength)) {
      availableOptions?.add(
        ChartPeriodOption.recent(),
      );
    }

    _availablePeriodOptions.addAll(availableOptions ?? []);
    _selectedPeriodOption.value = _availablePeriodOptions.lastOrNull;
  }

  Widget _buildUpdatedOnAndSource() {
    if (DeviceType.isTab()) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildDataUpdatedOn(),
          _buildDataSource(),
        ],
      );
    } else {
      return Align(
        alignment: DeviceType.isDirectionRTL(context) ? Alignment.centerRight : Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDataUpdatedOn(),
            const SizedBox(height: 17),
            _buildDataSource(),
            const SizedBox(height: 6),
          ],
        ),
      );
    }
  }

  Widget _buildActionButtons() {
    return Builder(
      builder: (context) {
        context.watch<OfficialExperimentalDetailsBloc>();
        final width = MediaQuery.sizeOf(context).width;
        final hasSeries = _seriesList.any((e) => e.isNotEmpty);
        final timeUnits = _indicatorDetails?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.timeUnit ?? [];
        final hasFilterPanel = _indicatorDetails?.filterPanel is FilterPanel;

        final isComparable = _indicatorDetails?.getIndicatorType() == IndicatorType.official &&
            _indicatorDetails?.isMultiDimension == false &&
            _indicatorDetails?.theme != null &&
            _indicatorDetails?.subtheme != null;

        return Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: IntroWidget(
                    stepKey: changeFrequencyButtonKey,
                    stepIndex: 3,
                    totalSteps: 7,
                    title: LocaleKeys.changeDataFrequency.tr(),
                    description: LocaleKeys.changeDataFrequencyGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomLeft,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.12, left: width * 0.12),
                    onPrevious: () {
                      ShowCaseWidget.of(myContext!).dismiss();
                      context.maybePop();
                    },
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.frequency(
                      enabled: false,
                      onPressed: () => SelectDataFrequencyBottomSheet.show(
                        context,
                        onPreview: (frequency) {
                          // TODO(Jerin): Ask Hafjash
                        },
                        onDone: (frequency) {
                          // TODO(Jerin): Ask Hafjash
                        },
                        timeUnits: timeUnits,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: changePresentationButtonKey,
                    stepIndex: 4,
                    totalSteps: 7,
                    title: LocaleKeys.changeDataPresentation.tr(),
                    description: LocaleKeys.changeDataPresentationGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomLeft,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.34, left: width * 0.34),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.presentation(
                      enabled: hasSeries,
                      onPressed: () async {
                        _chartRepresentationPreviewNotifier.value = _selectedChartRepresentation;

                        await SelectDataPresentationBottomSheet.show(
                          context,
                          options: [
                            ChartDataRepresentation.line,
                            ChartDataRepresentation.bar,
                            ChartDataRepresentation.table,
                          ],
                          initialRepresentation: _selectedChartRepresentation,
                          onPreview: (value) => _chartRepresentationPreviewNotifier.value = value,
                          onDone: (value) => _selectedChartRepresentation = value,
                        );
                        _chartRepresentationPreviewNotifier.value = null;
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: compareIndicatorsButtonKey,
                    stepIndex: 5,
                    totalSteps: 7,
                    title: LocaleKeys.compareIndicators.tr(),
                    description: LocaleKeys.compareDataGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomRight,
                    position: TooltipPosition.top,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.33, left: width * 0.33),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.compare(
                      enabled: isComparable && hasSeries,
                      onPressed: () => context.pushRoute(
                        SelectComparableScreenRoute(
                          indicatorDetails: _indicatorDetails!,
                          screener: widget.screenerPayload,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: computeDataButtonKey,
                    stepIndex: 6,
                    totalSteps: 7,
                    title: LocaleKeys.computeData.tr(),
                    description: LocaleKeys.computeDataGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomRight,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    onNext: () async {
                      await _scrollController.scrollToWidget(context, downloadAsButtonKey, () => setState(() {}));
                    },
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.1, left: width * 0.1),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.compute(
                      enabled: hasFilterPanel && hasSeries,
                      onPressed: () => SelectComputeOperationBottomSheet.show(
                        context,
                        onOperationSelected: (operation) {
                          final filterPanel = _indicatorDetails?.filterPanel as FilterPanel?;
                          final propertiesList = List<Properties>.from(filterPanel?.properties ?? []);

                          context.pushRoute(
                            SelectComputableScreenRoute(
                              indicatorId: _indicatorDetails?.indicatorId ?? '',
                              operation: operation,
                              propertiesList: propertiesList,
                              security: _indicatorDetails?.security,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final seriesListForDownloadAs = _filterPeriod(_seriesList, selectedPeriodOption);

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, value, child) {
            final isRestricted = _indicatorDetails?.security?.id != '0';
            final ChartDataFrequency frequency = IndicatorDateSetting.getChartDataFrequency(
              seriesList: _seriesList,
              indicatorDetails: _indicatorDetails,
            );

            final filter = context.read<OfficialExperimentalDetailsBloc>().filterPreview;

            return IntroWidget(
              stepKey: downloadAsButtonKey,
              stepIndex: 7,
              totalSteps: 7,
              title: LocaleKeys.downloadAs.tr(),
              description: LocaleKeys.downloadAsGuideDesc.tr(),
              arrowAlignment: Alignment.bottomLeft,
              position: TooltipPosition.top,
              crossAxisAlignment: CrossAxisAlignment.center,
              targetBorderRadius: 10,
              isDownArrow: true,
              arrowPadding: const EdgeInsets.only(top: 10),
              targetPadding: const EdgeInsets.all(10),
              onPrevious: () {
                _scrollController.scrollToWidget(context, changePresentationButtonKey, () => setState(() {}));
              },
              child: DownloadAsV2(
                key: Key('${_selectedChartRepresentation.index}'),
                title: _indicatorDetails?.componentTitle ?? '',
                description: _indicatorDetails?.componentSubtitle ?? '',
                seriesList: seriesListForDownloadAs,
                isRestricted: isRestricted,
                isTableView: _selectedChartRepresentation == ChartDataRepresentation.table,
                indicatorType: _indicatorDetails?.getIndicatorType(),
                chart: _buildChart(
                  enforceLightMode: true,
                  showTooltips: true,
                ),
                legendList: _getLegendList(isLightMode: true),
                filter: filter,
                tableFieldList: _indicatorDetails?.tableFields,
                frequency: frequency,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildChartAndLegends({
    bool? enforceLightMode,
    bool showTooltips = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildChart(
          enforceLightMode: enforceLightMode,
          showTooltips: showTooltips,
        ),
        _buildLegends(enforceLightMode: enforceLightMode),
      ],
    );
  }

  Widget _buildChart({
    bool? enforceLightMode,
    bool isFullScreen = false,
    bool showTooltips = false,
  }) {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, selectedChartPreview, child) {
            final chartRepresentation = selectedChartPreview ?? _selectedChartRepresentation;
            _setSeriesList();

            final list = _filterPeriod(_seriesList, selectedPeriodOption);
            final hasData = list.any((e) => e.isNotEmpty);
            if (!hasData) {
              SchedulerBinding.instance.addPostFrameCallback(
                (_) => _isFullScreenChartViewEnabled.value = false,
              );
              return const NoChartData();
            }

            final String freqString = IndicatorDateSetting.setFrequancy(
              l: list,
              indicatorDetails: IndicatorDetailsResponseHelper(_indicatorDetails!),
            )['selectedFrequencyForFilter'] as String;

            SchedulerBinding.instance.addPostFrameCallback(
              (_) => _isFullScreenChartViewEnabled.value = true,
            );
            final frequency = ChartDataFrequency.fromString(freqString);
            return ChartView(
              chartSeriesData: ChartSeriesData(
                series: list,
                tableFields: _indicatorDetails?.tableFields,
              ),
              frequency: frequency,
              chartRepresentation: chartRepresentation,
              enforceLightMode: enforceLightMode,
              showTooltips: showTooltips,
              isFullScreenView: isFullScreen,
              yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
              isVertical: !isFullScreen,
            );
          },
        );
      },
    );
  }

  Widget _buildLegends({bool? enforceLightMode}) {
    final isLightMode = enforceLightMode ?? _isLightMode;

    return ValueListenableBuilder(
      valueListenable: _chartRepresentationPreviewNotifier,
      builder: (context, previewChartType, child) {
        final selectedChartRepresentation = previewChartType ?? _selectedChartRepresentation;

        if (selectedChartRepresentation == ChartDataRepresentation.table) {
          return const SizedBox.shrink();
        }

        return Wrap(
          spacing: 12,
          runSpacing: 8,
          children: _getLegendList(isLightMode: isLightMode),
        );
      },
    );
  }

  List<ChartLegend> _getLegendList({required bool isLightMode}) {
    final selectedFilter = context.watch<OfficialExperimentalDetailsBloc>().dependencyResolvedFilters;
    final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    if (selectedFilter.isEmpty) {
      return [
        ChartLegend(
          label: _indicatorDetails?.componentTitle ?? '',
          color: colorSet.first,
          isLightMode: isLightMode,
        ),
      ];
    }

    final multiFilter = selectedFilter.entries
        .firstWhere(
          (entry) => entry.key != kObsDt && entry.value.length > 1,
          orElse: () => selectedFilter.entries.firstWhere((entry) => entry.key != kObsDt),
        )
        .value;

    return multiFilter.indexed
        .map(
          (e) => ChartLegend(
            label: e.$2,
            color: colorSet[e.$1 % colorSet.length],
            isLightMode: isLightMode,
          ),
        )
        .toList();
  }

  Widget _buildMetaData() {
    final metadata = _indicatorDetails?.metaData ?? [];

    if (metadata.isEmpty) {
      return const SizedBox();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: MetaDataWidget(
        id: widget.id,
        title: widget.title,
        metaData: _indicatorDetails?.metaData,
      ),
    );
  }

  Widget _buildDataUpdatedOn() {
    final IndicatorCategory category = _indicatorDetails?.getIndicatorCategory() ?? IndicatorCategory.other;

    if (category == IndicatorCategory.official) {
      return IconAndTitleWidget(
        icon: AppImages.icCalendar,
        title: LocaleKeys.updatedOn.tr(),
        content: DateTime.tryParse(
              '${_indicatorDetails?.updated}',
            )?.toFormattedDateTimeString('dd/MM/yyyy') ??
            _indicatorDetails?.updated ??
            '_',
      );
    } else if (category == IndicatorCategory.experimental || category == IndicatorCategory.analyticalApps) {
      return IconAndTitleWidget(
        icon: AppImages.icCalendar,
        title: LocaleKeys.updatedOn.tr(),
        content: DateTime.tryParse(
              '${_indicatorDetails?.publicationDate}',
            )?.toFormattedDateTimeString('dd/MM/yyyy') ??
            _indicatorDetails?.publicationDate ??
            '_',
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _buildDataSource() {
    if (_indicatorDetails?.dataSource != null && _indicatorDetails?.dataSource?.trim() != '') {
      return IconAndTitleWidget(
        icon: AppImages.icDocument,
        title: LocaleKeys.source.tr(),
        content: _indicatorDetails?.dataSource ?? '-',
      );
    } else {
      return const SizedBox();
    }
  }

  void _setSeriesList() {
    final seriesDataList = _selectedVisualizationMeta?.seriesMeta!.map((e) => e.data ?? []).toList() ?? [];
    final selectedFilter = context.read<OfficialExperimentalDetailsBloc>().dependencyResolvedFilters;
    if (selectedFilter.isEmpty) {
      _seriesList = seriesDataList;
      return;
    }

    _seriesList.clear();
    final multiFilter = selectedFilter.entries.firstWhere(
      (entry) => entry.key != kObsDt && entry.value.length > 1,
      orElse: () => selectedFilter.entries.firstWhere((entry) => entry.key != kObsDt),
    );

    final otherFilters = selectedFilter.entries.where((e) => e.key != multiFilter.key);

    bool isOtherFiltersSatisfied(JSONObject data) {
      if (otherFilters.isEmpty) return true;

      return otherFilters.every(
        (entry) => entry.value.any(
          (e) => e.toLowerCase() == data[entry.key]?.toLowerCase(),
        ),
      );
    }

    final key = multiFilter.key;
    for (final option in multiFilter.value) {
      final series = <JSONObject>[];
      for (final data in seriesDataList.first) {
        if (data[key].toString().toLowerCase() != option.toLowerCase() || !isOtherFiltersSatisfied(data)) continue;
        series.add(data);
      }

      _seriesList.add(series);
    }
  }

  SeriesDataList _filterPeriod(SeriesDataList series, ChartPeriodOption? option) {
    if (option == null) {
      return series;
    }

    if (option.id?.toLowerCase() == 'all') return series;

    if (option.id?.toLowerCase() == LocaleKeys.recent) {
      return series
          .map(
            (e) => e.reversed.take(12).toList().reversed.toList(),
          )
          .toList();
    }

    final startDate = DateTime.tryParse(
          _selectedVisualizationMeta?.seriesMeta?.firstOrNull?.xMax ?? '',
        ) ??
        DateTime.now();

    final isPeriodYear = option.unit?.toLowerCase() == 'years';
    final value = option.value ?? 0;
    final endDate = isPeriodYear
        ? DateTime(startDate.year - value, startDate.month, startDate.day)
        : DateTime(startDate.year, startDate.month - value, startDate.day);

    final periodFilteredSeries = <SeriesData>[];
    for (final element in series) {
      final filteredData = element.where((entry) {
        final obsDate = DateTime.parse(entry['OBS_DT'] as String);
        return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
      }).toList();

      periodFilteredSeries.add(filteredData);
    }

    return periodFilteredSeries;
  }

  Widget _buildPeriodFilter() {
    if (_availablePeriodOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final hasSeries = _seriesList.any((e) => e.isNotEmpty);
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
      child: AnimatedOpacity(
        opacity: hasSeries ? 1 : 0.3,
        duration: const Duration(milliseconds: 300),
        child: IgnorePointer(
          ignoring: !hasSeries,
          child: ChartPeriodSwitcher(
            initialSelectedIndex: _availablePeriodOptions.indexWhere(
              (e) => _selectedPeriodOption.value?.id == e.id,
            ),
            options: _availablePeriodOptions,
            onChanged: (option) => _selectedPeriodOption.value = option,
          ),
        ),
      ),
    );
  }
}
