import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../../base/bloc/details_base_bloc.dart';
import '../../../base/mixin/indicator_details_bloc_mixin.dart';
import '../../../base/mixin/indicator_filter_adapter_mixin.dart';

part 'official_experimental_details_event.dart';
part 'official_experimental_details_state.dart';

class OfficialExperimentalDetailsBloc extends DetailsBaseBloc
    with IndicatorDetailsBlocMixin, IndicatorFilterAdapterMixin {
  OfficialExperimentalDetailsBloc() {
    onGetIndicatorDetailsListener();

    on<ApplyOfficialExperimentalFilterEvent>(_onApplyOfficialExperimentalFilterEvent);
    on<GetIndicatorOverviewEvent>(_getAllIndicatorDetailsOverview);
  }

  VisualizationsMeta? get selectedVisualizationMeta {
    int i = indicatorDetails?.indicatorVisualizations?.visualizationsMeta?.indexWhere(
          (element) => element.id == indicatorDetails?.indicatorVisualizations?.visualizationDefault,
        ) ??
        0;
    if (i == -1) i = 0;
    return indicatorDetails?.indicatorVisualizations?.visualizationsMeta?.elementAtOrNull(i);
  }

  void resetBlocVariables() {
    selectedFilterMap.clear();
  }

  @override
  FilterPanel? get filterPanel {
    final item = indicatorDetails?.filterPanel;
    if (item is FilterPanel) return item;
    return null;
  }

  void _onApplyOfficialExperimentalFilterEvent(
    ApplyOfficialExperimentalFilterEvent event,
    Emitter<DetailsBaseState> emit,
  ) {
    selectedFilterMap
      ..clear()
      ..addAll(event.payload);

    final hash = jsonEncode(event.payload).hashCode;
    emit(
      ApplyOfficialExperimentalFilterSuccessState(hash),
    );
  }

  Future<void> _getAllIndicatorDetailsOverview(
      GetIndicatorOverviewEvent event,
      Emitter<DetailsBaseState> emit,
      ) async {
    try {
      final response = await servicelocator<IndicatorCardRepository>().allIndicatorDetailsOverview(
        ids: event.ids,
        type: event.type,
      );

      if (response.isSuccess) {
        final OverView? overView = response.response?.values.firstOrNull;
        if (overView != null) {
          emit(
            GetIndicatorOverviewSuccessState(event, overView),
          );
        }
      }
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
    }
  }
}
