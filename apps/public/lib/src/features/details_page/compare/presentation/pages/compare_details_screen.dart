import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:screenshot/screenshot.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../common/functions/indicator_date_setting.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../../common/widgets/chart_legend.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../common/widgets/error_reload_placeholder.dart';
import '../../../../../common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import '../../../../../common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/extentions/string_extentions.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../base/constants.dart';
import '../../../base/mixin/chart_period_filter_mixin.dart';
import '../../../widgets/bottom_sheet/select_data_frequency_bottom_sheet.dart';
import '../../../widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import '../../../widgets/chart_action_button.dart';
import '../../../widgets/chart_period_options.dart';
import '../../../widgets/chart_view/chart_view.dart';
import '../../../widgets/chart_view/no_chart_data.dart';
import '../../../widgets/domain_and_personalize_controls/domain_and_personalize_controls.dart';
import '../../../widgets/download_as/download_as.dart';
import '../../../widgets/full_screen_chart_view/full_screen_chart_view.dart';
import '../../../widgets/glossary_container.dart';
import '../../../widgets/indicator_type_tag.dart';
import '../bloc/compare_details_bloc.dart';

@RoutePage()
class CompareDetailsScreen extends StatefulWidget {
  const CompareDetailsScreen({
    required this.combinedId,
    super.key,
    this.comparedIndicatorName,
    this.indicatorType,
  });

  final String combinedId; // id format 1234_1235
  final String? comparedIndicatorName;
  final IndicatorType? indicatorType;

  @override
  State<CompareDetailsScreen> createState() => _CompareDetailsScreenState();
}

class _CompareDetailsScreenState extends State<CompareDetailsScreen> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  String get _indicatorTitle => widget.comparedIndicatorName ?? LocaleKeys.compareIndicatorsResult.tr();

  IndicatorDetailsResponse? get _indicatorDetails => context.read<CompareDetailsBloc>().indicatorDetails;

  VisualizationsMeta? get _visualizationsMeta => _indicatorDetails?.indicatorVisualizations?.visualizationsMeta
      ?.where((e) => e.id == _indicatorDetails?.indicatorVisualizations?.visualizationDefault)
      .firstOrNull;

  String? get yAxisLabel => _visualizationsMeta?.seriesMeta!.firstOrNull?.label?.toString();

  String? get yAxisLabelRight => _visualizationsMeta?.seriesMeta!.lastOrNull?.label?.toString();

  final _scrollController = ScrollController();
  final _screenshotController = ScreenshotController();

  final _seriesList = <SeriesData>[];

  final _availablePeriodOptions = <ChartPeriodOption>[];
  final _selectedPeriodOption = ValueNotifier<ChartPeriodOption?>(null);

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;

  final _chartRepresentationPreviewNotifier = ValueNotifier<ChartDataRepresentation?>(null);

  @override
  void initState() {
    super.initState();

    context.read<CompareDetailsBloc>().resetBlocVariables();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _loadIndicatorDetails();
    });
  }

  void _loadIndicatorDetails() {
    final arr = widget.combinedId.split('_');
    final indicatorId1 = arr.elementAtOrNull(0) ?? '';
    final indicatorId2 = arr.elementAtOrNull(1) ?? '';

    context.read<CompareDetailsBloc>().add(
          GetCompareIndicatorDetailsEvent(
            indicatorId1: indicatorId1,
            indicatorId2: indicatorId2,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: _indicatorTitle,
              bottomPadding: 0,
              isDetailsPage: true,
              scrollController: _scrollController,
            ),
            Expanded(
              child: BlocConsumer<CompareDetailsBloc, CompareDetailsState>(
                buildWhen: (_, state) => state is GetCompareIndicatorDetailsBaseState,
                listenWhen: (_, state) => state is GetCompareIndicatorDetailsBaseState,
                listener: (context, state) {
                  if (state is! GetCompareIndicatorDetailsSuccessState) return;

                  _seriesList.clear();
                  for (int i = 0; i < (_visualizationsMeta?.seriesMeta ?? []).length; i++) {
                    _seriesList.add(
                      (_visualizationsMeta?.seriesMeta![i].data ?? []).toList(),
                    );
                  }

                  final availableOptions = _indicatorDetails?.indicatorFilters?.firstOrNull?.options
                      ?.map(
                        (e) => ChartPeriodOption.fromOption(e),
                      )
                      .toList();

                  if (_seriesList.any((e) => e.length > kRecentLength)) {
                    availableOptions?.add(
                      ChartPeriodOption.recent(),
                    );
                  }

                  _availablePeriodOptions.addAll(availableOptions ?? []);
                  _selectedPeriodOption.value = _availablePeriodOptions.lastOrNull;
                },
                builder: (context, state) {
                  if (state is GetCompareIndicatorDetailsErrorState) {
                    return Center(
                      child: ErrorReloadPlaceholder(
                        error: state.error,
                        onReload: _loadIndicatorDetails,
                      ),
                    );
                  }

                  if (state is GetCompareIndicatorDetailsLoadingState && _indicatorDetails == null) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return _buildBody(state);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(CompareDetailsState state) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: ConstrainedBox(
        constraints: BoxConstraints(minHeight: MediaQuery.sizeOf(context).height),
        child: IgnorePointer(
          ignoring: state is GetCompareIndicatorDetailsLoadingState,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_indicatorDetails != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: DomainAndPersonalizeControls(
                    indicatorDetails: _indicatorDetails!,
                    contentType: _indicatorDetails?.type ?? '',
                    hideNotification: true,
                  ),
                ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 12),
                    _buildChartHeader(),
                    const SizedBox(height: 24),
                    Screenshot(
                      key: Key('compare.indicator.details.chart.${widget.combinedId}'),
                      controller: _screenshotController,
                      child: Container(
                        constraints: const BoxConstraints(minHeight: 200),
                        color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: _buildChartAndLegends(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildPeriodFilter(),
                        const Spacer(),
                      ],
                    ),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                    const SizedBox(height: 12),
                    _buildDownloadAs(),
                    const SizedBox(height: 32),
                    const GlossaryContainer(),
                    const SizedBox(height: 48),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartAndLegends({
    bool? enforceLightMode,
    bool showTooltips = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildChart(
          enforceLightMode: enforceLightMode,
          showTooltips: showTooltips,
        ),
        _buildLegends(enforceLightMode: enforceLightMode),
      ],
    );
  }

  Widget _buildChart({
    bool? enforceLightMode,
    bool isFullScreen = false,
    bool showTooltips = false,
  }) {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final series = _seriesList;
        final periodFilteredSeries = _filterPeriod(
          series,
          selectedPeriodOption,
        );

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, selectedChartPreview, child) {
            final chartRepresentation = selectedChartPreview ?? _selectedChartRepresentation;

            final hasData = periodFilteredSeries.any((e) => e.isNotEmpty);
            if (!hasData) {
              return const NoChartData();
            }

            final String freqString = IndicatorDateSetting.setFrequancy(
              l: periodFilteredSeries,
              indicatorDetails: IndicatorDetailsResponseHelper(_indicatorDetails!),
            )['selectedFrequencyForFilter'] as String;

            final frequency = ChartDataFrequency.fromString(freqString);
            return ChartView(
              chartSeriesData: ChartSeriesData(
                series: periodFilteredSeries,
                tableFields: _indicatorDetails?.tableFields,
              ),
              frequency: frequency,
              chartRepresentation: chartRepresentation,
              enforceLightMode: enforceLightMode,
              showTooltips: showTooltips,
              isFullScreenView: isFullScreen,
              yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
              isVertical: !isFullScreen,
            );
          },
        );
      },
    );
  }

  Widget _buildLegends({bool? enforceLightMode}) {
    final isLightMode = enforceLightMode ?? _isLightMode;

    return ValueListenableBuilder(
      valueListenable: _chartRepresentationPreviewNotifier,
      builder: (context, previewChartType, child) {
        final selectedChartRepresentation = previewChartType ?? _selectedChartRepresentation;

        if (selectedChartRepresentation == ChartDataRepresentation.table) {
          return const SizedBox.shrink();
        }

        return Wrap(
          spacing: 12,
          runSpacing: 8,
          children: _getLegendList(isLightMode: isLightMode),
        );
      },
    );
  }

  List<ChartLegend> _getLegendList({required bool isLightMode}) {
    final List<String> legendList = _visualizationsMeta?.seriesTitles?.values.map((e) => e.toString()).toList() ?? [];
    final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return legendList.indexed
        .map(
          (e) => ChartLegend(
            label: e.$2,
            color: colorSet[e.$1 % colorSet.length],
            isLightMode: isLightMode,
          ),
        )
        .toList();
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final seriesListForDownloadAs = _filterPeriod(_seriesList, selectedPeriodOption);

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, value, child) {
            final isRestricted = _indicatorDetails?.security?.id != '0';
            final ChartDataFrequency frequency = IndicatorDateSetting.getChartDataFrequency(
              seriesList: _seriesList,
              indicatorDetails: _indicatorDetails,
            );

            return DownloadAsV2(
                key: Key('${_selectedChartRepresentation.index}'),
                title: _indicatorDetails?.componentTitle ?? '',
                description: _indicatorDetails?.componentSubtitle ?? '',
                seriesList: seriesListForDownloadAs,
                isRestricted: isRestricted,
                isTableView: _selectedChartRepresentation == ChartDataRepresentation.table,
                indicatorType: _indicatorDetails?.getIndicatorType(),
                chart: _buildChart(
                  enforceLightMode: true,
                  showTooltips: true,
                ),
              legendList: _getLegendList(isLightMode: true),
                tableFieldList: _indicatorDetails?.tableFields,
                frequency: frequency,
            );
          },
        );
      },
    );
  }

  Widget _buildChartHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          const IndicatorTypeTag.compare(),
          const Spacer(),
          FullScreenExpandButton(
            onTap: () => FullScreenChartView.show(
              context: context,
              title: _indicatorTitle,
              chartBuilder: (context) => _buildChart(isFullScreen: true),
              legendBuilder: (context) => _buildLegends(),
            ),
          ),
          /*FullScreenExpandButton(
            onTap: () {
              FullScreenChartView.show(
                context: context,
                title: _indicatorTitle,
                titleSuffixBuilder: _buildTableNavigator,
                chartBuilder: (context) => _buildChart(isFullScreen: true),
              );
            },
          ),*/
        ],
      ),
    );
  }

  SeriesDataList _filterPeriod(SeriesDataList series, ChartPeriodOption? option) {
    if (option == null) {
      return series;
    }

    if (option.id?.toLowerCase() == 'all') return series;

    if (option.id?.toLowerCase() == LocaleKeys.recent) {
      return series
          .map(
            (e) => e.reversed.take(12).toList().reversed.toList(),
          )
          .toList();
    }

    final startDate = DateTime.tryParse(
          _visualizationsMeta?.seriesMeta?.firstOrNull?.xMax ?? '',
        ) ??
        DateTime.now();

    final isPeriodYear = option.unit?.toLowerCase() == 'years';
    final value = option.value ?? 0;
    final endDate = isPeriodYear
        ? DateTime(startDate.year - value, startDate.month, startDate.day)
        : DateTime(startDate.year, startDate.month - value, startDate.day);

    final periodFilteredSeries = <SeriesData>[];
    for (final element in series) {
      final filteredData = element.where((entry) {
        final obsDate = DateTime.parse(entry['OBS_DT'] as String);
        return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
      }).toList();

      periodFilteredSeries.add(filteredData);
    }

    return periodFilteredSeries;
  }

  Widget _buildPeriodFilter() {
    if (_availablePeriodOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final hasSeries = _seriesList.any((e) => e.isNotEmpty);
    return Padding(
      padding: const EdgeInsets.only(top:8, left: 8, right: 8),
      child: AnimatedOpacity(
        opacity: hasSeries ? 1 : 0.3,
        duration: const Duration(milliseconds: 300),
        child: IgnorePointer(
          ignoring: !hasSeries,
          child: ChartPeriodSwitcher(
            initialSelectedIndex: _availablePeriodOptions.indexWhere(
              (e) => _selectedPeriodOption.value?.id == e.id,
            ),
            options: _availablePeriodOptions,
            onChanged: (option) => _selectedPeriodOption.value = option,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final hasSeries = _seriesList.isNotEmpty && (_seriesList.any((e) => e.isNotEmpty));

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(
              child: ChartActionButton.frequency(
                enabled: false,
              ),
            ),
            Expanded(
              child: ChartActionButton.presentation(
                enabled: hasSeries,
                onPressed: () async {
                  _chartRepresentationPreviewNotifier.value = _selectedChartRepresentation;

                  await SelectDataPresentationBottomSheet.show(
                    context,
                    options: [
                      ChartDataRepresentation.line,
                      ChartDataRepresentation.bar,
                      ChartDataRepresentation.table,
                    ],
                    initialRepresentation: _selectedChartRepresentation,
                    onPreview: (value) => _chartRepresentationPreviewNotifier.value = value,
                    onDone: (value) => _selectedChartRepresentation = value,
                  );
                  _chartRepresentationPreviewNotifier.value = null;
                },
              ),
            ),
            const Expanded(
              child: ChartActionButton.compare(
                enabled: false,
              ),
            ),
            const Expanded(
              child: ChartActionButton.compute(
                enabled: false,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }
}
