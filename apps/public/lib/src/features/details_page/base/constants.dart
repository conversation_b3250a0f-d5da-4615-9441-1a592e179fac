import '../../../utils/hive_utils/hive_utils_settings.dart';

const kObsDt = 'OBS_DT';
const kYear = 'YEAR';

const kAllOptions = ['all', 'الجميع', 'المجموع', 'الكل'];

const kRecentLength = 12;

const _mapAr = {
  'INDICATOR_ID': 'معرف المؤشر',
  'RUN_SEQ_ID': 'تشغيل معرف التسلسل',
  'RUN_DT': 'تشغيل التاريخ',
  'VALUE': 'قيمة',
  'VALUE_LL': 'الحد الأدنى للقيمة',
  'VALUE_UL': 'الحد الأعلى للقيمة',
  'UNIT': 'وحدة',
  'OBS_DT': 'تاريخ المراقبة',
  'OPT': 'علم التوقعات',
  'TYPE': 'يكتب',
  'OIL_NONOIL': 'فئة',
  'SECTOR': 'قطاع',
  'INDUSTRY': 'صناعة',
  'PARAMETER_COMBO_ID': 'معرف التحرير والسرد المعلمة',
  'SECTOR_AR': 'القطاع العربي',
  'VALUE_FORECAST': 'القيمة المتوقعة',
  'OBS_DT_CUR': 'تاريخ المراقبة الحقيقي',
  'VALUE_PERC_ECO': 'النسبة المئوية للقيمة',
  'VALUE_CURRENT': 'القيمة الحقيقية',
  'CHANGE': 'يتغير',
  'CHANGE_PY': 'نسبة التغيير',
  'LANGUAGE_CD': 'نوع اللغة',
  'NATIONALITY_TYPE': 'نوع الجنسية',
};

const _mapEn = {
  'INDICATOR_ID': 'Indicator ID',
  'VALUE': 'Value',
  'VALUE_LL': 'Value Upper Limit',
  'VALUE_UL': 'Value Lower Limit',
  'UNIT': 'Unit',
  'OBS_DT': 'Observation Date',
  'OPT': 'OPT',
  'TYPE': 'Type',
  'OIL_NONOIL': 'Oil/Non Oil',
  'SECTOR': 'Sector',
  'INDUSTRY': 'Industry',
  'PARAMETER_COMBO_ID': 'Parameter Combo ID',
  'SECTOR_AR': 'Sector Arabic',
  'VALUE_FORECAST': 'Value Forecast',
  'OBS_DT_CUR': 'Current Observation Date',
  'VALUE_PERC_ECO': 'Value Percentage',
  'VALUE_CURRENT': 'Current Value',
  'CHANGE': 'Change',
  'NATIONALITY_TYPE': 'Nationality Type',
  'CHANGE_PY': 'Change Percentage',
};

Map<String, String> get tableHeaderTranslations {
  return HiveUtilsSettings.isLanguageEnglish ? _mapEn : _mapAr;
}
