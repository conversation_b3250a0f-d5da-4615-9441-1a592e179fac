import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../constants.dart';

typedef SelectedFilterMap = Map<String, List<String>>;
typedef FilterPreview = Map<String, String>;

mixin IndicatorFilterAdapterMixin {
  FilterPanel? get filterPanel;

  final SelectedFilterMap selectedFilterMap = {};

  static SelectedFilterMap getDefaultFilter(FilterPanel? filterPanel) {
    final properties = filterPanel?.properties;
    if (properties == null) return {};

    final filterEntries = properties.map(
          (e) => MapEntry(
        e.path!,
        e.defaultVal == null ? <String>[] : [e.defaultVal!],
      ),
    );

    return Map.fromEntries(filterEntries);
  }

  void initializeDefaultFilter() {
    if (filterPanel == null) return;
    final map = getDefaultFilter(filterPanel);
    selectedFilterMap
      ..clear()
      ..addAll(map);
  }

  SelectedFilterMap get dependencyResolvedFilters {
    final properties = filterPanel?.properties;
    if (properties == null || selectedFilterMap.isEmpty) return {};

    final filterMap = <String, List<String>>{};
    for (final property in properties) {
      final isStaticFilter = property.staticFilter == true;
      if (isStaticFilter) continue;

      final path = property.path ?? '';

      if (!property.isAllDependencySatisfied(selectedFilterMap)) {
        final value = (filterPanel?.isCFD ?? false) ? 'NONE' : property.defaultVal ?? '';
        filterMap[path] = [value];
        continue;
      }

      if (property.path == kObsDt) {
        final obsDtValues = selectedFilterMap[kObsDt];
        if (obsDtValues == null || obsDtValues.isEmpty) continue;

        final isAllOption = obsDtValues.any((e) => kAllOptions.contains(e));
        if (isAllOption) {
          obsDtValues
            ..clear()
            ..addAll(property.options ?? []);
        }

        filterMap[path] = obsDtValues;
        continue;
      }

      final values = selectedFilterMap[path] ?? []
      //  sort based on index in FilterPanel.Properties.options
        ..sort(
              (a, b) =>
          property.options?.indexOf(a).compareTo(
            property.options?.indexOf(b) ?? -1,
          ) ??
              -1,
        );
      filterMap[path] = values;
    }
    return filterMap;
  }

  Map<String, String> get filterPreview {
    final filter = dependencyResolvedFilters;
    if (filter.isEmpty) return {};

    final multiFilterKey = filter.entries
        .firstWhere(
          (entry) => entry.key != kObsDt && entry.value.length > 1,
      orElse: () => filter.entries.firstWhere((entry) => entry.key != kObsDt),
    )
        .key;

    final properties = filterPanel?.properties ?? [];
    final entries = filter.entries.where((e) => e.key != multiFilterKey).map((entry) {
      final property = properties.singleWhere((e) => e.path == entry.key);
      return MapEntry(property.label ?? '', entry.value.join(', '));
    });

    return Map.fromEntries(entries);
  }

  int get filterCount {
    final properties = filterPanel?.properties;
    if (properties == null || selectedFilterMap.isEmpty) return 0;

    int count = 0;
    for (final property in properties) {
      final selectedValues = selectedFilterMap[property.path];
      if (selectedValues == null || selectedValues.isEmpty) continue;

      if (property.path == kObsDt) {
        final isDefaultAllOption = kAllOptions.contains(property.defaultVal);
        if (isDefaultAllOption) {
          final isAllOption = selectedValues.any((e) => kAllOptions.contains(e));
          if (isAllOption) continue;
        } else {
          final isDefaultOptionSelected = selectedValues.length == 1 && selectedValues.first == property.defaultVal;
          if (isDefaultOptionSelected) continue;
        }

        count++;
        continue;
      }

      if (selectedValues.length > 1) {
        count++;
        continue;
      }

      if (selectedValues.first != property.defaultVal) count++;
    }
    return count;
  }
}
