import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';

import '../../../../../route_manager/route_imports.gr.dart';
import '../../../../common/types.dart';
import '../../../../common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import '../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../utils/app_utils/app_log.dart';
import '../../../../utils/app_utils/app_message.dart';

class DetailsPageRouteHelper {
  DetailsPageRouteHelper._();

  static Future<Object?> indicatorDetailsPageRoute(
    BuildContext context, {
    required String id,
    required String contentType,
    required String title,
    required IndicatorType indicatorType,
  }) async {
    switch (indicatorType) {
      case IndicatorType.official:
      case IndicatorType.experimental:
        return navigateToOfficialExperimentalDetailsPage(
          context,
          id: id,
          contentType: contentType,
          title: title,
        );

      case IndicatorType.insightDiscovery:
      case IndicatorType.scenarioDriver:
      case IndicatorType.forecast:
      case IndicatorType.other:
        AppMessage.showOverlayNotificationError(message: 'Invalid indicator type found');
        return null;
    }
  }

  static Future<void> computeIndicatorDetailsPage(
    BuildContext context, {
    required String title,
    required IndicatorDetailsResponse indicatorDetails,
    required Security? security,
  }) async {
    AppLog.info('Details Page:${ComputeDetailsScreenRoute.name}:');

    await context.pushRoute(
      ComputeDetailsScreenRoute(
        title: title,
        indicatorDetails: indicatorDetails,
        security: security,
      ),
    );
  }

  static Future<void> compareIndicatorDetailsPage(
    BuildContext context, {
    required String combinedId,
    required IndicatorType? indicatorType,
    String? comparedIndicatorName,
  }) async {
    AppLog.info('Details Page:${CompareDetailsScreenRoute.name}:$combinedId');

    context.router.removeWhere((route) => route.name == CompareDetailsScreenRoute.name);
    await context.pushRoute(
      CompareDetailsScreenRoute(
        combinedId: combinedId,
        indicatorType: indicatorType,
        comparedIndicatorName: comparedIndicatorName,
      ),
    );
  }

  static Future<Object?> navigateToOfficialExperimentalDetailsPage(
    BuildContext context, {
    required String id,
    required String contentType,
    required String title,
    JSONObject? screenerPayload,
  }) async {
    AppLog.info('Details Page:${OfficialExperimentalDetailsScreenRoute.name}:$id');

    context.router.removeWhere((route) => route.name == OfficialExperimentalDetailsScreenRoute.name);
    return context.pushRoute(
      OfficialExperimentalDetailsScreenRoute(
        id: id,
        title: title,
        contentType: contentType,
        screenerPayload: screenerPayload,
      ),
    );
  }
}
