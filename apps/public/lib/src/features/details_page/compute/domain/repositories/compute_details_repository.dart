import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../utils/hive_utils/api_cache/api_cache.dart';

abstract class ComputeDetailsRepository extends CacheableRepository {
  Future<RepoResponse<IndicatorDetailsResponse>> compute({
    required JSONObject payload,
  });
}
