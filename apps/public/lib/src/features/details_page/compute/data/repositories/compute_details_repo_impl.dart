import '../../../../../common/models/response_models/repo_response.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../services/http_service_impl.dart';
import '../../domain/repositories/compute_details_repository.dart';
import '../data_sources/compute_endpoints.dart';

class ComputeDetailsRepositoryImpl extends ComputeDetailsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> compute({required JSONObject payload}) {
    final endpoint = ComputeDetailsEndPoints.computeDataEndPoint;
    final cacheKey = getCacheKey(endpoint, payload: payload);
    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }
}