import 'package:easy_localization/easy_localization.dart';

import '../../../../../../translations/locale_keys.g.dart';

enum ComputeOperations {
  summation(LocaleKeys.summation, 'summation', '+'),
  subtraction(LocaleKeys.subtraction, 'subtraction', '-'),
  multiplication(LocaleKeys.multiplication, 'multiplication', '*'),
  division(LocaleKeys.division, 'division', '/');

  const ComputeOperations(this.localeKey, this.assetFile, this.symbol);

  final String localeKey;
  final String symbol;
  final String assetFile;

  String get label => localeKey.tr();
}
