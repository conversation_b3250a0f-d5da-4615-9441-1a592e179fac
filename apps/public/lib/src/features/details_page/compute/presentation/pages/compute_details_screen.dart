import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import '../../../../../common/functions/indicator_date_setting.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/appbar/flat_app_bar.dart';
import '../../../../../common/widgets/chart_legend.dart';
import '../../../../../common/widgets/domain_icon.dart';
import '../../../../../common/widgets/drawer/app_drawer_part.dart';
import '../../../../../common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../base/constants.dart';
import '../../../base/mixin/chart_period_filter_mixin.dart';
import '../../../widgets/bottom_sheet/select_data_frequency_bottom_sheet.dart';
import '../../../widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import '../../../widgets/chart_action_button.dart';
import '../../../widgets/chart_period_options.dart';
import '../../../widgets/chart_view/chart_view.dart';
import '../../../widgets/chart_view/no_chart_data.dart';
import '../../../widgets/download_as/download_as.dart';
import '../../../widgets/full_screen_chart_view/full_screen_chart_view.dart';
import '../../../widgets/glossary_container.dart';
import '../../../widgets/indicator_chart_header.dart';
import '../../../widgets/indicator_type_tag.dart';
import '../../../../../utils/constants/color_constants/color_constants.dart';
import '../../../../../utils/hive_utils/hive_utils_settings.dart';
import '../../../../../../translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

@RoutePage()
class ComputeDetailsScreen extends StatefulWidget {
  const ComputeDetailsScreen({
    required this.title,
    required this.indicatorDetails,
    required this.security,
    super.key,
  });

  final String title;
  final IndicatorDetailsResponse indicatorDetails;
  final Security? security;

  @override
  State<ComputeDetailsScreen> createState() => _ComputeDetailsScreenState();
}

class _ComputeDetailsScreenState extends State<ComputeDetailsScreen> {
  final bool _isLightMode = HiveUtilsSettings.isLightMode;

  final _scrollController = ScrollController();
  final _screenshotController = ScreenshotController();

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;

  final _chartRepresentationPreviewNotifier = ValueNotifier<ChartDataRepresentation?>(null);

  IndicatorDetailsResponse get _indicatorDetails => widget.indicatorDetails;

  IndicatorType get indicatorType => _indicatorDetails.getIndicatorType();

  VisualizationsMeta? get _visualizationMeta =>
      _indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull;

  final _availablePeriodOptions = <ChartPeriodOption>[];
  final _selectedPeriodOption = ValueNotifier<ChartPeriodOption?>(null);

  @override
  void initState() {
    super.initState();

    final availableOptions = _indicatorDetails.indicatorFilters?.firstOrNull?.options
        ?.map(
          (e) => ChartPeriodOption.fromOption(e),
        )
        .toList();

    if (_seriesList.any((e) => e.length > kRecentLength)) {
      availableOptions?.add(
        ChartPeriodOption.recent(),
      );
    }

    _availablePeriodOptions.addAll(availableOptions ?? []);
    _selectedPeriodOption.value = _availablePeriodOptions.lastOrNull;
  }

  SeriesDataList get _seriesList {
    final list = _visualizationMeta?.seriesMeta
        ?.map(
          (e) => e.data ?? <JSONObject>[],
        )
        .toList();
    return list ?? <SeriesData>[];
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: widget.title,
              bottomPadding: 0,
              isDetailsPage: true,
              scrollController: _scrollController,
            ),
            Expanded(
              child: _buildBody(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      controller: _scrollController,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.sizeOf(context).height,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: DomainIcon(
                domainIdOrName: widget.indicatorDetails.domainId,
                color: _isLightMode ? AppColors.black : AppColors.white,
                padding: const EdgeInsets.all(6),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(30),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildChartHeader(),
                  const SizedBox(height: 16),
                  Screenshot(
                    controller: _screenshotController,
                    child: Container(
                      constraints: const BoxConstraints(minHeight: 200),
                      color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                      child: AnimatedSize(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.fastLinearToSlowEaseIn,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: _buildChartAndLegends(),
                        ),
                      ),
                    ),
                  ),
                  _buildPeriodFilter(),
                  const SizedBox(height: 32),
                  _buildActionButtons(),
                  const SizedBox(height: 32),
                  _buildDownloadAs(),
                  const SizedBox(height: 32),
                  const GlossaryContainer(),
                  const SizedBox(height: 48),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartHeader() {
    final hasSeries = _seriesList.any((e) => e.isNotEmpty);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: IndicatorChartHeader(
        tag: const IndicatorTypeTag.compute(),
        security: widget.security,
        fullScreenButtonBuilder: (context) {
          return Opacity(
            opacity: hasSeries ? 1 : 0.5,
            child: FullScreenExpandButton(
              onTap: hasSeries
                  ? () => FullScreenChartView.show(
                        context: context,
                        title: widget.title,
                        chartBuilder: (context) => _buildChart(isFullScreen: true),
                        legendBuilder: (context) => _buildLegends(),
                      )
                  : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildChartAndLegends({bool showTooltips = false, bool? enforceLightMode}) {
    final hasSeriesData = _seriesList.any((e) => e.isNotEmpty);
    if (!hasSeriesData) {
      return const NoChartData();
    }

    return Column(
      children: [
        _buildChart(
          enforceLightMode: enforceLightMode,
          showTooltips: showTooltips,
        ),
        _buildLegends(
          enforceLightMode: enforceLightMode,
        ),
      ],
    );
  }

  SeriesDataList _filterPeriod(SeriesDataList series, ChartPeriodOption? option) {
    if (option == null) {
      return series;
    }

    if (option.id?.toLowerCase() == 'all') return series;

    if (option.id?.toLowerCase() == LocaleKeys.recent) {
      return series
          .map(
            (e) => e.reversed.take(12).toList().reversed.toList(),
          )
          .toList();
    }

    final startDate = DateTime.tryParse(
          _visualizationMeta?.seriesMeta?.firstOrNull?.xMax ?? '',
        ) ??
        DateTime.now();

    final isPeriodYear = option.unit?.toLowerCase() == 'years';
    final value = option.value ?? 0;
    final endDate = isPeriodYear
        ? DateTime(startDate.year - value, startDate.month, startDate.day)
        : DateTime(startDate.year, startDate.month - value, startDate.day);

    final periodFilteredSeries = <SeriesData>[];
    for (final element in series) {
      final filteredData = element.where((entry) {
        final obsDate = DateTime.parse(entry['OBS_DT'] as String);
        return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
      }).toList();

      periodFilteredSeries.add(filteredData);
    }

    return periodFilteredSeries;
  }

  Widget _buildChart({
    bool? enforceLightMode,
    bool showTooltips = false,
    bool isFullScreen = false,
  }) {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final series = _seriesList;
        final periodFilteredSeries = _filterPeriod(
          series,
          selectedPeriodOption,
        );

        final frequency = IndicatorDateSetting.getChartDataFrequency(
          seriesList: series,
          indicatorDetails: _indicatorDetails,
        );

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, chartRepresentationPreview, child) {
            final chartRepresentation = chartRepresentationPreview ?? _selectedChartRepresentation;
            final yAxisLabel = _visualizationMeta?.yAxisLabel;

            return ChartView(
              chartRepresentation: chartRepresentation,
              frequency: frequency,
              enforceLightMode: enforceLightMode,
              chartSeriesData: ChartSeriesData(
                series: periodFilteredSeries,
                tableFields: _indicatorDetails.tableFields,
              ),
              isFullScreenView: isFullScreen,
              showTooltips: showTooltips,
              yAxisLabel: yAxisLabel,
              isVertical: !isFullScreen,
            );
          },
        );
      },
    );
  }

  Widget _buildLegends({bool? enforceLightMode}) {
    final isLightMode = enforceLightMode ?? HiveUtilsSettings.isLightMode;

    return ValueListenableBuilder(
      valueListenable: _chartRepresentationPreviewNotifier,
      builder: (context, previewChartType, child) {
        final selectedChartRepresentation = previewChartType ?? _selectedChartRepresentation;
        if (selectedChartRepresentation == ChartDataRepresentation.table) {
          return const SizedBox.shrink();
        }

        return Center(
          child: _getLegendList(
            isLightMode: isLightMode,
          ).first,
        );
      },
    );
  }

  List<ChartLegend> _getLegendList({required bool isLightMode}) {
    final legend = _visualizationMeta?.seriesMeta?.firstOrNull?.label as String? ?? '';
    final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return [
      ChartLegend(
        label: legend,
        color: colorSet.first,
        isLightMode: isLightMode,
      ),
    ];
  }

  Widget _buildPeriodFilter() {
    if (_availablePeriodOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final hasSeries = _seriesList.any((e) => e.isNotEmpty);
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 8, right: 8),
      child: AnimatedOpacity(
        opacity: hasSeries ? 1 : 0.3,
        duration: const Duration(milliseconds: 300),
        child: IgnorePointer(
          ignoring: !hasSeries,
          child: ChartPeriodSwitcher(
            initialSelectedIndex: _availablePeriodOptions.indexWhere(
              (e) => _selectedPeriodOption.value?.id == e.id,
            ),
            options: _availablePeriodOptions,
            onChanged: (option) => _selectedPeriodOption.value = option,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final hasSeries = _seriesList.isNotEmpty && (_seriesList.any((e) => e.isNotEmpty));
    final timeUnits = _visualizationMeta?.timeUnit ?? [];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ChartActionButton.frequency(
            enabled: false,
            onPressed: () {
              SelectDataFrequencyBottomSheet.show(
                context,
                timeUnits: timeUnits,
                onPreview: (frequency) {},
                onDone: (frequency) {},
              );
            },
          ),
        ),
        Expanded(
          child: ChartActionButton.presentation(
            enabled: hasSeries,
            onPressed: () async {
              _chartRepresentationPreviewNotifier.value = _selectedChartRepresentation;

              await SelectDataPresentationBottomSheet.show(
                context,
                options: [
                  ChartDataRepresentation.line,
                  ChartDataRepresentation.bar,
                  ChartDataRepresentation.table,
                ],
                initialRepresentation: _selectedChartRepresentation,
                onPreview: (value) => _chartRepresentationPreviewNotifier.value = value,
                onDone: (value) => _selectedChartRepresentation = value,
              );
              _chartRepresentationPreviewNotifier.value = null;
            },
          ),
        ),
        const Expanded(
          child: ChartActionButton.compare(
            enabled: false,
          ),
        ),
        const Expanded(
          child: ChartActionButton.compute(
            enabled: false,
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final series = _seriesList;
        final periodFilteredSeries = _filterPeriod(
          series,
          selectedPeriodOption,
        );

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, value, child) {
            final isRestricted = widget.security?.id != '0';
            final frequency = IndicatorDateSetting.getChartDataFrequency(
              seriesList: _seriesList,
              indicatorDetails: _indicatorDetails,
            );

            return DownloadAsV2(
              key: Key('${_selectedChartRepresentation.index}'),
              title: widget.title,
              description: _indicatorDetails.componentSubtitle ?? '',
              seriesList: periodFilteredSeries,
              isRestricted: isRestricted,
              isTableView: _selectedChartRepresentation == ChartDataRepresentation.table,
              chart: _buildChart(
                enforceLightMode: true,
                showTooltips: true,
              ),
              legendList: _getLegendList(isLightMode: true),
              tableFieldList: _indicatorDetails.tableFields,
              frequency: frequency,
              indicatorType: indicatorType,
            );
          },
        );
      },
    );
  }
}
