import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../translations/locale_keys.g.dart';
import '../../../../../common/types.dart';
import '../../../../../common/widgets/indicator_card/data/models/indicator_details_response.dart';
import '../../../../../config/dependancy_injection/injection_container.dart';
import '../../domain/repositories/compute_details_repository.dart';

part 'compute_details_event.dart';
part 'compute_details_state.dart';

class ComputeDetailsBloc extends Bloc<ComputeDetailsEvent, ComputeDetailsState> {
  ComputeDetailsBloc() : super(ComputeDetailsVoidState()) {
    on<GetComputedDataEvent>(_onGetComputedDataEvent);
  }

  Future<void> _onGetComputedDataEvent(
    GetComputedDataEvent event,
    Emitter<ComputeDetailsState> emit,
  ) async {
    emit(GetComputeDetailsLoadingState());

    try {
      final response = await servicelocator<ComputeDetailsRepository>().compute(
        payload: event.payload,
      );

      if (!response.isSuccess) {
        emit(
          GetComputeDetailsErrorState(error: response.errorMessage),
        );
        return;
      }

      emit(
        GetComputeDetailsSuccessState(
          computeName: event.name,
          data: response.response!,
        ),
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        GetComputeDetailsErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }
}
