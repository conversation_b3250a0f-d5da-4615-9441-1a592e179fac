import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:http_certificate_pinning/http_certificate_pinning.dart';
import 'package:http_parser/http_parser.dart';
import '../common/models/response_models/api_response.dart';
import '../config/app_config/api_config.dart';
import '../config/app_config/secret.dart';
import 'http_services.dart';
import 'interceptor_log.dart';
import '../utils/app_utils/app_cache.dart';
import '../utils/app_utils/app_message.dart';
import '../utils/app_utils/downloadHelper.dart';
import '../utils/hive_utils/hive_utils_settings.dart';
import '../../translations/locale_keys.g.dart';

enum ApiServer {
  ifp,
  app,
  arcGis,
  scad,
}

class HttpServiceRequests implements HttpService {
  final List<String> allowedSHA256Fingerprints = [
    Secret.oldSslFingerprintSHA256Wildcard,
    Secret.newSslFingerprintSHA256Wildcard,
    Secret.sslFingerprintSHA256scad,
  ];

  String apiBaseUrl(ApiServer server) {
    return server == ApiServer.scad
        ? ApiConfig.scadBaseUrl
        : server == ApiServer.ifp
        ? ApiConfig.ifpBaseUrl
        : server == ApiServer.arcGis
        ? ApiConfig.arcGisBaseUrl
        : ApiConfig.appBaseUrl;
  }

  String get appLanguage => HiveUtilsSettings.appLanguage;

  void _addInterceptors(Dio dio, [bool excludeLogInterceptor = false]) {
    if (!excludeLogInterceptor) {
      dio.interceptors.add(InterceptorLog());
    }

    if (Secret.appInstance != AppInstance.beinexDev) {
      dio.interceptors.add(
        CertificatePinningInterceptor(allowedSHAFingerprints: allowedSHA256Fingerprints),
      );
    }
  }

  @override
  Future<ApiResponse> postJson(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic>? jsonPayloadMap,
        List<dynamic>? jsonPayloadList,
        bool shouldAuthenticate = true,
        bool encodedHeader = false,
        String? token,
        bool shouldIntercept = true,
        Map<String, dynamic> header = const {},
      }) async {
    try {
      final String apiToken = token ?? ''; // ?? (shouldAuthenticate ? HiveUtilsAuth.getToken() : '');

      final Dio dio = encodedHeader
          ? Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          // connectTimeout: const Duration(seconds: 20),
          headers: {
            'Content-type': 'application/x-www-form-urlencoded',
            'Accept-Language': appLanguage,
          },
        ),
      )
          : Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': appLanguage,
            if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      if (header.isNotEmpty) {
        dio.options.headers.addAll(header);
      }

      _addInterceptors(dio, !shouldIntercept);

      dynamic data;
      if (jsonPayloadMap != null) {
        data = jsonPayloadMap;
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      final response = await dio.post<dynamic>(
        endpoint,
        data: data,
      );
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  @override
  Future<ApiResponse> postMultipart(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic>? formDataPayload,
        List<dynamic>? jsonPayloadList,
        Map<String, File> filePayload = const {},
      }) async {
    try {
      // final String apiToken = HiveUtilsAuth.getToken();

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          // connectTimeout: const Duration(seconds: 20),
          headers: {
            'Content-type': 'multipart/form-data',
            'Accept-Language': appLanguage,
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      _addInterceptors(dio);

      dynamic data;
      if (formDataPayload != null) {
        data = FormData.fromMap(formDataPayload);
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      if (filePayload != {}) {
        data ??= FormData();
        for (final MapEntry<String, File> entry in filePayload.entries) {
          final String? mime = DownloadHelper.mimeType(entry.value.path);
          MediaType? mediaType;
          if (mime != null) {
            mediaType = MediaType(mime.split('/')[0], mime.split('/')[1]);
          }
          data.files.add(
            MapEntry(
              entry.key,
              await MultipartFile.fromFile(entry.value.path,
                  filename: entry.value.path.split('/').last, contentType: mediaType),
            ),
          );
        }
      }

      final response = await dio.post<dynamic>(endpoint, data: data);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  @override
  Future<ApiResponse> get(
      String endpoint, {
        ApiServer server = ApiServer.app,
        String? token,
        Map<String, dynamic> header = const {},
      }) async {
    try {
      final String apiToken = token ?? ''; // HiveUtilsAuth.getToken();

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: server == ApiServer.arcGis
              ? null
              : {
            'Content-type': 'application/json',
            'Accept-Language': appLanguage,
            if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      if (header.isNotEmpty) {
        dio.options.headers.addAll(header);
      }

      _addInterceptors(dio);

      final response = await dio.get<dynamic>(endpoint);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  ApiResponse _processResponse(Response<dynamic> response) {
    switch (response.statusCode) {
      case 200:
        if (response.data is Map) {
          return ApiResponse.success(
            response.statusCode,
            response.data as Map<String, dynamic>,
          );
        } else {
          return ApiResponse.success(response.statusCode, {
            'data': response.data,
          });
        }
      case 204:
        return ApiResponse.success(response.statusCode, {});
      case 423:
        ApiResponse.error(
          response.statusCode,
          '${LocaleKeys.badResponse.tr()} ${response.statusCode}',
        );
      case 424:
        return ApiResponse.error(
          response.statusCode,
          '${LocaleKeys.unknownResponse.tr()} ${response.statusCode}',
        );
      case 201:
        if (response.data is Map) {
          return ApiResponse.success(
            response.statusCode,
            response.data as Map<String, dynamic>,
          );
        } else {
          return ApiResponse.success(response.statusCode, {'data': response.data});
        }
      case 301:
        return ApiResponse.success(response.statusCode, {});
      case 400:
        String msg = LocaleKeys.badRequest.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('message')) {
            return ApiResponse.error(response.statusCode, map['message'].toString());
          } else if (map.containsKey('error')) {
            return ApiResponse.error(response.statusCode, map['error'].toString());
          }

          if (map.containsKey('current_password') && (map['current_password'] as List).isNotEmpty) {
            //user profile reset password
            msg = map['current_password'].firstOrNull.toString();
          } else if (map.containsKey('non_field_errors') && map['non_field_errors'].toString().isNotEmpty) {
            //user profile reset password
            msg = map['non_field_errors'].firstOrNull.toString();
          }

          return ApiResponse.error(response.statusCode, msg);
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }

        ApiResponse.error(response.statusCode, msg);
      case 401:
        String msg = LocaleKeys.unauthorized.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('error')) {
            msg = map['error'].toString();
          }
          if (msg == 'inactive_userprofile') {
            AppMessage.showOverlayNotificationError(message: LocaleKeys.inactiveProfileStatusMessage.tr());
            // HiveUtilsAuth.logout();
          }
          if (map['code'].toString() == 'user_not_found') {
            AppMessage.showOverlayNotificationError(message: LocaleKeys.inactiveProfileStatusMessage.tr());
            // HiveUtilsAuth.logout();
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }

        return ApiResponse.error(response.statusCode, msg);
      case 403:
        return ApiResponse.error(
          response.statusCode,
          LocaleKeys.sessionExpired.tr(),
        );
      case 404:
        String msg = LocaleKeys.requestedResourceIsMissing.tr();
        try {
          final Map map = response.data as Map;
          if (map.containsKey('message')) {
            msg = map['message'].toString();
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
        return ApiResponse.error(response.statusCode, msg);
      case 422:
        return ApiResponse.error(response.statusCode, LocaleKeys.unableToProcessTheRequest.tr());
      case 502:
        return ApiResponse.error(response.statusCode, LocaleKeys.badGatewayError.tr());
      case 503:
        return ApiResponse.error(response.statusCode, LocaleKeys.serviceUnavailableError.tr());
      case 500:
      default:
        String? msg;
        try {
          final Map map = response.data as Map;
          if (map.containsKey('message')) {
            msg = map['message'].toString();
          } else if (map.containsKey('detail')) {
            msg = map['detail'].toString();
          }
        } catch (e, s) {
          Completer<dynamic>().completeError(e, s);
        }
        if (msg != null) {
          return ApiResponse.error(response.statusCode, msg);
        }
    }
    return ApiResponse.error(response.statusCode, LocaleKeys.unknownErrorOccurred.tr());
  }

  @override
  Future<ApiResponse> putJson(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic> jsonPayload = const {},
        bool shouldAuthenticate = true,
      }) async {
    try {
      // final String apiToken = shouldAuthenticate ? HiveUtilsAuth.getToken() : '';

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': appLanguage,
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      _addInterceptors(dio);

      final response = await dio.put<dynamic>(endpoint, data: jsonPayload);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  @override
  Future<ApiResponse> putMultipart(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic>? formDataPayload,
        List<dynamic>? jsonPayloadList,
        Map<String, File> filePayload = const {},
      }) async {
    try {
      // final String apiToken = HiveUtilsAuth.getToken();

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'multipart/form-data',
            'Accept-Language': appLanguage,
            // if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      _addInterceptors(dio);

      dynamic data;
      if (formDataPayload != null) {
        data = FormData.fromMap(formDataPayload);
      } else if (jsonPayloadList != null) {
        data = json.encode(jsonPayloadList);
      }

      if (filePayload != {}) {
        data ??= FormData();
        for (final MapEntry<String, File> entry in filePayload.entries) {
          final String? mime = DownloadHelper.mimeType(entry.value.path);
          MediaType? mediaType;
          if (mime != null) {
            mediaType = MediaType(mime.split('/')[0], mime.split('/')[1]);
          }
          data.files.add(
            MapEntry(
              entry.key,
              await MultipartFile.fromFile(
                entry.value.path,
                filename: entry.value.path.split('/').last,
                contentType: mediaType,
              ),
            ),
          );
        }
      }

      final response = await dio.put<dynamic>(endpoint, data: data);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  @override
  Future<ApiResponse> patchFormData(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic>? data,
      }) async {
    try {
      // final String apiToken = HiveUtilsAuth.getToken();

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            HttpHeaders.contentTypeHeader: 'multipart/form-data',
            HttpHeaders.acceptLanguageHeader: appLanguage,
            // if (apiToken.isNotEmpty) HttpHeaders.authorizationHeader: 'Bearer $apiToken',
          },
        ),
      );

      _addInterceptors(dio);

      dynamic body;
      if (data != null) {
        body = FormData.fromMap(data);
      }

      final response = await dio.patch<dynamic>(endpoint, data: body);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }

  @override
  Future<ApiResponse> delete(
      String endpoint, {
        ApiServer server = ApiServer.app,
        String? token,
      }) async {
    try {
      final String apiToken = token ?? ''; //HiveUtilsAuth.getToken();

      final Dio dio = Dio(
        BaseOptions(
          baseUrl: apiBaseUrl(server),
          headers: {
            'Content-type': 'application/json',
            'Accept-Language': appLanguage,
            if (apiToken.isNotEmpty) 'Authorization': 'Bearer $apiToken',
          },
        ),
      );

      _addInterceptors(dio);

      final response = await dio.delete<dynamic>(endpoint);
      return _processResponse(response);
    } on SocketException {
      return ApiResponse.error(408, LocaleKeys.networkError.tr());
    } on TimeoutException {
      return ApiResponse.error(408, LocaleKeys.requestHasBeenTimedOut.tr());
    } catch (error, s) {
      Completer<dynamic>().completeError(error, s);

      if (error is DioException) {
        return _processResponse(error.response!);
      } else {
        return ApiResponse.error(500, LocaleKeys.somethingWentWrong.tr());
      }
    } finally {
      await AppCache.clearIOSCacheDB();
    }
  }
}
