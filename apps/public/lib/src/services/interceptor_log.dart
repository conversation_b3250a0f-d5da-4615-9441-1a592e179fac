import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../utils/hive_utils/hive_utils_persistent.dart';

class InterceptorLog extends Interceptor {
  bool get _enableLogging {
    if (kDebugMode) {
      return HiveUtilsPersistent.box.get('enableInterceptorLog', defaultValue: false) as bool;
    }
    return false;
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _log(options.method, options.uri, options.headers, options.data);
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) {
    _log(
      response.requestOptions.method,
      response.requestOptions.uri,
      response.requestOptions.headers,
      response.requestOptions.data,
      response: response,
    );
    super.onResponse(response, handler);
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    _log(
      err.requestOptions.method,
      err.requestOptions.uri,
      err.requestOptions.headers,
      err.requestOptions.data,
      error: err,
    );

    super.onError(err, handler);
  }

  void _log(
    String method,
    Uri uri,
    Map<String, dynamic> headers,
    dynamic payload, {
    Response<dynamic>? response,
    DioException? error,
  }) {
    if (!kDebugMode || !_enableLogging) return;

    try {
      final isRequest = response == null && error == null;
      var msg = '';

      msg += '\n=======${isRequest ? 'REQUEST' : 'RESPONSE'}-$method=========BEGIN========================\n';
      msg += '[DATETIME] :${DateTime.now()}\n';
      msg += '[URL] :$uri\n';
      msg += '[HEADERS] :${json.encode(headers)}\n';
      msg += '[REQ_HEADERS] :${json.encode(headers)}\n';
      if (!isRequest) {
        msg += '[RES_HEADERS] :${json.encode(response?.headers.map ?? {})}\n';
      }
      try {
        if (payload is FormData) {
          final FormData data = payload;
          var str = '';
          data.fields.toList().forEach((element) {
            str += '${element.key}:${element.value}\n'; //json.encode(element);
          });
          msg += '[MULTIPART_PAYLOAD] :$str';
        } else {
          msg += '[JSON_PAYLOAD] :${jsonEncode(payload)}\n';
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        msg += '[_PAYLOAD] :$payload\n';
      }
      if (response != null) {
        msg += '[RESPONSE_CODE] :${response.statusCode}\n';
        msg += '[RESPONSE_DATA] :${json.encode(response.data)}\n';
      }
      if (error != null) {
        msg += '[RESPONSE_CODE] :${error.response?.statusCode}\n';
        msg += '[ERRORt] :${error.type}\n';
        msg += '[ERRORm] :${error.message}\n';
        msg += '[ERRORe] :${error.error}\n';
        msg += '[ERRORr] :${json.encode(error.response?.data)}\n';
      }
      msg += '=======${isRequest ? 'REQUEST' : 'RESPONSE'}-$method==========END=========================\n';
      log(msg);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }
}
