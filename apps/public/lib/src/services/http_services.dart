import 'dart:io';

import '../common/models/response_models/api_response.dart';
import 'http_service_impl.dart';

typedef RequestParamsMap = Map<String, String>;

abstract class HttpService {
  Future<ApiResponse> get(
    String endpoint, {
    ApiServer server = ApiServer.app,
    String token='',
    Map<String, dynamic> header = const {},
  });

  Future<ApiResponse> putJson(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic> jsonPayload = const {},
    bool shouldAuthenticate = true,
  });

  Future<ApiResponse> putMultipart(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? formDataPayload,
    List<dynamic>? jsonPayloadList,
    Map<String, File> filePayload = const {},
  });

  Future<ApiResponse> postJson(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? jsonPayloadMap,
    List<dynamic>? jsonPayloadList,
    bool shouldAuthenticate = true,
    bool encodedHeader = false,
    String token ='',
    bool shouldIntercept = true,
    Map<String, dynamic> header = const {},
  });

  Future<ApiResponse> postMultipart(
    String endpoint, {
    ApiServer server = ApiServer.app,
    Map<String, dynamic>? formDataPayload,
    List<dynamic>? jsonPayloadList,
    Map<String, File> filePayload = const {},
  });

  Future<ApiResponse> patchFormData(
      String endpoint, {
        ApiServer server = ApiServer.app,
        Map<String, dynamic>? data,
      });

  Future<ApiResponse> delete(
    String endpoint, {
    ApiServer server = ApiServer.app,
        String token='',
      });
}
