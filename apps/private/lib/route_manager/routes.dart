part of 'route_imports.dart';

@AutoRouterConfig(replaceInRouteName: 'Route')
class AppRouter extends RootStackRouter {
  RouteType get defaultType => const RouteType.cupertino();

  final maintenanceGuard = MaintenanceGuard();

  @override
  List<AutoRoute> get routes => [
        CupertinoRoute<void>(page: SplashPageRoute.page, initial: true),
        CupertinoRoute<void>(page: LoginPageRoute.page),
        CupertinoRoute<void>(
          page: HomeNavigationRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: HomePageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SettingsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SettingInterestPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SearchScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: NotificationListRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ChatWithSmeInboxPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: AboutThisAppScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: TermsAndConditionsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: FeedbackScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: FeedbackSuccessScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ThemesPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ResetPasswordScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ThemeIndicatorsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ProductsRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: DashboardWebViewPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(page: OnboardingScreenRoute.page),
        CupertinoRoute<void>(
          page: GlossaryScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ContactUsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: UserGuideScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: CollectionIndicatorsRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ForgotPasswordScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ContactUsSuccessPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SpatialAnalyticsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SpatialAnalyticsV2ScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: MyAppsLandingPageRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: PdfPreviewScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ExcelPreviewScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: AppUpdateScreenRoute.page,
          path: '/app-update',
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: GenAIScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: OfficialExperimentalDetailsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: InsightsDiscoveryDetailsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ScenarioForecastDetailsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ScenarioDriverModifierScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: CompareDetailsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SelectComputableScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ComputeDetailsScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: ImageViewerScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CustomRoute<void>(
          page: NDAScreenRoute.page,
          transitionsBuilder: TransitionsBuilders.fadeIn,
        ),
        CupertinoRoute<void>(page: MaintenanceScreenRoute.page),
        CupertinoRoute<void>(
          page: DownloadHistoryScreenRoute.page,
          guards: [maintenanceGuard],
        ),
        CupertinoRoute<void>(
          page: SelectComparableScreenRoute.page,
          guards: [maintenanceGuard],
        ),
      ];
}
