// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:io' as _i49;

import 'package:auto_route/auto_route.dart' as _i46;
import 'package:collection/collection.dart' as _i58;
import 'package:flutter/cupertino.dart' as _i57;
import 'package:flutter/foundation.dart' as _i53;
import 'package:flutter/material.dart' as _i48;
import 'package:scad_mobile/src/common/types.dart' as _i56;
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart'
    as _i51;
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart'
    as _i52;
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/forgot_password_screen.dart'
    as _i15;
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/login_page.dart'
    as _i22;
import 'package:scad_mobile/src/features/authentication/presentation/pages/login/reset_password_screen.dart'
    as _i31;
import 'package:scad_mobile/src/features/authentication/presentation/pages/splash/splash.dart'
    as _i41;
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/ask_us_landing_page.dart'
    as _i3;
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/chat_with_sme_inbox_page.dart'
    as _i4;
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/image_viewer_screen.dart'
    as _i20;
import 'package:scad_mobile/src/features/details_page/compare/presentation/pages/compare_details_screen.dart'
    as _i6;
import 'package:scad_mobile/src/features/details_page/compare/presentation/pages/select_comparable_screen.dart'
    as _i35;
import 'package:scad_mobile/src/features/details_page/compute/data/enums/compute_operations.dart'
    as _i59;
import 'package:scad_mobile/src/features/details_page/compute/presentation/pages/compute_details_screen.dart'
    as _i7;
import 'package:scad_mobile/src/features/details_page/compute/presentation/pages/select_computable_screen.dart'
    as _i36;
import 'package:scad_mobile/src/features/details_page/insights_discovery/presentation/pages/insights_discovery_details_screen.dart'
    as _i21;
import 'package:scad_mobile/src/features/details_page/official_experimental/presentation/pages/official_experimental_details_screen.dart'
    as _i27;
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/pages/scenario_driver_modifier_screen.dart'
    as _i32;
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/pages/scenario_forecast_details_screen.dart'
    as _i33;
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart'
    as _i61;
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart'
    as _i60;
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart'
    as _i62;
import 'package:scad_mobile/src/features/domains/presentation/pages/theme_indicators_screen.dart'
    as _i43;
import 'package:scad_mobile/src/features/domains/presentation/pages/themes_page.dart'
    as _i44;
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/pages/about_this_app_screen.dart'
    as _i1;
import 'package:scad_mobile/src/features/drawer_items/contact_us/presentation/pages/contact_us_screen.dart'
    as _i8;
import 'package:scad_mobile/src/features/drawer_items/contact_us/presentation/pages/contact_us_success_page.dart'
    as _i9;
import 'package:scad_mobile/src/features/drawer_items/download_history/download_history_screen.dart'
    as _i11;
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/pages/feedback_screen.dart'
    as _i13;
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/pages/feedback_success_screen.dart'
    as _i14;
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/pages/glossary_screen.dart'
    as _i17;
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/pages/terms_and_conditions_screen.dart'
    as _i42;
import 'package:scad_mobile/src/features/drawer_items/user_guide/user_guide_screen.dart'
    as _i45;
import 'package:scad_mobile/src/features/gen_ai/presentation/pages/gen_ai_screen.dart'
    as _i16;
import 'package:scad_mobile/src/features/home/<USER>/models/sla_check_response.dart'
    as _i55;
import 'package:scad_mobile/src/features/home/<USER>/models/update_response_model.dart'
    as _i47;
import 'package:scad_mobile/src/features/home/<USER>/pages/home/<USER>'
    as _i19;
import 'package:scad_mobile/src/features/home/<USER>/pages/home_navigation.dart'
    as _i18;
import 'package:scad_mobile/src/features/misc_screens/excel_preview_screen.dart'
    as _i12;
import 'package:scad_mobile/src/features/misc_screens/maintenance_screen.dart'
    as _i23;
import 'package:scad_mobile/src/features/misc_screens/pdf_preview_screen.dart'
    as _i29;
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart'
    as _i50;
import 'package:scad_mobile/src/features/my_apps/presentation/pages/collection_indicators.dart'
    as _i5;
import 'package:scad_mobile/src/features/my_apps/presentation/pages/myapps_landing.dart'
    as _i24;
import 'package:scad_mobile/src/features/notification/presentation/pages/notification_list/notification_list.dart'
    as _i26;
import 'package:scad_mobile/src/features/onboarding/presentation/pages/nda_screen.dart'
    as _i25;
import 'package:scad_mobile/src/features/onboarding/presentation/pages/onboarding_main_screen.dart'
    as _i28;
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart'
    as _i54;
import 'package:scad_mobile/src/features/products/presentation/pages/products_screen.dart'
    as _i30;
import 'package:scad_mobile/src/features/products/presentation/pages/tab_pages/dashboard_webpage_screen.dart'
    as _i10;
import 'package:scad_mobile/src/features/search/presentation/pages/search/search_screen.dart'
    as _i34;
import 'package:scad_mobile/src/features/settings/presentation/pages/app_update_screen.dart'
    as _i2;
import 'package:scad_mobile/src/features/settings/presentation/pages/setting_interest_page.dart'
    as _i37;
import 'package:scad_mobile/src/features/settings/presentation/pages/settings_page.dart'
    as _i38;
import 'package:scad_mobile/src/features/spatial_analytics/presentation/pages/spatial_analytics_screen.dart'
    as _i39;
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/pages/spatial_analytics_v2_screen.dart'
    as _i40;

/// generated route for
/// [_i1.AboutThisAppScreen]
class AboutThisAppScreenRoute extends _i46.PageRouteInfo<void> {
  const AboutThisAppScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(AboutThisAppScreenRoute.name, initialChildren: children);

  static const String name = 'AboutThisAppScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i1.AboutThisAppScreen();
    },
  );
}

/// generated route for
/// [_i2.AppUpdateScreen]
class AppUpdateScreenRoute
    extends _i46.PageRouteInfo<AppUpdateScreenRouteArgs> {
  AppUpdateScreenRoute({
    required _i47.UpdateResponse? updateResponse,
    bool startDownloadImmediately = false,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          AppUpdateScreenRoute.name,
          args: AppUpdateScreenRouteArgs(
            updateResponse: updateResponse,
            startDownloadImmediately: startDownloadImmediately,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'AppUpdateScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AppUpdateScreenRouteArgs>();
      return _i2.AppUpdateScreen(
        updateResponse: args.updateResponse,
        startDownloadImmediately: args.startDownloadImmediately,
        key: args.key,
      );
    },
  );
}

class AppUpdateScreenRouteArgs {
  const AppUpdateScreenRouteArgs({
    required this.updateResponse,
    this.startDownloadImmediately = false,
    this.key,
  });

  final _i47.UpdateResponse? updateResponse;

  final bool startDownloadImmediately;

  final _i48.Key? key;

  @override
  String toString() {
    return 'AppUpdateScreenRouteArgs{updateResponse: $updateResponse, startDownloadImmediately: $startDownloadImmediately, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AppUpdateScreenRouteArgs) return false;
    return updateResponse == other.updateResponse &&
        startDownloadImmediately == other.startDownloadImmediately &&
        key == other.key;
  }

  @override
  int get hashCode =>
      updateResponse.hashCode ^
      startDownloadImmediately.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i3.AskUsLandingPage]
class AskUsLandingPageRoute extends _i46.PageRouteInfo<void> {
  const AskUsLandingPageRoute({List<_i46.PageRouteInfo>? children})
      : super(AskUsLandingPageRoute.name, initialChildren: children);

  static const String name = 'AskUsLandingPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i3.AskUsLandingPage();
    },
  );
}

/// generated route for
/// [_i4.ChatWithSmeInboxPage]
class ChatWithSmeInboxPageRoute
    extends _i46.PageRouteInfo<ChatWithSmeInboxPageRouteArgs> {
  ChatWithSmeInboxPageRoute({
    String? title,
    String? domain,
    int? domainId,
    String? theme,
    String? subTheme,
    String? ticketId,
    String? indicatorNodeId,
    String? indicatorAppType,
    String? indicatorContentType,
    String? indicatorKey,
    String? indicatorName,
    String? chatThreadId,
    bool? chatDisabled,
    bool? chatThreadClosed,
    _i49.File? attachFile,
    bool? isRead,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ChatWithSmeInboxPageRoute.name,
          args: ChatWithSmeInboxPageRouteArgs(
            title: title,
            domain: domain,
            domainId: domainId,
            theme: theme,
            subTheme: subTheme,
            ticketId: ticketId,
            indicatorNodeId: indicatorNodeId,
            indicatorAppType: indicatorAppType,
            indicatorContentType: indicatorContentType,
            indicatorKey: indicatorKey,
            indicatorName: indicatorName,
            chatThreadId: chatThreadId,
            chatDisabled: chatDisabled,
            chatThreadClosed: chatThreadClosed,
            attachFile: attachFile,
            isRead: isRead,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ChatWithSmeInboxPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ChatWithSmeInboxPageRouteArgs>(
        orElse: () => const ChatWithSmeInboxPageRouteArgs(),
      );
      return _i4.ChatWithSmeInboxPage(
        title: args.title,
        domain: args.domain,
        domainId: args.domainId,
        theme: args.theme,
        subTheme: args.subTheme,
        ticketId: args.ticketId,
        indicatorNodeId: args.indicatorNodeId,
        indicatorAppType: args.indicatorAppType,
        indicatorContentType: args.indicatorContentType,
        indicatorKey: args.indicatorKey,
        indicatorName: args.indicatorName,
        chatThreadId: args.chatThreadId,
        chatDisabled: args.chatDisabled,
        chatThreadClosed: args.chatThreadClosed,
        attachFile: args.attachFile,
        isRead: args.isRead,
        key: args.key,
      );
    },
  );
}

class ChatWithSmeInboxPageRouteArgs {
  const ChatWithSmeInboxPageRouteArgs({
    this.title,
    this.domain,
    this.domainId,
    this.theme,
    this.subTheme,
    this.ticketId,
    this.indicatorNodeId,
    this.indicatorAppType,
    this.indicatorContentType,
    this.indicatorKey,
    this.indicatorName,
    this.chatThreadId,
    this.chatDisabled,
    this.chatThreadClosed,
    this.attachFile,
    this.isRead,
    this.key,
  });

  final String? title;

  final String? domain;

  final int? domainId;

  final String? theme;

  final String? subTheme;

  final String? ticketId;

  final String? indicatorNodeId;

  final String? indicatorAppType;

  final String? indicatorContentType;

  final String? indicatorKey;

  final String? indicatorName;

  final String? chatThreadId;

  final bool? chatDisabled;

  final bool? chatThreadClosed;

  final _i49.File? attachFile;

  final bool? isRead;

  final _i48.Key? key;

  @override
  String toString() {
    return 'ChatWithSmeInboxPageRouteArgs{title: $title, domain: $domain, domainId: $domainId, theme: $theme, subTheme: $subTheme, ticketId: $ticketId, indicatorNodeId: $indicatorNodeId, indicatorAppType: $indicatorAppType, indicatorContentType: $indicatorContentType, indicatorKey: $indicatorKey, indicatorName: $indicatorName, chatThreadId: $chatThreadId, chatDisabled: $chatDisabled, chatThreadClosed: $chatThreadClosed, attachFile: $attachFile, isRead: $isRead, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ChatWithSmeInboxPageRouteArgs) return false;
    return title == other.title &&
        domain == other.domain &&
        domainId == other.domainId &&
        theme == other.theme &&
        subTheme == other.subTheme &&
        ticketId == other.ticketId &&
        indicatorNodeId == other.indicatorNodeId &&
        indicatorAppType == other.indicatorAppType &&
        indicatorContentType == other.indicatorContentType &&
        indicatorKey == other.indicatorKey &&
        indicatorName == other.indicatorName &&
        chatThreadId == other.chatThreadId &&
        chatDisabled == other.chatDisabled &&
        chatThreadClosed == other.chatThreadClosed &&
        attachFile == other.attachFile &&
        isRead == other.isRead &&
        key == other.key;
  }

  @override
  int get hashCode =>
      title.hashCode ^
      domain.hashCode ^
      domainId.hashCode ^
      theme.hashCode ^
      subTheme.hashCode ^
      ticketId.hashCode ^
      indicatorNodeId.hashCode ^
      indicatorAppType.hashCode ^
      indicatorContentType.hashCode ^
      indicatorKey.hashCode ^
      indicatorName.hashCode ^
      chatThreadId.hashCode ^
      chatDisabled.hashCode ^
      chatThreadClosed.hashCode ^
      attachFile.hashCode ^
      isRead.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i5.CollectionIndicators]
class CollectionIndicatorsRoute
    extends _i46.PageRouteInfo<CollectionIndicatorsRouteArgs> {
  CollectionIndicatorsRoute({
    required String title,
    required _i50.CollectionResult? collection,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          CollectionIndicatorsRoute.name,
          args: CollectionIndicatorsRouteArgs(
            title: title,
            collection: collection,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'CollectionIndicatorsRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CollectionIndicatorsRouteArgs>();
      return _i5.CollectionIndicators(
        title: args.title,
        collection: args.collection,
        key: args.key,
      );
    },
  );
}

class CollectionIndicatorsRouteArgs {
  const CollectionIndicatorsRouteArgs({
    required this.title,
    required this.collection,
    this.key,
  });

  final String title;

  final _i50.CollectionResult? collection;

  final _i48.Key? key;

  @override
  String toString() {
    return 'CollectionIndicatorsRouteArgs{title: $title, collection: $collection, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CollectionIndicatorsRouteArgs) return false;
    return title == other.title &&
        collection == other.collection &&
        key == other.key;
  }

  @override
  int get hashCode => title.hashCode ^ collection.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i6.CompareDetailsScreen]
class CompareDetailsScreenRoute
    extends _i46.PageRouteInfo<CompareDetailsScreenRouteArgs> {
  CompareDetailsScreenRoute({
    required String combinedId,
    _i48.Key? key,
    String? comparedIndicatorName,
    _i51.IndicatorType? indicatorType,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          CompareDetailsScreenRoute.name,
          args: CompareDetailsScreenRouteArgs(
            combinedId: combinedId,
            key: key,
            comparedIndicatorName: comparedIndicatorName,
            indicatorType: indicatorType,
          ),
          initialChildren: children,
        );

  static const String name = 'CompareDetailsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CompareDetailsScreenRouteArgs>();
      return _i6.CompareDetailsScreen(
        combinedId: args.combinedId,
        key: args.key,
        comparedIndicatorName: args.comparedIndicatorName,
        indicatorType: args.indicatorType,
      );
    },
  );
}

class CompareDetailsScreenRouteArgs {
  const CompareDetailsScreenRouteArgs({
    required this.combinedId,
    this.key,
    this.comparedIndicatorName,
    this.indicatorType,
  });

  final String combinedId;

  final _i48.Key? key;

  final String? comparedIndicatorName;

  final _i51.IndicatorType? indicatorType;

  @override
  String toString() {
    return 'CompareDetailsScreenRouteArgs{combinedId: $combinedId, key: $key, comparedIndicatorName: $comparedIndicatorName, indicatorType: $indicatorType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompareDetailsScreenRouteArgs) return false;
    return combinedId == other.combinedId &&
        key == other.key &&
        comparedIndicatorName == other.comparedIndicatorName &&
        indicatorType == other.indicatorType;
  }

  @override
  int get hashCode =>
      combinedId.hashCode ^
      key.hashCode ^
      comparedIndicatorName.hashCode ^
      indicatorType.hashCode;
}

/// generated route for
/// [_i7.ComputeDetailsScreen]
class ComputeDetailsScreenRoute
    extends _i46.PageRouteInfo<ComputeDetailsScreenRouteArgs> {
  ComputeDetailsScreenRoute({
    required String title,
    required _i52.IndicatorDetailsResponse indicatorDetails,
    required _i52.Security? security,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ComputeDetailsScreenRoute.name,
          args: ComputeDetailsScreenRouteArgs(
            title: title,
            indicatorDetails: indicatorDetails,
            security: security,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ComputeDetailsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ComputeDetailsScreenRouteArgs>();
      return _i7.ComputeDetailsScreen(
        title: args.title,
        indicatorDetails: args.indicatorDetails,
        security: args.security,
        key: args.key,
      );
    },
  );
}

class ComputeDetailsScreenRouteArgs {
  const ComputeDetailsScreenRouteArgs({
    required this.title,
    required this.indicatorDetails,
    required this.security,
    this.key,
  });

  final String title;

  final _i52.IndicatorDetailsResponse indicatorDetails;

  final _i52.Security? security;

  final _i48.Key? key;

  @override
  String toString() {
    return 'ComputeDetailsScreenRouteArgs{title: $title, indicatorDetails: $indicatorDetails, security: $security, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ComputeDetailsScreenRouteArgs) return false;
    return title == other.title &&
        indicatorDetails == other.indicatorDetails &&
        security == other.security &&
        key == other.key;
  }

  @override
  int get hashCode =>
      title.hashCode ^
      indicatorDetails.hashCode ^
      security.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i8.ContactUsScreen]
class ContactUsScreenRoute extends _i46.PageRouteInfo<void> {
  const ContactUsScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(ContactUsScreenRoute.name, initialChildren: children);

  static const String name = 'ContactUsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i8.ContactUsScreen();
    },
  );
}

/// generated route for
/// [_i9.ContactUsSuccessPage]
class ContactUsSuccessPageRoute extends _i46.PageRouteInfo<void> {
  const ContactUsSuccessPageRoute({List<_i46.PageRouteInfo>? children})
      : super(ContactUsSuccessPageRoute.name, initialChildren: children);

  static const String name = 'ContactUsSuccessPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i9.ContactUsSuccessPage();
    },
  );
}

/// generated route for
/// [_i10.DashboardWebViewPage]
class DashboardWebViewPageRoute
    extends _i46.PageRouteInfo<DashboardWebViewPageRouteArgs> {
  DashboardWebViewPageRoute({
    required String title,
    required String uuid,
    _i53.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          DashboardWebViewPageRoute.name,
          args: DashboardWebViewPageRouteArgs(
            title: title,
            uuid: uuid,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'DashboardWebViewPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<DashboardWebViewPageRouteArgs>();
      return _i10.DashboardWebViewPage(
        title: args.title,
        uuid: args.uuid,
        key: args.key,
      );
    },
  );
}

class DashboardWebViewPageRouteArgs {
  const DashboardWebViewPageRouteArgs({
    required this.title,
    required this.uuid,
    this.key,
  });

  final String title;

  final String uuid;

  final _i53.Key? key;

  @override
  String toString() {
    return 'DashboardWebViewPageRouteArgs{title: $title, uuid: $uuid, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DashboardWebViewPageRouteArgs) return false;
    return title == other.title && uuid == other.uuid && key == other.key;
  }

  @override
  int get hashCode => title.hashCode ^ uuid.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i11.DownloadHistoryScreen]
class DownloadHistoryScreenRoute extends _i46.PageRouteInfo<void> {
  const DownloadHistoryScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(DownloadHistoryScreenRoute.name, initialChildren: children);

  static const String name = 'DownloadHistoryScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i11.DownloadHistoryScreen();
    },
  );
}

/// generated route for
/// [_i12.ExcelPreviewScreen]
class ExcelPreviewScreenRoute
    extends _i46.PageRouteInfo<ExcelPreviewScreenRouteArgs> {
  ExcelPreviewScreenRoute({
    required String url,
    required String title,
    _i53.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ExcelPreviewScreenRoute.name,
          args: ExcelPreviewScreenRouteArgs(url: url, title: title, key: key),
          initialChildren: children,
        );

  static const String name = 'ExcelPreviewScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ExcelPreviewScreenRouteArgs>();
      return _i12.ExcelPreviewScreen(
        url: args.url,
        title: args.title,
        key: args.key,
      );
    },
  );
}

class ExcelPreviewScreenRouteArgs {
  const ExcelPreviewScreenRouteArgs({
    required this.url,
    required this.title,
    this.key,
  });

  final String url;

  final String title;

  final _i53.Key? key;

  @override
  String toString() {
    return 'ExcelPreviewScreenRouteArgs{url: $url, title: $title, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ExcelPreviewScreenRouteArgs) return false;
    return url == other.url && title == other.title && key == other.key;
  }

  @override
  int get hashCode => url.hashCode ^ title.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i13.FeedbackScreen]
class FeedbackScreenRoute extends _i46.PageRouteInfo<void> {
  const FeedbackScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(FeedbackScreenRoute.name, initialChildren: children);

  static const String name = 'FeedbackScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i13.FeedbackScreen();
    },
  );
}

/// generated route for
/// [_i14.FeedbackSuccessScreen]
class FeedbackSuccessScreenRoute extends _i46.PageRouteInfo<void> {
  const FeedbackSuccessScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(FeedbackSuccessScreenRoute.name, initialChildren: children);

  static const String name = 'FeedbackSuccessScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i14.FeedbackSuccessScreen();
    },
  );
}

/// generated route for
/// [_i15.ForgotPasswordScreen]
class ForgotPasswordScreenRoute extends _i46.PageRouteInfo<void> {
  const ForgotPasswordScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(ForgotPasswordScreenRoute.name, initialChildren: children);

  static const String name = 'ForgotPasswordScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i15.ForgotPasswordScreen();
    },
  );
}

/// generated route for
/// [_i16.GenAIScreen]
class GenAIScreenRoute extends _i46.PageRouteInfo<void> {
  const GenAIScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(GenAIScreenRoute.name, initialChildren: children);

  static const String name = 'GenAIScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i16.GenAIScreen();
    },
  );
}

/// generated route for
/// [_i17.GlossaryScreen]
class GlossaryScreenRoute extends _i46.PageRouteInfo<GlossaryScreenRouteArgs> {
  GlossaryScreenRoute({
    String initialSearchTerm = '',
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          GlossaryScreenRoute.name,
          args: GlossaryScreenRouteArgs(
            initialSearchTerm: initialSearchTerm,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'GlossaryScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GlossaryScreenRouteArgs>(
        orElse: () => const GlossaryScreenRouteArgs(),
      );
      return _i17.GlossaryScreen(
        initialSearchTerm: args.initialSearchTerm,
        key: args.key,
      );
    },
  );
}

class GlossaryScreenRouteArgs {
  const GlossaryScreenRouteArgs({this.initialSearchTerm = '', this.key});

  final String initialSearchTerm;

  final _i48.Key? key;

  @override
  String toString() {
    return 'GlossaryScreenRouteArgs{initialSearchTerm: $initialSearchTerm, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! GlossaryScreenRouteArgs) return false;
    return initialSearchTerm == other.initialSearchTerm && key == other.key;
  }

  @override
  int get hashCode => initialSearchTerm.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i18.HomeNavigation]
class HomeNavigationRoute extends _i46.PageRouteInfo<HomeNavigationRouteArgs> {
  HomeNavigationRoute({
    _i48.Key? key,
    int? screenTabIndex,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          HomeNavigationRoute.name,
          args: HomeNavigationRouteArgs(
            key: key,
            screenTabIndex: screenTabIndex,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeNavigationRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomeNavigationRouteArgs>(
        orElse: () => const HomeNavigationRouteArgs(),
      );
      return _i18.HomeNavigation(
        key: args.key,
        screenTabIndex: args.screenTabIndex,
      );
    },
  );
}

class HomeNavigationRouteArgs {
  const HomeNavigationRouteArgs({this.key, this.screenTabIndex});

  final _i48.Key? key;

  final int? screenTabIndex;

  @override
  String toString() {
    return 'HomeNavigationRouteArgs{key: $key, screenTabIndex: $screenTabIndex}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomeNavigationRouteArgs) return false;
    return key == other.key && screenTabIndex == other.screenTabIndex;
  }

  @override
  int get hashCode => key.hashCode ^ screenTabIndex.hashCode;
}

/// generated route for
/// [_i19.HomePage]
class HomePageRoute extends _i46.PageRouteInfo<HomePageRouteArgs> {
  HomePageRoute({
    required _i48.VoidCallback navToDomainsPage,
    required void Function(_i54.ProductTabType) navToProductsPage,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          HomePageRoute.name,
          args: HomePageRouteArgs(
            navToDomainsPage: navToDomainsPage,
            navToProductsPage: navToProductsPage,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'HomePageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomePageRouteArgs>();
      return _i19.HomePage(
        navToDomainsPage: args.navToDomainsPage,
        navToProductsPage: args.navToProductsPage,
        key: args.key,
      );
    },
  );
}

class HomePageRouteArgs {
  const HomePageRouteArgs({
    required this.navToDomainsPage,
    required this.navToProductsPage,
    this.key,
  });

  final _i48.VoidCallback navToDomainsPage;

  final void Function(_i54.ProductTabType) navToProductsPage;

  final _i48.Key? key;

  @override
  String toString() {
    return 'HomePageRouteArgs{navToDomainsPage: $navToDomainsPage, navToProductsPage: $navToProductsPage, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomePageRouteArgs) return false;
    return navToDomainsPage == other.navToDomainsPage && key == other.key;
  }

  @override
  int get hashCode => navToDomainsPage.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i20.ImageViewerScreen]
class ImageViewerScreenRoute
    extends _i46.PageRouteInfo<ImageViewerScreenRouteArgs> {
  ImageViewerScreenRoute({
    String? url,
    String? filePath,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ImageViewerScreenRoute.name,
          args: ImageViewerScreenRouteArgs(
            url: url,
            filePath: filePath,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ImageViewerScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ImageViewerScreenRouteArgs>(
        orElse: () => const ImageViewerScreenRouteArgs(),
      );
      return _i20.ImageViewerScreen(
        url: args.url,
        filePath: args.filePath,
        key: args.key,
      );
    },
  );
}

class ImageViewerScreenRouteArgs {
  const ImageViewerScreenRouteArgs({this.url, this.filePath, this.key});

  final String? url;

  final String? filePath;

  final _i48.Key? key;

  @override
  String toString() {
    return 'ImageViewerScreenRouteArgs{url: $url, filePath: $filePath, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ImageViewerScreenRouteArgs) return false;
    return url == other.url && filePath == other.filePath && key == other.key;
  }

  @override
  int get hashCode => url.hashCode ^ filePath.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i21.InsightsDiscoveryDetailsScreen]
class InsightsDiscoveryDetailsScreenRoute
    extends _i46.PageRouteInfo<InsightsDiscoveryDetailsScreenRouteArgs> {
  InsightsDiscoveryDetailsScreenRoute({
    required String id,
    required String contentType,
    required String title,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          InsightsDiscoveryDetailsScreenRoute.name,
          args: InsightsDiscoveryDetailsScreenRouteArgs(
            id: id,
            contentType: contentType,
            title: title,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'InsightsDiscoveryDetailsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<InsightsDiscoveryDetailsScreenRouteArgs>();
      return _i21.InsightsDiscoveryDetailsScreen(
        id: args.id,
        contentType: args.contentType,
        title: args.title,
        key: args.key,
      );
    },
  );
}

class InsightsDiscoveryDetailsScreenRouteArgs {
  const InsightsDiscoveryDetailsScreenRouteArgs({
    required this.id,
    required this.contentType,
    required this.title,
    this.key,
  });

  final String id;

  final String contentType;

  final String title;

  final _i48.Key? key;

  @override
  String toString() {
    return 'InsightsDiscoveryDetailsScreenRouteArgs{id: $id, contentType: $contentType, title: $title, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! InsightsDiscoveryDetailsScreenRouteArgs) return false;
    return id == other.id &&
        contentType == other.contentType &&
        title == other.title &&
        key == other.key;
  }

  @override
  int get hashCode =>
      id.hashCode ^ contentType.hashCode ^ title.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i22.LoginPage]
class LoginPageRoute extends _i46.PageRouteInfo<LoginPageRouteArgs> {
  LoginPageRoute({
    bool isFromPasswordReset = false,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          LoginPageRoute.name,
          args: LoginPageRouteArgs(
            isFromPasswordReset: isFromPasswordReset,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'LoginPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<LoginPageRouteArgs>(
        orElse: () => const LoginPageRouteArgs(),
      );
      return _i22.LoginPage(
        isFromPasswordReset: args.isFromPasswordReset,
        key: args.key,
      );
    },
  );
}

class LoginPageRouteArgs {
  const LoginPageRouteArgs({this.isFromPasswordReset = false, this.key});

  final bool isFromPasswordReset;

  final _i48.Key? key;

  @override
  String toString() {
    return 'LoginPageRouteArgs{isFromPasswordReset: $isFromPasswordReset, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LoginPageRouteArgs) return false;
    return isFromPasswordReset == other.isFromPasswordReset && key == other.key;
  }

  @override
  int get hashCode => isFromPasswordReset.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i23.MaintenanceScreen]
class MaintenanceScreenRoute
    extends _i46.PageRouteInfo<MaintenanceScreenRouteArgs> {
  MaintenanceScreenRoute({
    required _i55.SlaCheckResponse data,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          MaintenanceScreenRoute.name,
          args: MaintenanceScreenRouteArgs(data: data, key: key),
          initialChildren: children,
        );

  static const String name = 'MaintenanceScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MaintenanceScreenRouteArgs>();
      return _i23.MaintenanceScreen(data: args.data, key: args.key);
    },
  );
}

class MaintenanceScreenRouteArgs {
  const MaintenanceScreenRouteArgs({required this.data, this.key});

  final _i55.SlaCheckResponse data;

  final _i48.Key? key;

  @override
  String toString() {
    return 'MaintenanceScreenRouteArgs{data: $data, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MaintenanceScreenRouteArgs) return false;
    return data == other.data && key == other.key;
  }

  @override
  int get hashCode => data.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i24.MyAppsLandingPage]
class MyAppsLandingPageRoute extends _i46.PageRouteInfo<void> {
  const MyAppsLandingPageRoute({List<_i46.PageRouteInfo>? children})
      : super(MyAppsLandingPageRoute.name, initialChildren: children);

  static const String name = 'MyAppsLandingPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i24.MyAppsLandingPage();
    },
  );
}

/// generated route for
/// [_i25.NDAScreen]
class NDAScreenRoute extends _i46.PageRouteInfo<void> {
  const NDAScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(NDAScreenRoute.name, initialChildren: children);

  static const String name = 'NDAScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i25.NDAScreen();
    },
  );
}

/// generated route for
/// [_i26.NotificationList]
class NotificationListRoute extends _i46.PageRouteInfo<void> {
  const NotificationListRoute({List<_i46.PageRouteInfo>? children})
      : super(NotificationListRoute.name, initialChildren: children);

  static const String name = 'NotificationListRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i26.NotificationList();
    },
  );
}

/// generated route for
/// [_i27.OfficialExperimentalDetailsScreen]
class OfficialExperimentalDetailsScreenRoute
    extends _i46.PageRouteInfo<OfficialExperimentalDetailsScreenRouteArgs> {
  OfficialExperimentalDetailsScreenRoute({
    required String id,
    required String contentType,
    required String title,
    _i56.JSONObject? screenerPayload,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          OfficialExperimentalDetailsScreenRoute.name,
          args: OfficialExperimentalDetailsScreenRouteArgs(
            id: id,
            contentType: contentType,
            title: title,
            screenerPayload: screenerPayload,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'OfficialExperimentalDetailsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OfficialExperimentalDetailsScreenRouteArgs>();
      return _i27.OfficialExperimentalDetailsScreen(
        id: args.id,
        contentType: args.contentType,
        title: args.title,
        screenerPayload: args.screenerPayload,
        key: args.key,
      );
    },
  );
}

class OfficialExperimentalDetailsScreenRouteArgs {
  const OfficialExperimentalDetailsScreenRouteArgs({
    required this.id,
    required this.contentType,
    required this.title,
    this.screenerPayload,
    this.key,
  });

  final String id;

  final String contentType;

  final String title;

  final _i56.JSONObject? screenerPayload;

  final _i48.Key? key;

  @override
  String toString() {
    return 'OfficialExperimentalDetailsScreenRouteArgs{id: $id, contentType: $contentType, title: $title, screenerPayload: $screenerPayload, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OfficialExperimentalDetailsScreenRouteArgs) return false;
    return id == other.id &&
        contentType == other.contentType &&
        title == other.title &&
        screenerPayload == other.screenerPayload &&
        key == other.key;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      contentType.hashCode ^
      title.hashCode ^
      screenerPayload.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i28.OnboardingScreen]
class OnboardingScreenRoute extends _i46.PageRouteInfo<void> {
  const OnboardingScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(OnboardingScreenRoute.name, initialChildren: children);

  static const String name = 'OnboardingScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i28.OnboardingScreen();
    },
  );
}

/// generated route for
/// [_i29.PdfPreviewScreen]
class PdfPreviewScreenRoute
    extends _i46.PageRouteInfo<PdfPreviewScreenRouteArgs> {
  PdfPreviewScreenRoute({
    required String title,
    required String url,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          PdfPreviewScreenRoute.name,
          args: PdfPreviewScreenRouteArgs(title: title, url: url, key: key),
          initialChildren: children,
        );

  static const String name = 'PdfPreviewScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PdfPreviewScreenRouteArgs>();
      return _i29.PdfPreviewScreen(
        title: args.title,
        url: args.url,
        key: args.key,
      );
    },
  );
}

class PdfPreviewScreenRouteArgs {
  const PdfPreviewScreenRouteArgs({
    required this.title,
    required this.url,
    this.key,
  });

  final String title;

  final String url;

  final _i48.Key? key;

  @override
  String toString() {
    return 'PdfPreviewScreenRouteArgs{title: $title, url: $url, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PdfPreviewScreenRouteArgs) return false;
    return title == other.title && url == other.url && key == other.key;
  }

  @override
  int get hashCode => title.hashCode ^ url.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i30.Products]
class ProductsRoute extends _i46.PageRouteInfo<void> {
  const ProductsRoute({List<_i46.PageRouteInfo>? children})
      : super(ProductsRoute.name, initialChildren: children);

  static const String name = 'ProductsRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i30.Products();
    },
  );
}

/// generated route for
/// [_i31.ResetPasswordScreen]
class ResetPasswordScreenRoute
    extends _i46.PageRouteInfo<ResetPasswordScreenRouteArgs> {
  ResetPasswordScreenRoute({
    required String token,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ResetPasswordScreenRoute.name,
          args: ResetPasswordScreenRouteArgs(token: token, key: key),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordScreenRouteArgs>();
      return _i31.ResetPasswordScreen(token: args.token, key: args.key);
    },
  );
}

class ResetPasswordScreenRouteArgs {
  const ResetPasswordScreenRouteArgs({required this.token, this.key});

  final String token;

  final _i48.Key? key;

  @override
  String toString() {
    return 'ResetPasswordScreenRouteArgs{token: $token, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ResetPasswordScreenRouteArgs) return false;
    return token == other.token && key == other.key;
  }

  @override
  int get hashCode => token.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i32.ScenarioDriverModifierScreen]
class ScenarioDriverModifierScreenRoute
    extends _i46.PageRouteInfo<ScenarioDriverModifierScreenRouteArgs> {
  ScenarioDriverModifierScreenRoute({
    required _i52.IndicatorDetailsResponse indicatorDetails,
    required Map<String, String> payload,
    required String contentType,
    required bool isScadProjectionOn,
    int index = 0,
    _i57.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ScenarioDriverModifierScreenRoute.name,
          args: ScenarioDriverModifierScreenRouteArgs(
            indicatorDetails: indicatorDetails,
            payload: payload,
            contentType: contentType,
            isScadProjectionOn: isScadProjectionOn,
            index: index,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ScenarioDriverModifierScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ScenarioDriverModifierScreenRouteArgs>();
      return _i32.ScenarioDriverModifierScreen(
        indicatorDetails: args.indicatorDetails,
        payload: args.payload,
        contentType: args.contentType,
        isScadProjectionOn: args.isScadProjectionOn,
        index: args.index,
        key: args.key,
      );
    },
  );
}

class ScenarioDriverModifierScreenRouteArgs {
  const ScenarioDriverModifierScreenRouteArgs({
    required this.indicatorDetails,
    required this.payload,
    required this.contentType,
    required this.isScadProjectionOn,
    this.index = 0,
    this.key,
  });

  final _i52.IndicatorDetailsResponse indicatorDetails;

  final Map<String, String> payload;

  final String contentType;

  final bool isScadProjectionOn;

  final int index;

  final _i57.Key? key;

  @override
  String toString() {
    return 'ScenarioDriverModifierScreenRouteArgs{indicatorDetails: $indicatorDetails, payload: $payload, contentType: $contentType, isScadProjectionOn: $isScadProjectionOn, index: $index, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ScenarioDriverModifierScreenRouteArgs) return false;
    return indicatorDetails == other.indicatorDetails &&
        const _i58.MapEquality().equals(payload, other.payload) &&
        contentType == other.contentType &&
        isScadProjectionOn == other.isScadProjectionOn &&
        index == other.index &&
        key == other.key;
  }

  @override
  int get hashCode =>
      indicatorDetails.hashCode ^
      const _i58.MapEquality().hash(payload) ^
      contentType.hashCode ^
      isScadProjectionOn.hashCode ^
      index.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i33.ScenarioForecastDetailsScreen]
class ScenarioForecastDetailsScreenRoute
    extends _i46.PageRouteInfo<ScenarioForecastDetailsScreenRouteArgs> {
  ScenarioForecastDetailsScreenRoute({
    required String id,
    required String contentType,
    required String title,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ScenarioForecastDetailsScreenRoute.name,
          args: ScenarioForecastDetailsScreenRouteArgs(
            id: id,
            contentType: contentType,
            title: title,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ScenarioForecastDetailsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ScenarioForecastDetailsScreenRouteArgs>();
      return _i33.ScenarioForecastDetailsScreen(
        id: args.id,
        contentType: args.contentType,
        title: args.title,
        key: args.key,
      );
    },
  );
}

class ScenarioForecastDetailsScreenRouteArgs {
  const ScenarioForecastDetailsScreenRouteArgs({
    required this.id,
    required this.contentType,
    required this.title,
    this.key,
  });

  final String id;

  final String contentType;

  final String title;

  final _i48.Key? key;

  @override
  String toString() {
    return 'ScenarioForecastDetailsScreenRouteArgs{id: $id, contentType: $contentType, title: $title, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ScenarioForecastDetailsScreenRouteArgs) return false;
    return id == other.id &&
        contentType == other.contentType &&
        title == other.title &&
        key == other.key;
  }

  @override
  int get hashCode =>
      id.hashCode ^ contentType.hashCode ^ title.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i34.SearchScreen]
class SearchScreenRoute extends _i46.PageRouteInfo<SearchScreenRouteArgs> {
  SearchScreenRoute({
    _i48.Key? key,
    _i34.SearchTypes? type,
    String? contentType,
    String? initialNodeIdForComparison,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SearchScreenRoute.name,
          args: SearchScreenRouteArgs(
            key: key,
            type: type,
            contentType: contentType,
            initialNodeIdForComparison: initialNodeIdForComparison,
          ),
          initialChildren: children,
        );

  static const String name = 'SearchScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SearchScreenRouteArgs>(
        orElse: () => const SearchScreenRouteArgs(),
      );
      return _i34.SearchScreen(
        key: args.key,
        type: args.type,
        contentType: args.contentType,
        initialNodeIdForComparison: args.initialNodeIdForComparison,
      );
    },
  );
}

class SearchScreenRouteArgs {
  const SearchScreenRouteArgs({
    this.key,
    this.type,
    this.contentType,
    this.initialNodeIdForComparison,
  });

  final _i48.Key? key;

  final _i34.SearchTypes? type;

  final String? contentType;

  final String? initialNodeIdForComparison;

  @override
  String toString() {
    return 'SearchScreenRouteArgs{key: $key, type: $type, contentType: $contentType, initialNodeIdForComparison: $initialNodeIdForComparison}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SearchScreenRouteArgs) return false;
    return key == other.key &&
        type == other.type &&
        contentType == other.contentType &&
        initialNodeIdForComparison == other.initialNodeIdForComparison;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      type.hashCode ^
      contentType.hashCode ^
      initialNodeIdForComparison.hashCode;
}

/// generated route for
/// [_i35.SelectComparableScreen]
class SelectComparableScreenRoute
    extends _i46.PageRouteInfo<SelectComparableScreenRouteArgs> {
  SelectComparableScreenRoute({
    required _i52.IndicatorDetailsResponse indicatorDetails,
    _i56.JSONObject? screener,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SelectComparableScreenRoute.name,
          args: SelectComparableScreenRouteArgs(
            indicatorDetails: indicatorDetails,
            screener: screener,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectComparableScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectComparableScreenRouteArgs>();
      return _i35.SelectComparableScreen(
        indicatorDetails: args.indicatorDetails,
        screener: args.screener,
        key: args.key,
      );
    },
  );
}

class SelectComparableScreenRouteArgs {
  const SelectComparableScreenRouteArgs({
    required this.indicatorDetails,
    this.screener,
    this.key,
  });

  final _i52.IndicatorDetailsResponse indicatorDetails;

  final _i56.JSONObject? screener;

  final _i48.Key? key;

  @override
  String toString() {
    return 'SelectComparableScreenRouteArgs{indicatorDetails: $indicatorDetails, screener: $screener, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SelectComparableScreenRouteArgs) return false;
    return indicatorDetails == other.indicatorDetails &&
        screener == other.screener &&
        key == other.key;
  }

  @override
  int get hashCode =>
      indicatorDetails.hashCode ^ screener.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i36.SelectComputableScreen]
class SelectComputableScreenRoute
    extends _i46.PageRouteInfo<SelectComputableScreenRouteArgs> {
  SelectComputableScreenRoute({
    required String indicatorId,
    required _i59.ComputeOperations operation,
    required List<_i52.Properties> propertiesList,
    required _i52.Security? security,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SelectComputableScreenRoute.name,
          args: SelectComputableScreenRouteArgs(
            indicatorId: indicatorId,
            operation: operation,
            propertiesList: propertiesList,
            security: security,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectComputableScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectComputableScreenRouteArgs>();
      return _i36.SelectComputableScreen(
        indicatorId: args.indicatorId,
        operation: args.operation,
        propertiesList: args.propertiesList,
        security: args.security,
        key: args.key,
      );
    },
  );
}

class SelectComputableScreenRouteArgs {
  const SelectComputableScreenRouteArgs({
    required this.indicatorId,
    required this.operation,
    required this.propertiesList,
    required this.security,
    this.key,
  });

  final String indicatorId;

  final _i59.ComputeOperations operation;

  final List<_i52.Properties> propertiesList;

  final _i52.Security? security;

  final _i48.Key? key;

  @override
  String toString() {
    return 'SelectComputableScreenRouteArgs{indicatorId: $indicatorId, operation: $operation, propertiesList: $propertiesList, security: $security, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SelectComputableScreenRouteArgs) return false;
    return indicatorId == other.indicatorId &&
        operation == other.operation &&
        const _i58.ListEquality().equals(
          propertiesList,
          other.propertiesList,
        ) &&
        security == other.security &&
        key == other.key;
  }

  @override
  int get hashCode =>
      indicatorId.hashCode ^
      operation.hashCode ^
      const _i58.ListEquality().hash(propertiesList) ^
      security.hashCode ^
      key.hashCode;
}

/// generated route for
/// [_i37.SettingInterestPage]
class SettingInterestPageRoute extends _i46.PageRouteInfo<void> {
  const SettingInterestPageRoute({List<_i46.PageRouteInfo>? children})
      : super(SettingInterestPageRoute.name, initialChildren: children);

  static const String name = 'SettingInterestPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i37.SettingInterestPage();
    },
  );
}

/// generated route for
/// [_i38.SettingsScreen]
class SettingsScreenRoute extends _i46.PageRouteInfo<void> {
  const SettingsScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(SettingsScreenRoute.name, initialChildren: children);

  static const String name = 'SettingsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i38.SettingsScreen();
    },
  );
}

/// generated route for
/// [_i39.SpatialAnalyticsScreen]
class SpatialAnalyticsScreenRoute
    extends _i46.PageRouteInfo<SpatialAnalyticsScreenRouteArgs> {
  SpatialAnalyticsScreenRoute({
    String? initialModuleKey,
    void Function(bool)? postMyAppButtonTap,
    _i53.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SpatialAnalyticsScreenRoute.name,
          args: SpatialAnalyticsScreenRouteArgs(
            initialModuleKey: initialModuleKey,
            postMyAppButtonTap: postMyAppButtonTap,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SpatialAnalyticsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SpatialAnalyticsScreenRouteArgs>(
        orElse: () => const SpatialAnalyticsScreenRouteArgs(),
      );
      return _i39.SpatialAnalyticsScreen(
        initialModuleKey: args.initialModuleKey,
        postMyAppButtonTap: args.postMyAppButtonTap,
        key: args.key,
      );
    },
  );
}

class SpatialAnalyticsScreenRouteArgs {
  const SpatialAnalyticsScreenRouteArgs({
    this.initialModuleKey,
    this.postMyAppButtonTap,
    this.key,
  });

  final String? initialModuleKey;

  final void Function(bool)? postMyAppButtonTap;

  final _i53.Key? key;

  @override
  String toString() {
    return 'SpatialAnalyticsScreenRouteArgs{initialModuleKey: $initialModuleKey, postMyAppButtonTap: $postMyAppButtonTap, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SpatialAnalyticsScreenRouteArgs) return false;
    return initialModuleKey == other.initialModuleKey && key == other.key;
  }

  @override
  int get hashCode => initialModuleKey.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i40.SpatialAnalyticsV2Screen]
class SpatialAnalyticsV2ScreenRoute
    extends _i46.PageRouteInfo<SpatialAnalyticsV2ScreenRouteArgs> {
  SpatialAnalyticsV2ScreenRoute({
    int initialDomainId = 1,
    _i53.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SpatialAnalyticsV2ScreenRoute.name,
          args: SpatialAnalyticsV2ScreenRouteArgs(
            initialDomainId: initialDomainId,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SpatialAnalyticsV2ScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SpatialAnalyticsV2ScreenRouteArgs>(
        orElse: () => const SpatialAnalyticsV2ScreenRouteArgs(),
      );
      return _i40.SpatialAnalyticsV2Screen(
        initialDomainId: args.initialDomainId,
        key: args.key,
      );
    },
  );
}

class SpatialAnalyticsV2ScreenRouteArgs {
  const SpatialAnalyticsV2ScreenRouteArgs({this.initialDomainId = 1, this.key});

  final int initialDomainId;

  final _i53.Key? key;

  @override
  String toString() {
    return 'SpatialAnalyticsV2ScreenRouteArgs{initialDomainId: $initialDomainId, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SpatialAnalyticsV2ScreenRouteArgs) return false;
    return initialDomainId == other.initialDomainId && key == other.key;
  }

  @override
  int get hashCode => initialDomainId.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i41.SplashPage]
class SplashPageRoute extends _i46.PageRouteInfo<SplashPageRouteArgs> {
  SplashPageRoute({
    bool toAnimateLogo = true,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          SplashPageRoute.name,
          args: SplashPageRouteArgs(toAnimateLogo: toAnimateLogo, key: key),
          initialChildren: children,
        );

  static const String name = 'SplashPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SplashPageRouteArgs>(
        orElse: () => const SplashPageRouteArgs(),
      );
      return _i41.SplashPage(toAnimateLogo: args.toAnimateLogo, key: args.key);
    },
  );
}

class SplashPageRouteArgs {
  const SplashPageRouteArgs({this.toAnimateLogo = true, this.key});

  final bool toAnimateLogo;

  final _i48.Key? key;

  @override
  String toString() {
    return 'SplashPageRouteArgs{toAnimateLogo: $toAnimateLogo, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! SplashPageRouteArgs) return false;
    return toAnimateLogo == other.toAnimateLogo && key == other.key;
  }

  @override
  int get hashCode => toAnimateLogo.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i42.TermsAndConditionsScreen]
class TermsAndConditionsScreenRoute
    extends _i46.PageRouteInfo<TermsAndConditionsScreenRouteArgs> {
  TermsAndConditionsScreenRoute({
    bool showAppDrawer = false,
    _i48.Key? key,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          TermsAndConditionsScreenRoute.name,
          args: TermsAndConditionsScreenRouteArgs(
            showAppDrawer: showAppDrawer,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'TermsAndConditionsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TermsAndConditionsScreenRouteArgs>(
        orElse: () => const TermsAndConditionsScreenRouteArgs(),
      );
      return _i42.TermsAndConditionsScreen(
        showAppDrawer: args.showAppDrawer,
        key: args.key,
      );
    },
  );
}

class TermsAndConditionsScreenRouteArgs {
  const TermsAndConditionsScreenRouteArgs({
    this.showAppDrawer = false,
    this.key,
  });

  final bool showAppDrawer;

  final _i48.Key? key;

  @override
  String toString() {
    return 'TermsAndConditionsScreenRouteArgs{showAppDrawer: $showAppDrawer, key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TermsAndConditionsScreenRouteArgs) return false;
    return showAppDrawer == other.showAppDrawer && key == other.key;
  }

  @override
  int get hashCode => showAppDrawer.hashCode ^ key.hashCode;
}

/// generated route for
/// [_i43.ThemeIndicatorsScreen]
class ThemeIndicatorsScreenRoute
    extends _i46.PageRouteInfo<ThemeIndicatorsScreenRouteArgs> {
  ThemeIndicatorsScreenRoute({
    required String title,
    required _i60.DomainModel domain,
    required _i61.DomainClassificationModel classification,
    required _i62.SubTheme subTheme,
    required _i62.ThemeSubThemeResponse subDomain,
    _i48.Key? key,
    _i62.ScreenerConfiguration? screenerConfiguration,
    _i50.CollectionResult? collection,
    bool isFromMainScreen = false,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ThemeIndicatorsScreenRoute.name,
          args: ThemeIndicatorsScreenRouteArgs(
            title: title,
            domain: domain,
            classification: classification,
            subTheme: subTheme,
            subDomain: subDomain,
            key: key,
            screenerConfiguration: screenerConfiguration,
            collection: collection,
            isFromMainScreen: isFromMainScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ThemeIndicatorsScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ThemeIndicatorsScreenRouteArgs>();
      return _i43.ThemeIndicatorsScreen(
        title: args.title,
        domain: args.domain,
        classification: args.classification,
        subTheme: args.subTheme,
        subDomain: args.subDomain,
        key: args.key,
        screenerConfiguration: args.screenerConfiguration,
        collection: args.collection,
        isFromMainScreen: args.isFromMainScreen,
      );
    },
  );
}

class ThemeIndicatorsScreenRouteArgs {
  const ThemeIndicatorsScreenRouteArgs({
    required this.title,
    required this.domain,
    required this.classification,
    required this.subTheme,
    required this.subDomain,
    this.key,
    this.screenerConfiguration,
    this.collection,
    this.isFromMainScreen = false,
  });

  final String title;

  final _i60.DomainModel domain;

  final _i61.DomainClassificationModel classification;

  final _i62.SubTheme subTheme;

  final _i62.ThemeSubThemeResponse subDomain;

  final _i48.Key? key;

  final _i62.ScreenerConfiguration? screenerConfiguration;

  final _i50.CollectionResult? collection;

  final bool isFromMainScreen;

  @override
  String toString() {
    return 'ThemeIndicatorsScreenRouteArgs{title: $title, domain: $domain, classification: $classification, subTheme: $subTheme, subDomain: $subDomain, key: $key, screenerConfiguration: $screenerConfiguration, collection: $collection, isFromMainScreen: $isFromMainScreen}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ThemeIndicatorsScreenRouteArgs) return false;
    return title == other.title &&
        domain == other.domain &&
        classification == other.classification &&
        subTheme == other.subTheme &&
        subDomain == other.subDomain &&
        key == other.key &&
        screenerConfiguration == other.screenerConfiguration &&
        collection == other.collection &&
        isFromMainScreen == other.isFromMainScreen;
  }

  @override
  int get hashCode =>
      title.hashCode ^
      domain.hashCode ^
      classification.hashCode ^
      subTheme.hashCode ^
      subDomain.hashCode ^
      key.hashCode ^
      screenerConfiguration.hashCode ^
      collection.hashCode ^
      isFromMainScreen.hashCode;
}

/// generated route for
/// [_i44.ThemesPage]
class ThemesPageRoute extends _i46.PageRouteInfo<ThemesPageRouteArgs> {
  ThemesPageRoute({
    String? domainId,
    _i48.Key? key,
    _i50.CollectionResult? collection,
    List<_i46.PageRouteInfo>? children,
  }) : super(
          ThemesPageRoute.name,
          args: ThemesPageRouteArgs(
            domainId: domainId,
            key: key,
            collection: collection,
          ),
          initialChildren: children,
        );

  static const String name = 'ThemesPageRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ThemesPageRouteArgs>(
        orElse: () => const ThemesPageRouteArgs(),
      );
      return _i44.ThemesPage(
        domainId: args.domainId,
        key: args.key,
        collection: args.collection,
      );
    },
  );
}

class ThemesPageRouteArgs {
  const ThemesPageRouteArgs({this.domainId, this.key, this.collection});

  final String? domainId;

  final _i48.Key? key;

  final _i50.CollectionResult? collection;

  @override
  String toString() {
    return 'ThemesPageRouteArgs{domainId: $domainId, key: $key, collection: $collection}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ThemesPageRouteArgs) return false;
    return domainId == other.domainId &&
        key == other.key &&
        collection == other.collection;
  }

  @override
  int get hashCode => domainId.hashCode ^ key.hashCode ^ collection.hashCode;
}

/// generated route for
/// [_i45.UserGuideScreen]
class UserGuideScreenRoute extends _i46.PageRouteInfo<void> {
  const UserGuideScreenRoute({List<_i46.PageRouteInfo>? children})
      : super(UserGuideScreenRoute.name, initialChildren: children);

  static const String name = 'UserGuideScreenRoute';

  static _i46.PageInfo page = _i46.PageInfo(
    name,
    builder: (data) {
      return const _i45.UserGuideScreen();
    },
  );
}
