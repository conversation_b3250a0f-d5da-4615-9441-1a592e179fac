import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/views/ask_us_thread_list_page.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/pages/views/faq_page.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/faq_list_widget.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class AskUsLandingPage extends StatefulWidget {
  const AskUsLandingPage({super.key});

  @override
  State<AskUsLandingPage> createState() => _AskUsLandingPageState();
}

class _AskUsLandingPageState extends State<AskUsLandingPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final bool isLightMode = HiveUtilsSettings.isLightMode;

  final ValueNotifier<int> _tabIndex = ValueNotifier(0);
  late final TabController _tabController;
  final ScrollController chatThreadScrollController = ScrollController();
  final ScrollController generalFaqScrollController = ScrollController();
  final ScrollController domainFaqScrollController = ScrollController();

  final GlobalKey chatThreadKey = GlobalKey(debugLabel: 'chatThreadKey');
  final GlobalKey mostPopularFAQsKey = GlobalKey(debugLabel: 'mostPopularFAQsKey');
  final GlobalKey submitQuestionKey = GlobalKey(debugLabel: 'submitQuestionKey');

  List<GlobalKey> steps = [];

  @override
  void initState() {
    super.initState();

    if (!isDemoMode) {
      steps = [chatThreadKey, mostPopularFAQsKey, submitQuestionKey];
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.AskUs) {
          ShowCaseWidget.of(context).startShowCase(steps);
        }
      });
      _tabController = TabController(length: 2, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          ValueListenableBuilder(
            valueListenable: _tabIndex,
            builder: (context, _, __) {
              return FlatAppBar(
                key: Key('${_tabIndex.value}'),
                title: LocaleKeys.askUs.tr(),
                mainScreen: true,
                appBarHeight: 10,
                bottomPadding: 10,
                scrollController: isDemoMode
                    ? null
                    : _tabIndex.value == 0
                        ? chatThreadScrollController
                        : _tabIndex.value == 1
                            ? generalFaqScrollController
                            : domainFaqScrollController,
              );
            },
          ),
          if (isDemoMode)
            _demoModeContent()
          else ...[
            TabBar(
              controller: _tabController,
              overlayColor: WidgetStateProperty.all<Color>(
                AppColors.blueLight.withValues(alpha: 0.1),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  width: 3,
                  color: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                ),
                insets: const EdgeInsets.symmetric(horizontal: 24),
              ),
              onTap: (i) {
                _tabIndex.value = i;
              },
              tabs: [
                IntroWidget(
                  stepKey: chatThreadKey,
                  stepIndex: 1,
                  totalSteps: steps.length,
                  title: LocaleKeys.chatThreads.tr(),
                  description: LocaleKeys.chatThreadsGuideDesc.tr(),
                  arrowAlignment: Alignment.topLeft,
                  targetBorderRadius: 6,
                  arrowPadding: EdgeInsets.only(
                    right: MediaQuery.sizeOf(context).width * 0.2,
                    left: MediaQuery.sizeOf(context).width * 0.2,
                    bottom: 10,
                  ),
                  targetPadding: const EdgeInsets.symmetric(horizontal: 44),
                  child: Tab(
                    child: Text(
                      LocaleKeys.chatThreads.tr(),
                    ),
                  ),
                ),
                IntroWidget(
                  stepKey: mostPopularFAQsKey,
                  stepIndex: 2,
                  totalSteps: steps.length,
                  title: LocaleKeys.mostPopularFAQ.tr(),
                  description: LocaleKeys.mostPopularFAQGuideDesc.tr(),
                  arrowAlignment: Alignment.topRight,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  targetBorderRadius: 6,
                  arrowPadding: EdgeInsets.only(
                    right: MediaQuery.sizeOf(context).width * 0.18,
                    left: MediaQuery.sizeOf(context).width * 0.18,
                    bottom: 10,
                  ),
                  targetPadding: const EdgeInsets.symmetric(horizontal: 30),
                  child: Tab(
                    child: Text(
                      LocaleKeys.mostPopularFAQ.tr(),
                    ),
                  ),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  if (!isDemoMode)
                    AskUsThreadListPage(
                      scrollController: chatThreadScrollController,
                      steps: steps,
                      submitQuestionKey: submitQuestionKey,
                    ),
                  FaqPage(
                    generalFaqScrollController: generalFaqScrollController,
                    domainFaqScrollController: domainFaqScrollController,
                    mostPopularFAQsKey: mostPopularFAQsKey,
                    steps: steps,
                    onFaqTabChange: (int i) {
                      _tabIndex.value = 1 + i;
                    },
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _demoModeContent() {
    final List<dynamic> list = demoFaqResponse as List<dynamic>;

    final List<FaqModel> faqList = list.map((e) => FaqModel.fromJson(e as Map<String, dynamic>)).toList();

    return Expanded(
      child: SingleChildScrollView(
        controller: generalFaqScrollController,
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 150),
        child: FaqListWidget(faqList: faqList),
      ),
    );
  }
}
