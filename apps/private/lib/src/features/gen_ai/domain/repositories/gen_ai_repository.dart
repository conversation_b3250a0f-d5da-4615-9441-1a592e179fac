import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chat_history.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chats_overview_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_conversation_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_message_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_preference_options_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_prompt_item_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_user_preferences.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';

enum DateRangeGroup {
  today('today'),
  yesterday('yesterday'),
  last7Days('last_7_days'),
  last30Days('older_than_7_days');

  const DateRangeGroup(this.key);

  final String key;
}

class GetChatParams {
  GetChatParams({
    this.isPinned,
    this.dateRangeGroup,
    this.searchQuery,
  });

  final bool? isPinned;
  final String? searchQuery;
  final DateRangeGroup? dateRangeGroup;

  Map<String, String> toJson() {
    final map = <String, String>{};
    if (isPinned != null) map['is_pinned'] = isPinned!.toString();
    if (searchQuery != null) map['search'] = searchQuery!;
    if (dateRangeGroup != null) map['date_range_group'] = dateRangeGroup!.key;
    return map;
  }
}

class SaveUserPreferenceParams {
  SaveUserPreferenceParams.single({
    required this.categoryId,
    required int preferenceId,
  })  : preferenceId = [preferenceId],
        isMultiSelect = false;

  SaveUserPreferenceParams.multi({
    required this.categoryId,
    required this.preferenceId,
  }) : isMultiSelect = true;

  final int categoryId;
  final List<int> preferenceId;
  final bool isMultiSelect;

  JSONObject toJson() => {
        'category_id': categoryId,
        'preference_id': isMultiSelect ? preferenceId : preferenceId.first,
      };
}

class GenAISettingsParams {
  const GenAISettingsParams({
    this.dataSource,
    this.responseLength,
    this.version,
    this.reasoning,
  });

  final String? dataSource;
  final String? responseLength;
  final String? version;
  final bool? reasoning;
}

abstract class GenAiRepository extends CacheableRepository {
  Future<RepoResponse<GenAIPreferenceOptionsResponse>> getPreferenceOptions(String type);

  Future<RepoResponse<GenAIUserPreferenceResponse>> getUserPreferences();

  Future<RepoResponse<bool>> saveUserPreference(SaveUserPreferenceParams preference);

  Future<RepoResponse<PromptLibraryResponseList>> getPromptLibrary();

  Future<RepoResponse<GenAIConversationResponse>> getConversation(String chatObjectId);

  Future<RepoResponse<GenAIChatsOverviewResponse>> getChatsOverview();

  Future<RepoResponse<GenAIChatHistoryResponse>> getChats(GetChatParams params);

  Future<RepoResponse<bool>> pinChat(String chatObjectId, bool isPinned);

  Future<RepoResponse<bool>> deleteChat(String chatObjectId);

  Stream<RepoResponse<GenAIMessageResponse>> getChatResponse(
    String message,
    String responseType, {
    String? chatObjectId,
    String? messageObjectId,
    GenAISettingsParams settings,
  });

  Future<RepoResponse<void>> submitGeneralFeedback(
    String content,
    int rating,
  );

  Future<RepoResponse<void>> submitAiMessageFeedback(
    String feedbackType,
    String chatId,
    String content,
    bool liked,
  );
}
