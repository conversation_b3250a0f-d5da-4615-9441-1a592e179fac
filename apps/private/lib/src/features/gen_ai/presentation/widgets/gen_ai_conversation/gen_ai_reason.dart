import 'package:control_style/control_style.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:scad_mobile/src/features/gen_ai/presentation/widgets/gen_ai_option_tile/elliptical_gradient.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/gen_ai_assets.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GenAiReason extends StatefulWidget {
  const GenAiReason({
    required this.reason,
    super.key,
  });

  final String reason;

  @override
  State<GenAiReason> createState() => _GenAiReasonState();
}

class _GenAiReasonState extends State<GenAiReason> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  double? _collapsedHeight = 0;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback(
      (_) => _setCollapsedHeight(),
    );
  }

  void _setCollapsedHeight() {
    const textStyle = TextStyle(fontSize: 16, fontWeight: FontWeight.w600);

    final bool enableCollapse = widget.reason.getTextNoOfLines(context, textStyle) > 3;
    setState(() {
      if (enableCollapse) {
        final double textHeight = widget.reason.getTextLineHeight(context, textStyle);
        _collapsedHeight = (textHeight * 3 - 4) * HiveUtilsSettings.textSizeFactor;
      } else {
        _collapsedHeight = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return _buildReasonContainer(
      child: Column(
        children: [
          _buildTitle(),
          const SizedBox(height: 8),
          _buildContent(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Row(
      children: [
        SvgPicture.asset(
          GenAIAssets.icReason,
          height: 20,
          colorFilter: const ColorFilter.mode(
            AppColors.blueShade40,
            BlendMode.srcIn,
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            LocaleKeys.genAI_reason.tr(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: _isLightMode ? AppColors.black : AppColors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    final isRtl = HiveUtilsSettings.isLanguageArabic;

    Widget mdContent() {
      return IgnorePointer(
        child: Markdown(
          padding: EdgeInsets.zero,
          data: widget.reason,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          styleSheet: MarkdownStyleSheet.fromTheme(
            ThemeData(
              dividerColor: Colors.grey.shade200,
              cardColor: Colors.transparent.withValues(alpha: 0.1),
              textTheme: Theme.of(context).textTheme.apply(
                    bodyColor: _isLightMode ? AppColors.black : AppColors.white,
                    fontFamily: GoogleFonts.outfit().fontFamily,
                  ),
            ),
          ).copyWith(
            h1: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            h2: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            h3: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            h4: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      );
    }

    return Directionality(
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: SelectableRegion(
        selectionControls: materialTextSelectionControls,
        child: ExpandableNotifier(
          child: ScrollOnExpand(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ExpandablePanel(
                  theme: const ExpandableThemeData(
                    headerAlignment: ExpandablePanelHeaderAlignment.center,
                    tapBodyToCollapse: true,
                    hasIcon: false,
                  ),
                  header: const SizedBox(),
                  collapsed: SizedBox(
                    height: _collapsedHeight,
                    child: mdContent(),
                  ),
                  expanded: mdContent(),
                ),
                if (_collapsedHeight != null) _buildShowMoreLessButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReasonContainer({required Widget child}) {
    return TextButton(
      onPressed: null,
      style: ButtonStyle(
        animationDuration: Duration.zero,
        textStyle: WidgetStateProperty.all(
          Theme.of(context).textTheme.displayMedium,
        ),
        splashFactory: NoSplash.splashFactory,
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        ),
        shape: WidgetStateProperty.resolveWith(
          (states) {
            const lightGradientBorder = GradientBorderSide(
              width: 0.5,
              gradient: LinearGradient(
                stops: [0, .59, 1],
                colors: [
                  Color(0x333FBFFC),
                  Color(0x331676E8),
                  Color(0x330154B8),
                ],
              ),
            );

            const darkGradientBorder = GradientBorderSide(
              gradient: EllipticalGradient(
                stops: [0, .47, .94],
                colors: [
                  Color(0x54FFFFFF),
                  Color(0x3369A0EE),
                  Color(0x66FFFFFF),
                ],
                backgroundColor: Colors.transparent,
                ellipseRelativeCenter: Offset.zero,
                ellipseScale: Scale(
                  heightFactor: 1.85,
                  widthFactor: 1.75,
                ),
              ),
            );

            final backgroundGradient = _isLightMode
                ? null
                : EllipticalGradient(
                    stops: const [0, .47, .94],
                    colors: const [
                      Color(0x14406FA8),
                      Color(0x14023C83),
                      Color(0x143892FF),
                    ],
                    backgroundColor: _isLightMode ? const Color(0xFFE9F3FF) : Colors.transparent,
                    ellipseRelativeCenter: Offset.zero,
                    ellipseScale: const Scale(
                      heightFactor: 1.85,
                      widthFactor: 1.75,
                    ),
                  );

            final innerShadow = _isLightMode
                ? const <BoxShadow>[]
                : [
                    const BoxShadow(
                      color: Color(0x292687FD),
                      offset: Offset(0, -5),
                      blurRadius: 10,
                    ),
                    const BoxShadow(
                      color: Color(0x1A055BE5),
                      offset: Offset(0, 4),
                      blurRadius: 14,
                    ),
                  ];

            return DecoratedOutlinedBorder(
              borderGradient: _isLightMode ? lightGradientBorder : darkGradientBorder,
              backgroundGradient: backgroundGradient,
              innerShadow: innerShadow,
              child: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
            );
          },
        ),
      ),
      child: child,
    );
  }

  Widget _buildShowMoreLessButton() {
    return Builder(
      builder: (context) {
        final controller = ExpandableController.of(
          context,
          required: true,
        )!;
        return Padding(
          padding: const EdgeInsets.only(top: 4),
          child: InkWell(
            onTap: controller.toggle,
            splashFactory: NoSplash.splashFactory,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  controller.expanded ? LocaleKeys.showLess.tr() : LocaleKeys.showMore.tr(),
                  style: AppTextStyles.s14w4cBlue.copyWith(color: !_isLightMode ? AppColors.blueLightOld : null),
                  overflow: TextOverflow.ellipsis,
                ),
                Icon(
                  controller.expanded ? Icons.keyboard_arrow_up_rounded : Icons.keyboard_arrow_down_rounded,
                  size: 18,
                  color: _isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
