import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chat_history.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chats_overview_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_conversation_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_message_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_preference_options_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_prompt_item_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_user_preferences.dart';
import 'package:scad_mobile/src/features/gen_ai/domain/repositories/gen_ai_repository.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'gen_ai_chat_event.dart';
part 'gen_ai_chat_state.dart';

const _kGenAIModelKey = 'gen-ai-model';

const kCategoryResponseLength = 'chat_preference_response_length';
const kCategoryDataSource = 'chat_preference_data_source';
const kCategoryAiModels = 'ai_model';

class GenAiChatBloc extends Bloc<GenAiChatEvent, GenAiChatState> {
  GenAiChatBloc() : super(const ChatConversationScreenState.newThread()) {
    on<NewThreadEvent>(_onNewThreadEvent);

    on<GetChatsOverviewEvent>(_onGetChatsOverviewEvent);
    on<GetChatsEvent>(_onGetChatsEvent);

    on<OnChatThreadSelectedEvent>(_onChatThreadSelected);

    on<GetPromptLibraryEvent>(_onGetPromptLibraryEvent);

    on<GenAiMessageSentEvent>(_onGenAIMessageSentEvent);
    on<GenAiMessageRegenerateEvent>(_onGenAIMessageRegenerateEvent);
    on<GenAiStateForwardEvent>(_stateForwardEvent);

    on<PinChatThreadEvent>(_onPinChatThreadEvent);
    on<DeleteChatThreadEvent>(_onDeleteChatThreadEvent);

    on<SendAiMessageFeedbackEvent>(_onSendAiMessageFeedbackEvent);
    on<SendGeneralFeedbackEvent>(_onSendGeneralFeedbackEvent);

    on<GetUserPreferenceEvent>(_onGetUserPreferenceEvent);
    on<SaveUserPreferenceEvent>(_onSaveUserPreferenceEvent);
    on<GetPreferenceOptionsEvent>(_onGetPreferenceOptionsEvent);
  }

  final genAiRepository = servicelocator<GenAiRepository>();
  final domainRepository = servicelocator<DomainsRepository>();

  StreamSubscription<RepoResponse<GenAIMessageResponse>>? _streamSubscription;

  String? dataSource;

  String? responseLength;

  String? get aiModel => HiveUtilsSettings.box.get(_kGenAIModelKey)?.toString();

  set aiModel(String? value) => HiveUtilsSettings.box.put(_kGenAIModelKey, value);

  bool isReasonEnabled = false;

  GenAISettingsParams get settings => GenAISettingsParams(
        dataSource: dataSource,
        responseLength: responseLength,
        version: aiModel,
        reasoning: isReasonEnabled,
      );

  String? _currentChatId;

  bool _isGeneratingMessage = false;

  bool get isGeneratingMessage => _isGeneratingMessage;

  void _onNewThreadEvent(NewThreadEvent event, Emitter<GenAiChatState> emit) {
    _currentChatId = null;
    _isGeneratingMessage = false;

    _streamSubscription?.cancel();
    emit(const ChatConversationScreenState.newThread());
    add(GetPromptLibraryEvent());
  }

  Future<void> _onGetChatsOverviewEvent(
    GetChatsOverviewEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    emit(GetChatsOverviewLoadingState());
    try {
      final response = await genAiRepository.getChatsOverview();
      if (!response.isSuccess) {
        emit(GetChatsOverviewErrorState(error: response.errorMessage));
        return;
      }

      emit(
        GetChatsOverviewSuccessState(
          overview: response.response ?? GenAIChatsOverviewResponse(),
        ),
      );
    } on Exception {
      emit(
        GetChatsOverviewErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetChatsEvent(
    GetChatsEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    emit(GetChatsLoadingState(event.nonce));
    try {
      final params = GetChatParams(
        isPinned: event.isPinned,
        dateRangeGroup: event.dateRangeGroup,
        searchQuery: event.searchQuery,
      );

      final response = await genAiRepository.getChats(params);
      if (!response.isSuccess) {
        emit(
          GetChatsErrorState(
            error: response.errorMessage,
            nonce: event.nonce,
          ),
        );
        return;
      }

      emit(
        GetChatsSuccessState(
          chats: response.response ?? GenAIChatHistoryResponse(),
          nonce: event.nonce,
        ),
      );
    } on Exception {
      emit(
        GetChatsErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
          nonce: event.nonce,
        ),
      );
    }
  }

  Future<void> _onGetPromptLibraryEvent(
    GetPromptLibraryEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    emit(GetPromptLibraryLoadingState());
    try {
      final response = await genAiRepository.getPromptLibrary();
      if (response.isSuccess) {
        return emit(
          GetPromptLibrarySuccessState(prompts: response.response ?? []),
        );
      }

      emit(
        GetPromptLibraryErrorState(error: response.errorMessage),
      );
    } on Exception catch (e) {
      emit(GetPromptLibraryErrorState(error: e.toString()));
    }
  }

  Future<void> _onChatThreadSelected(
    OnChatThreadSelectedEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    _currentChatId = event.chatObjectId;
    emit(const ChatConversationScreenState.ongoing());
    emit(ChatHistoryLoadingState());
    try {
      final response = await genAiRepository.getConversation(_currentChatId!);
      if (response.isSuccess) {
        emit(
          ChatHistorySuccessState(history: response.response!),
        );
      } else {
        emit(ChatHistoryErrorState(error: response.errorMessage));
      }
    } on Exception catch (e) {
      emit(ChatHistoryErrorState(error: e.toString()));
    }
  }

  void _stateForwardEvent(
    GenAiStateForwardEvent event,
    Emitter<GenAiChatState> emit,
  ) =>
      emit(event.state);

  void _dispatchState(GenAiChatState state) => add(GenAiStateForwardEvent(state));

  Future<void> _onGenAIMessageSentEvent(
    GenAiMessageSentEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    final key = UniqueKey();

    _isGeneratingMessage = true;
    _dispatchState(const ChatConversationScreenState.ongoing());
    await Future<void>.delayed(const Duration(milliseconds: 300));
    _dispatchState(GenAiMessageLoadingState(key));
    try {
      final stream = genAiRepository.getChatResponse(
        event.message,
        'general',
        chatObjectId: _currentChatId,
        settings: settings,
      );
      await _handleMessageStream(key, event.message, stream, emit);
    } on Exception catch (e) {
      await _streamSubscription?.cancel();
      emit(GenAiMessageErrorState(key, error: e.toString()));
    }
  }

  Future<void> _handleMessageStream(
    Key key,
    String message,
    Stream<RepoResponse<GenAIMessageResponse>> stream,
    Emitter<GenAiChatState> emit,
  ) async {
    bool isFirst = true;
    GenAIMessageResponse? recentMessage;

    _streamSubscription = stream.listen(
      (response) async {
        recentMessage = response.response;
        if (!response.isSuccess) {
          _dispatchState(
            GenAiMessageErrorState(key, error: response.errorMessage),
          );
          _isGeneratingMessage = false;
          await _streamSubscription?.cancel();
          return;
        }

        if (isFirst) {
          isFirst = false;
          _dispatchState(NewUserMessageState(message));
          await Future<void>.delayed(const Duration(milliseconds: 300));
          _dispatchState(
            GenAiNewMessageState(key, message: response.response!),
          );
          return;
        }

        _dispatchState(
          GenAiMessageUpdateState(key, message: response.response!),
        );
      },
    )..onDone(() {
        _isGeneratingMessage = false;
        _currentChatId = recentMessage!.chatId;
        _dispatchState(
          GenAiMessageCompleteState(key, message: recentMessage!),
        );
      });
  }

  Future<void> _onGenAIMessageRegenerateEvent(
    GenAiMessageRegenerateEvent event,
    Emitter<GenAiChatState> emit,
  ) async {
    emit(GenAiMessageLoadingState(event.key));
    _isGeneratingMessage = true;
    try {
      final stream = genAiRepository.getChatResponse(
        event.message,
        'general',
        chatObjectId: _currentChatId,
        settings: settings,
        messageObjectId: event.messageObjectId,
      );

      GenAIMessageResponse? recentMessage;
      _streamSubscription = stream.listen(
        (response) {
          if (!response.isSuccess) {
            _streamSubscription?.cancel();
            emit(GenAiMessageErrorState(event.key, error: response.errorMessage));
            return;
          }

          recentMessage = response.response;
          _dispatchState(
            GenAiMessageUpdateState(
              event.key,
              message: response.response!,
            ),
          );
        },
      )..onDone(() {
          _dispatchState(
            GenAiMessageCompleteState(event.key, message: recentMessage!),
          );
          _isGeneratingMessage = false;
        });
    } on Exception catch (e, st) {
      debugPrint('GenAiChatBloc._onGenAIMessageRegenerateTapped: $e\n$st');
      await _streamSubscription?.cancel();
      emit(
        GenAiMessageErrorState(event.key, error: LocaleKeys.somethingWentWrong),
      );
    }
  }

  Future<void> _onPinChatThreadEvent(PinChatThreadEvent event, Emitter<GenAiChatState> emit) async {
    try {
      final response = await genAiRepository.pinChat(event.chatObjectId, event.isPinned);
      if (!response.isSuccess) {
        debugPrint('GenAiChatBloc._onPinChatThreadEvent: Failed to pin chat');
        return;
      }
      if (event.reloadEvent != null) {
        add(event.reloadEvent!);
      }

      final isNotPinnedChatsReloadEvent = event.reloadEvent != const GetChatsEvent.pinned();
      if (isNotPinnedChatsReloadEvent) {
        add(const GetChatsEvent.pinned());
      }
    } on Exception catch (e, st) {
      AppLog.error(e, st);
    }
  }

  Future<void> _onDeleteChatThreadEvent(DeleteChatThreadEvent event, Emitter<GenAiChatState> emit) async {
    try {
      final response = await genAiRepository.deleteChat(event.chatObjectId);
      if (!response.isSuccess) {
        debugPrint('GenAiChatBloc._onDeleteChatThreadEvent: Failed to delete chat');
        return;
      }

      if (event.reloadEvent != null) {
        add(event.reloadEvent!);
      }

      if (event.isPinnedChat) {
        add(const GetChatsEvent.pinned());
      }
    } on Exception catch (e, st) {
      AppLog.error(e, st);
    }
  }

  Future<void> _onSendAiMessageFeedbackEvent(SendAiMessageFeedbackEvent event, Emitter<GenAiChatState> emit) async {
    emit(SendFeedbackLoadingState());
    final response = await genAiRepository.submitAiMessageFeedback(
      event.feedbackType,
      event.chatId,
      event.content,
      event.liked,
    );

    if (response.isSuccess) {
      emit(SendFeedbackSuccessState());
    } else {
      emit(
        SendFeedbackErrorState(error: response.errorMessage),
      );
    }
  }

  Future<void> _onSendGeneralFeedbackEvent(SendGeneralFeedbackEvent event, Emitter<GenAiChatState> emit) async {
    emit(SendFeedbackLoadingState());
    final response = await genAiRepository.submitGeneralFeedback(
      event.content,
      event.rating,
    );

    if (response.isSuccess) {
      emit(SendFeedbackSuccessState());
    } else {
      emit(
        SendFeedbackErrorState(error: response.errorMessage),
      );
    }
  }

  Future<void> _onGetUserPreferenceEvent(GetUserPreferenceEvent event, Emitter<GenAiChatState> emit) async {
    if (event.notify) {
      emit(GetUserPreferenceLoadingState());
    }

    try {
      final response = await genAiRepository.getUserPreferences();
      if (!response.isSuccess) {
        if (event.notify) {
          emit(GetUserPreferenceErrorState(error: response.errorMessage));
        }
        return;
      }

      final preference = response.response!;
      dataSource = preference.dataSource?.preferences?.firstOrNull?.value;

      final responseLengthPreference = preference.responseLength?.preferences?.firstOrNull?.value;
      // null check added, cause when onboarding when data source is saved, response length is null (not set)
      // so when saving data source, this value is overridden with null if this null check is removed.
      if (responseLengthPreference != null) {
        responseLength = responseLengthPreference;
      }

      if (event.notify) {
        emit(GetUserPreferenceSuccessState(preference: preference));
      }
    } on Exception {
      if (event.notify) {
        emit(
          GetUserPreferenceErrorState(error: LocaleKeys.somethingWentWrong.tr()),
        );
      }
    }
  }

  Future<void> _onSaveUserPreferenceEvent(SaveUserPreferenceEvent event, Emitter<GenAiChatState> emit) async {
    try {
      final preference = event.isMultiSelect
          ? SaveUserPreferenceParams.multi(
              categoryId: event.categoryId,
              preferenceId: event.preferenceId,
            )
          : SaveUserPreferenceParams.single(
              categoryId: event.categoryId,
              preferenceId: event.preferenceId.first,
            );

      final response = await genAiRepository.saveUserPreference(preference);
      if (!response.isSuccess) {
        debugPrint('GenAiChatBloc._onSaveUserPreferenceEvent: ERROR (${response.errorMessage})');
        return;
      }

      emit(
        SaveUserPreferenceSuccessState(event.categoryId),
      );

      add(
        const GetUserPreferenceEvent(notify: false),
      );
    } on Exception catch (e, st) {
      AppLog.error(e, st);
    }
  }

  Future<void> _onGetPreferenceOptionsEvent(GetPreferenceOptionsEvent event, Emitter<GenAiChatState> emit) async {
    emit(GetPreferenceOptionsLoadingState(event.category));

    try {
      final response = await genAiRepository.getPreferenceOptions(event.category);
      if (!response.isSuccess) {
        emit(
          GetPreferenceOptionsErrorState(
            event.category,
            error: response.errorMessage,
          ),
        );
        return;
      }

      emit(
        GetPreferenceOptionsSuccessState(
          event.category,
          option: response.response,
        ),
      );
    } on Exception {
      emit(
        GetPreferenceOptionsErrorState(
          event.category,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _streamSubscription?.cancel();
    return super.close();
  }
}
