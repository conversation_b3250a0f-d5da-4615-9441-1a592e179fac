import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/app_shimmer.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/primary_button.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_preference_options_response.dart';
import 'package:scad_mobile/src/features/gen_ai/presentation/bloc/gen_ai_chat_bloc.dart';
import 'package:scad_mobile/src/features/gen_ai/presentation/bloc/mixin/save_gen_ai_user_preference_mixin.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GenAISettingsBottomSheet extends StatefulWidget {
  const GenAISettingsBottomSheet._();

  static Future<Object?> show(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: HiveUtilsSettings.isLightMode ? AppColors.white : AppColors.scaffoldBackground,
      builder: (context) {
        return const GenAISettingsBottomSheet._();
      },
    );
  }

  @override
  State<GenAISettingsBottomSheet> createState() => _GenAISettingsBottomSheetState();
}

class _GenAISettingsBottomSheetState extends State<GenAISettingsBottomSheet> with SaveGenAIUserPreferenceMixin {
  GenAIPreferenceOptionsResponse? _dataSourceOptions;
  GenAIPreferenceOptionsResponse? _responseLengthOptions;

  String? _dataSource;
  String? _responseLength;
  String? _aiModel;

  @override
  void initState() {
    super.initState();

    final bloc = context.read<GenAiChatBloc>();
    _dataSource = bloc.dataSource;
    _responseLength = bloc.responseLength;
    _aiModel = bloc.aiModel;
  }

  void _onApplyTapped() {
    final bloc = context.read<GenAiChatBloc>();
    final currentDataSource = bloc.dataSource;
    if (_dataSource != null && currentDataSource != _dataSource) {
      savePreference(context, _dataSourceOptions, [_dataSource!]);
    }

    final currentResponseLength = bloc.responseLength;
    if (_responseLength != null && currentResponseLength != _responseLength) {
      savePreference(context, _responseLengthOptions, [_responseLength!]);
    }

    bloc.aiModel = _aiModel;
    context.maybePop();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<GenAiChatBloc, GenAiChatState>(
      listenWhen: (_, state) => state is ApplyGenAiSettingsSuccessState,
      listener: (context, state) {
        if (state is ApplyGenAiSettingsSuccessState) {
          context.maybePop();
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: BottomSheetTopNotch(),
            ),
            Text(
              LocaleKeys.genAI_tools.tr(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildVersionDropdown(context),
            const SizedBox(height: 16),
            _buildDataSourceToggle(context),
            const SizedBox(height: 24),
            _buildResponseLengthToggle(context),
            const SizedBox(height: 24),
            PrimaryButton(
              text: LocaleKeys.apply.tr(),
              borderRadius: 10,
              onTap: _onApplyTapped,
              color: AppColors.white,
              backgroundColor: AppColors.blueShade40,
            ),
            const SizedBox(height: 16),
            PrimaryButton(
              text: LocaleKeys.cancel.tr(),
              borderRadius: 10,
              onTap: () => context.maybePop(),
              color: AppColors.blueShade40,
              side: const BorderSide(
                color: AppColors.blueShade40,
                width: 1.5,
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildVersionDropdown(BuildContext context) {
    const event = GetPreferenceOptionsEvent.aiModels();
    context.read<GenAiChatBloc>().add(event);

    final isLightMode = HiveUtilsSettings.isLightMode;

    return AnimatedSize(
      duration: const Duration(milliseconds: 300),
      curve: Curves.fastLinearToSlowEaseIn,
      child: BlocBuilder<GenAiChatBloc, GenAiChatState>(
        buildWhen: (previous, current) =>
            current is GetPreferenceOptionsSuccessState && current.category == event.category,
        builder: (context, state) {
          if (state is! GetPreferenceOptionsSuccessState || state.category != event.category) {
            return const SizedBox.shrink();
          }

          final options = state.option?.options ?? <PreferenceOption>[];

          final selectedOption = options.firstWhere(
            (option) => option.value == _aiModel,
            orElse: () => options.first,
          );

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.genAI_version.tr(),
                style: TextStyle(
                  color: HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              DropdownMenu(
                trailingIcon: const SizedBox(),
                enabled: options.length > 1,
                initialSelection: selectedOption.value,
                hintText: LocaleKeys.select.tr(),
                expandedInsets: EdgeInsets.zero,
                inputDecorationTheme: InputDecorationTheme(
                  labelStyle: const TextStyle(fontSize: 14),
                  prefixStyle: const TextStyle(fontSize: 10),
                  floatingLabelStyle: const TextStyle(fontSize: 10),
                  constraints: const BoxConstraints(maxHeight: 36),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide(
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide(
                      color: isLightMode ? AppColors.greyShade1 : const Color(0xFF2F4B6C),
                    ),
                  ),
                  isDense: true,
                  isCollapsed: true,
                ),
                textStyle: const TextStyle(fontSize: 14, height: 1),
                alignmentOffset: const Offset(0, 4),
                menuStyle: MenuStyle(
                  padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                  backgroundColor: WidgetStateProperty.all(isLightMode ? AppColors.white : AppColors.blueShade32),
                  maximumSize: const WidgetStatePropertyAll(Size.infinite),
                ),
                onSelected: (value) => _aiModel = value,
                dropdownMenuEntries: options
                    .map(
                      (option) => DropdownMenuEntry(
                        value: option.value,
                        label: option.displayName ?? '',
                      ),
                    )
                    .toList(),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDataSourceToggle(BuildContext context) {
    const event = GetPreferenceOptionsEvent.dataSource();
    context.read<GenAiChatBloc>().add(event);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.genAI_dataSource.tr(),
          style: TextStyle(
            color: HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        BlocBuilder<GenAiChatBloc, GenAiChatState>(
          buildWhen: (previous, current) =>
              current is GetPreferenceOptionsSuccessState && current.category == event.category,
          builder: (context, state) {
            final isOtherCategory = state is GetPreferenceOptionsBaseState && state.category != event.category;

            if (state is GetPreferenceOptionsLoadingState || isOtherCategory) {
              return const AppShimmer(
                width: double.infinity,
                height: 36,
                radius: 24,
              );
            }

            if (state is! GetPreferenceOptionsSuccessState) {
              return const SizedBox.shrink();
            }

            _dataSourceOptions = state.option;
            final preferences = _dataSourceOptions?.options ?? <PreferenceOption>[];
            final initialIndex = preferences.indexWhere((option) => option.value == _dataSource);

            return _ToggleDelegate<PreferenceOption>(
              initialIndex: initialIndex,
              options: preferences,
              labelBuilder: (context, option) => option.displayName ?? '',
              onToggle: (index) => _dataSource = preferences.elementAt(index).value,
            );
          },
        ),
      ],
    );
  }

  Widget _buildResponseLengthToggle(BuildContext context) {
    const event = GetPreferenceOptionsEvent.responseLength();
    context.read<GenAiChatBloc>().add(event);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.genAI_responseLength.tr(),
          style: TextStyle(
            color: HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        BlocBuilder<GenAiChatBloc, GenAiChatState>(
          buildWhen: (previous, current) =>
              current is GetPreferenceOptionsSuccessState && current.category == event.category,
          builder: (context, state) {
            if (state is GetPreferenceOptionsLoadingState) {
              return const AppShimmer(
                width: double.infinity,
                height: 36,
                radius: 24,
              );
            }

            if (state is! GetPreferenceOptionsSuccessState || state.category != event.category) {
              return const SizedBox.shrink();
            }

            _responseLengthOptions = state.option;
            final preferences = _responseLengthOptions?.options ?? <PreferenceOption>[];

            final initialIndex = preferences.indexWhere((option) => option.value == _responseLength);
            return _ToggleDelegate<PreferenceOption>(
              initialIndex: initialIndex,
              options: preferences,
              labelBuilder: (context, option) => option.displayName ?? '',
              onToggle: (index) => _responseLength = preferences.elementAt(index).value,
            );
          },
        ),
      ],
    );
  }
}

class _ToggleDelegate<T> extends StatefulWidget {
  const _ToggleDelegate({
    required this.options,
    required this.onToggle,
    required this.labelBuilder,
    this.initialIndex = 0,
  });

  final int initialIndex;
  final List<T> options;
  final String Function(BuildContext, T) labelBuilder;
  final ValueChanged<int> onToggle;

  @override
  State<_ToggleDelegate<T>> createState() => _ToggleDelegateState<T>();
}

class _ToggleDelegateState<T> extends State<_ToggleDelegate<T>> {
  late int _selectedIndex;

  final isRtl = HiveUtilsSettings.isLanguageArabic;

  @override
  void initState() {
    super.initState();

    _selectedIndex = widget.initialIndex;
    if (_selectedIndex == -1) _selectedIndex = 0;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
        side: BorderSide(
          color: HiveUtilsSettings.isLightMode ? AppColors.greyShade1 : const Color(0xFF2F4B6C),
        ),
      ),
      child: Stack(
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              final width = constraints.maxWidth / widget.options.length;

              return Row(
                children: [
                  ...List<Widget>.generate(
                    widget.options.length - 1,
                    (index) {
                      final isSpaced = _selectedIndex > index;

                      return Flexible(
                        child: AnimatedSize(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.fastLinearToSlowEaseIn,
                          child: SizedBox(width: isSpaced ? width : 0),
                        ),
                      );
                    },
                  ),
                  SizedBox(
                    width: width,
                    child: Material(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                      color: AppColors.blueShade40,
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 6),
                        child: Text(''),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          Positioned.fill(
            child: Row(
              children: widget.options.indexed
                  .map(
                    (entry) => Expanded(
                      child: _buildText(entry.$1, entry.$2, true),
                    ),
                  )
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildText(int index, T option, bool isOption1) {
    final isSelected = index == _selectedIndex;
    final text = widget.labelBuilder(context, option);

    return GestureDetector(
      onTap: () => _onTap(index),
      child: Container(
        color: Colors.white.withValues(alpha: 0.01),
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Tooltip(
          message: text,
          child: Text(
            text,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 13,
              color: isSelected
                  ? Colors.white
                  : HiveUtilsSettings.isLightMode
                      ? AppColors.black
                      : AppColors.greyShade8,
            ),
          ),
        ),
      ),
    );
  }

  void _onTap(int index) {
    setState(() => _selectedIndex = index);
    widget.onToggle.call(index);
  }
}
