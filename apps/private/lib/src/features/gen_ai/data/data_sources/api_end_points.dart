import 'package:scad_mobile/src/config/app_config/api_config.dart';

class GenAIEndPoints {
  GenAIEndPoints._();

  static String chats = '${ApiConfig.genAIBaseUrl}/api/v2/chats';

  static String chatObject(String id) => '${ApiConfig.genAIBaseUrl}/api/v2/chats/$id';

  static String chatPreferenceOptions(String type) =>
      '${ApiConfig.genAIBaseUrl}/api/v2/dropdowns/$type?with_icons=true';

  static String userPreference = '${ApiConfig.genAIBaseUrl}/api/v2/chats/user-preference';

  static String promptLibrary = '${ApiConfig.genAIBaseUrl}/api/v2/prompt';

  static String generalFeedback = '${ApiConfig.genAIBaseUrl}/api/v2/feedback/general';
  static String aiMessageFeedback = '${ApiConfig.genAIBaseUrl}/api/v2/feedback/ai-message';

  static String chatHistoryOverview = '${ApiConfig.genAIBaseUrl}/api/v2/chats/overview';

  static String pinChatThread(String chatObjectId) => '${ApiConfig.genAIBaseUrl}/api/v2/chats/$chatObjectId/pin';
}
