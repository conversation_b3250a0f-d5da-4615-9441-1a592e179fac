import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/gen_ai/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chat_history.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_chats_overview_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_conversation_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_message_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_preference_options_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_prompt_item_response.dart';
import 'package:scad_mobile/src/features/gen_ai/data/models/gen_ai_user_preferences.dart';
import 'package:scad_mobile/src/features/gen_ai/domain/repositories/gen_ai_repository.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_stream_service.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GenAiRepositoryImpl extends GenAiRepository {
  final _httpService = HttpServiceRequests();
  final _httpStreamService = HttpStreamService();

  @override
  Future<RepoResponse<GenAIPreferenceOptionsResponse>> getPreferenceOptions(String type) async {
    final endpoint = GenAIEndPoints.chatPreferenceOptions(type);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) => GenAIPreferenceOptionsResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<GenAIUserPreferenceResponse>> getUserPreferences() async {
    final endpoint = GenAIEndPoints.userPreference;
    try {
      final response = await _httpService.get(endpoint);
      if (!response.isSuccess) {
        return RepoResponse.error(
          errorMessage: response.message,
        );
      }

      return RepoResponse.success(
        response: GenAIUserPreferenceResponse.fromJson(response.response),
      );
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      return RepoResponse.error(errorMessage: LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<bool>> saveUserPreference(SaveUserPreferenceParams preference) async {
    final endpoint = GenAIEndPoints.userPreference;
    try {
      final response = await _httpService.postJson(
        endpoint,
        jsonPayloadMap: preference.toJson(),
      );
      return RepoResponse.success(response: response.isSuccess);
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      return RepoResponse.error(errorMessage: LocaleKeys.somethingWentWrong.tr());
    }
  }

  @override
  Future<RepoResponse<PromptLibraryResponseList>> getPromptLibrary() async {
    final endpoint = GenAIEndPoints.promptLibrary;
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => GenAIPromptItemResponse.fromJson(e as JSONObject),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<GenAIConversationResponse>> getConversation(String chatObjectId) async {
    try {
      final url = GenAIEndPoints.chatObject(chatObjectId);
      final response = await _httpService.get(url);
      if (!response.isSuccess) {
        return RepoResponse.error(
          errorMessage: response.message,
        );
      }

      return RepoResponse.success(
        response: GenAIConversationResponse.fromJson(response.response),
      );
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      return RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<GenAIChatsOverviewResponse>> getChatsOverview() async {
    final endpoint = GenAIEndPoints.chatHistoryOverview;
    try {
      final response = await _httpService.get(endpoint);
      if (!response.isSuccess) {
        return RepoResponse.error(
          errorMessage: response.message,
        );
      }

      return RepoResponse.success(
        response: GenAIChatsOverviewResponse.fromJson(response.response),
      );
    } on Exception {
      return RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<GenAIChatHistoryResponse>> getChats(GetChatParams params) async {
    try {
      final queryParams = params.toJson();
      final uri = Uri.parse(GenAIEndPoints.chats).replace(queryParameters: queryParams);
      final endpoint = uri.toString();

      final response = await _httpService.get(endpoint);
      if (!response.isSuccess) {
        return RepoResponse.error(
          errorMessage: response.message,
        );
      }

      return RepoResponse.success(
        response: GenAIChatHistoryResponse.fromJson(response.response),
      );
    } on Exception {
      return RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<bool>> pinChat(String chatObjectId, bool isPinned) async {
    try {
      final endpoint = GenAIEndPoints.pinChatThread(chatObjectId);
      final body = {'is_pinned': isPinned};
      final response = await _httpService.patch(endpoint, data: body);
      if (!response.isSuccess) {
        return RepoResponse.error(errorMessage: response.message);
      }
      return RepoResponse.success(response: response.isSuccess);
    } on Exception {
      return RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<bool>> deleteChat(String chatObjectId) async {
    try {
      final endpoint = GenAIEndPoints.chatObject(chatObjectId);
      final response = await _httpService.delete(endpoint);
      if (!response.isSuccess) {
        return RepoResponse.error(errorMessage: response.message);
      }
      return RepoResponse.success(response: response.isSuccess);
    } on Exception {
      return RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Stream<RepoResponse<GenAIMessageResponse>> getChatResponse(
    String message,
    String responseType, {
    String? chatObjectId,
    String? messageObjectId,
    GenAISettingsParams? settings,
  }) async* {
    final url = '${GenAIEndPoints.chats}?stream=True&version=${settings?.version ?? 'latest'}';

    final bodyMap = <String, dynamic>{
      'action': messageObjectId == null ? 'next' : 'regenerate',
      'message': {
        'id': messageObjectId,
        'content': message,
        'type': responseType,
        'responseType': settings?.responseLength ?? 'short',
        'source': settings?.dataSource ?? 'scad',
      },
      'reasoning': settings?.reasoning ?? false,
      'conversation_id': chatObjectId,
    };

    try {
      final stream = _httpStreamService.post(url, body: bodyMap);
      await for (final item in stream) {
        if (!item.isSuccess) {
          yield RepoResponse.error(errorMessage: item.message);
          continue;
        }

        final message = GenAIMessageResponse.fromJson(item.response);
        yield RepoResponse.success(response: message);
      }
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      yield RepoResponse.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<void>> submitGeneralFeedback(String content, int rating) async {
    final endpoint = GenAIEndPoints.generalFeedback;
    final body = {
      'content': content,
      'rating': rating,
    };

    try {
      final response = await _httpService.postJson(endpoint, jsonPayloadMap: body);
      if (!response.isSuccess) {
        return RepoResponse<void>.error(
          errorMessage: response.message,
        );
      }
      return RepoResponse<void>.success(response: null);
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      return RepoResponse<void>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<void>> submitAiMessageFeedback(
    String feedbackType,
    String chatId,
    String content,
    bool liked,
  ) async {
    final endpoint = GenAIEndPoints.aiMessageFeedback;
    final body = {
      'feedback_type': feedbackType, // See Feedback Type API
      'liked': liked,
      'content': content, // Optional
      'chat_id': chatId,
    };

    try {
      final response = await _httpService.postJson(endpoint, jsonPayloadMap: body);
      if (!response.isSuccess) {
        return RepoResponse<void>.error(
          errorMessage: response.message,
        );
      }
      return RepoResponse<void>.success(response: null);
    } catch (e, st) {
      Completer<dynamic>().completeError(e, st);
      return RepoResponse<void>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
