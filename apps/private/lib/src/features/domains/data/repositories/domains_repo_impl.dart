import 'dart:async';

import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import 'package:scad_mobile/src/features/domains/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/government_affair_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';

class DomainsRepositoryImpl extends DomainsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<List<DomainModel>>> getDomainIconsList() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyDomainList);
    return fetchWithCache(
      cacheKey: cacheKey,
      demoApiResponse: () => {'data': demoDomainModelResponse},
      whenNoCache: () => _httpService.get(
        DomainsEndPoints.getDomainList,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => DomainModel.fromJson(e as JSONObject),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainListIfp() async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyDomainListIfp);
    return fetchWithCache<DomainThemeSubThemeModelResponse>(
      cacheKey: cacheKey,
      demoApiResponse: () => {'data': demoIfpNavigationSuccess},
      whenNoCache: () => _httpService.get(
        DomainsEndPoints.getDomainListIfp,
        header: {'Accept-Version': '2.0'},
      ),
      parseResult: (json) => DomainThemeSubThemeModelResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<List<DomainClassificationModel>>> getDomainClassificationList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getDomainClassificationList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache<List<DomainClassificationModel>>(
      cacheKey: cacheKey,
      demoApiResponse: () => demoDomainClassificationsResponse(requestParams['domain_id'].toString()),
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['classification'] as List<dynamic>;
        return data
            .map(
              (e) => DomainClassificationModel.fromJson(e as JSONObject),
            )
            .toList()
          ..removeWhere(
            (element) => ![
              'official_statistics',
              'experimental_statistics',
            ].contains(element.key),
          );
      },
    );
  }

  @override
  Future<RepoResponse<List<ThemeSubThemeResponse>>> getThemeList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getThemeList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache(
      cacheKey: cacheKey,
      demoApiResponse: () => {
        'data': domainThemes(
          requestParams['domain_id']!,
          requestParams['classification_id']!,
        ),
      },
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => ThemeSubThemeResponse.fromJson(e as JSONObject),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<ThemeIndicatorListResponse>> getThemeIndicatorList(
    RequestParamsMap requestParams,
  ) async {
    final endpoint = DomainsEndPoints.getThemeIndicatorList.setUrlParams(requestParams);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache<ThemeIndicatorListResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) => ThemeIndicatorListResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<ExperimentalIndicatorListResponse>> getThemeExperimentalIndicatorList({
    required String classification,
    required int pageNo,
    required Map<String, dynamic> filters,
  }) async {
    final String endpoint = DomainsEndPoints.getThemeExperimentalIndicatorList.setUrlParams({
      'classification': classification,
      'page': '$pageNo',
    });

    final cacheKey = getCacheKey(endpoint, payload: filters);
    return fetchWithCache<ExperimentalIndicatorListResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        jsonPayloadMap: filters,
        server: ApiServer.ifp,
      ),
      parseResult: (json) => ExperimentalIndicatorListResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<List<ExperimentalFiltersResponseItem>>> getExperimentalFilters({
    required String classification,
    required String screenerIndicator,
  }) async {
    final endpoint = DomainsEndPoints.getScreenerFilters(classification, screenerIndicator);
    final cacheKey = getCacheKey(endpoint);
    return fetchWithCache<List<ExperimentalFiltersResponseItem>>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => ExperimentalFiltersResponseItem.fromJson(
                e as JSONObject,
              ),
            )
            .toList();
      },
    );
  }

  @override
  Future<RepoResponse<GovernmentAffairItemList>> getGovernmentAffairs() async {
    final String endpoint = DomainsEndPoints.getGovernmentAffairs;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache<GovernmentAffairItemList>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      ),
      parseResult: (json) {
        final data = json['data'] as List<dynamic>;
        return data
            .map(
              (e) => GovernmentAffairResponseItem.fromJson(
                e as JSONObject,
              ),
            )
            .toList();
      },
    );
  }
}
