part of 'domains_bloc.dart';

abstract class DomainsState extends Equatable {
  const DomainsState();

  @override
  List<Object> get props => [];
}

// Domain list page states ->
class DomainLoadingState extends DomainsState {}

class DomainShowResponseState extends DomainsState {
  const DomainShowResponseState({required this.list});

  final List<DomainModel> list;

  @override
  List<Object> get props => [list];
}

class DomainErrorState extends DomainsState {
  const DomainErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

// Theme list page states ->
class ThemeLoadingState extends DomainsState {
  const ThemeLoadingState();
}

class ThemeIndicatorsLoadingState extends DomainsState {}

class ThemeClassificationShowResponseState extends DomainsState {
  const ThemeClassificationShowResponseState(
      {required this.list, required this.domains, required this.domainId});

  final String domainId;
  final List<DomainClassificationModel> list;
  final List<DomainModel> domains;

  @override
  List<Object> get props => [list, domains, domainId];
}

class ThemeListShowResponseState extends DomainsState {
  const ThemeListShowResponseState(
      {required this.list, required this.domainId});

  final String domainId;
  final List<ThemeSubThemeResponse> list;

  @override
  List<Object> get props => [domainId, list];
}

class GetThemesSuccessState extends DomainsState {
  const GetThemesSuccessState(
      {required this.officialThemes, required this.experimentalThemes, required this.domainId});

  final String domainId;
  final List<ThemeSubThemeResponse> officialThemes;
  final List<ThemeSubThemeResponse> experimentalThemes;

  @override
  List<Object> get props => [domainId, officialThemes, experimentalThemes];
}

class ThemeErrorState extends DomainsState {
  const ThemeErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class ThemeIndicatorsListState extends DomainsState {
  const ThemeIndicatorsListState({
    required this.themeIndicatorListResponse,
  });

  final ThemeIndicatorListResponse themeIndicatorListResponse;

  @override
  List<Object> get props => [themeIndicatorListResponse];
}

class CompareThemeIndicatorListState extends DomainsState {
  const CompareThemeIndicatorListState({
    required this.themeIndicatorListResponse,
    required this.subThemeId,
  });

  final ThemeIndicatorListResponse themeIndicatorListResponse;
  final String subThemeId;

  @override
  List<Object> get props => [themeIndicatorListResponse, subThemeId];
}

class ExperimentalFilterListState extends DomainsState {
  const ExperimentalFilterListState({required this.experimentalFilters});

  final List<ExperimentalFiltersResponseItem> experimentalFilters;
  @override
  List<Object> get props => [experimentalFilters];
}

class ThemeExperimentalIndicatorListResponseState extends DomainsState {
  const ThemeExperimentalIndicatorListResponseState({required this.response});

  final ExperimentalIndicatorListResponse response;
  @override
  List<Object> get props => [response];
}

class ThemeIndicatorState extends DomainsState {
  const ThemeIndicatorState({
    required this.value,
    required this.unit,
    required this.numberUnit,
    required this.series,
    required this.domain,
    required this.title,
    required this.chartType,
  });

  final String chartType;
  final String domain;
  final String title;
  final List<Map<String, dynamic>> series;
  final String value;
  final String unit;
  final String numberUnit;

  @override
  List<Object> get props =>
      [value, unit, numberUnit, series, domain, title, chartType];
}

class ThemeIndicatorShowResponseState extends DomainsState {
  const ThemeIndicatorShowResponseState();

  @override
  List<Object> get props => [];
}

class DomainSearchState extends DomainsState {
  const DomainSearchState({required this.searchResult});

  final List<DomainModel> searchResult;

  @override
  List<Object> get props => [searchResult];
}

class GetThemeClassificationState extends DomainsState {
  const GetThemeClassificationState({
    required this.list,
    required this.domainId,
  });

  final String domainId;
  final List<DomainClassificationModel> list;

  @override
  List<Object> get props => [list, domainId];
}

class GetGovernmentAffairsLoadingState extends DomainsState {}

class GetGovernmentAffairsSuccessState extends DomainsState {
  const GetGovernmentAffairsSuccessState(this.items);

  final GovernmentAffairItemList items;

  @override
  List<Object> get props => items;
}

class GetGovernmentAffairsErrorState extends DomainsState {
  const GetGovernmentAffairsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}