part of 'spatial_analytics_v2_bloc.dart';

abstract class SpatialAnalyticsV2State extends Equatable {
  const SpatialAnalyticsV2State();

  @override
  List<Object> get props => [];
}

class SpatialAnalyticsV2InitState extends SpatialAnalyticsV2State {}

class FullScreenLoadingState extends SpatialAnalyticsV2State {}

class MapState extends SpatialAnalyticsV2State {
  const MapState();
}

class GetArcGISTokenSuccessState extends MapState {
  const GetArcGISTokenSuccessState({
    required this.token,
    required this.license,
  });

  final String token;
  final String license;

  @override
  List<Object> get props => [token, license];
}

class GetArcGISTokenErrorState extends MapState {
  const GetArcGISTokenErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class GetWebMapSuccessState extends MapState {
  const GetWebMapSuccessState({
    required this.map,
  });

  final ArcGISMap map;

  @override
  List<Object> get props => [map];
}

class GetWebMapErrorState extends MapState {
  const GetWebMapErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

abstract class BaseGetPOIListState extends SpatialAnalyticsV2State {
  const BaseGetPOIListState();
}

class GetPOIListLoadingState extends BaseGetPOIListState {}

class GetPOIListSuccessState extends BaseGetPOIListState {
  const GetPOIListSuccessState({
    required this.icons,
    required this.list,
  });

  final POIItemList list;
  final POIIconList icons;

  @override
  List<Object> get props => [list, icons];
}

class GetPOIListErrorState extends BaseGetPOIListState {
  const GetPOIListErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class SetMapLayerVisibilityOnFilterState extends MapState {
  const SetMapLayerVisibilityOnFilterState({
    required this.filter,
    required this.didDomainChange,
  });

  final GSFilterParams filter;
  final bool didDomainChange;

  @override
  List<Object> get props => [filter, didDomainChange];
}

abstract class BaseGetAllGSFilterState extends SpatialAnalyticsV2State {
  const BaseGetAllGSFilterState();
}

class GetAllGSFilterLoadingState extends BaseGetAllGSFilterState {}

class GetAllGSFilterSuccessState extends BaseGetAllGSFilterState {
  const GetAllGSFilterSuccessState({
    required this.filter,
  });

  final GSFilterParams filter;

  @override
  List<Object> get props => [filter];
}

class GetAllGSFilterErrorState extends BaseGetAllGSFilterState {
  const GetAllGSFilterErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

abstract class BaseGetArcGISSummaryState extends SpatialAnalyticsV2State {
  const BaseGetArcGISSummaryState();
}

class GetArcGISSummaryLoadingState extends BaseGetArcGISSummaryState {
  const GetArcGISSummaryLoadingState();
}

class GetArcGISSummarySuccessState extends BaseGetArcGISSummaryState {
  const GetArcGISSummarySuccessState({
    required this.summary,
  });

  final GSSummaryList summary;

  @override
  List<Object> get props => [summary];
}

class GetArcGISSummaryErrorState extends BaseGetArcGISSummaryState {
  const GetArcGISSummaryErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

abstract class BaseGetDomainSummaryState extends SpatialAnalyticsV2State {
  const BaseGetDomainSummaryState();
}

class GetDomainSummaryLoadingState extends BaseGetDomainSummaryState {
  const GetDomainSummaryLoadingState();
}

class GetDomainSummarySuccessState extends BaseGetDomainSummaryState {
  const GetDomainSummarySuccessState({
    required this.summary,
    required this.indicatorConfigs,
    this.interactionFromIndicatorId,
  });

  final GSDomainSummary summary;
  final List<GSIndicatorConfiguration> indicatorConfigs;
  final int? interactionFromIndicatorId;

  @override
  List<Object> get props => [summary];
}

class GetDomainSummaryErrorState extends BaseGetDomainSummaryState {
  const GetDomainSummaryErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class ApplyGSFilterAcceptedState extends SpatialAnalyticsV2State {
  const ApplyGSFilterAcceptedState();
}

class GSChartTypeChangedState extends SpatialAnalyticsV2State {
  const GSChartTypeChangedState({
    required this.indicatorId,
    required this.chartType,
  });

  final int indicatorId;
  final GSChartType chartType;

  @override
  List<Object> get props => [indicatorId, chartType];
}

abstract class BaseGSCardSubscriptionState extends SpatialAnalyticsV2State {
  const BaseGSCardSubscriptionState({required this.id});

  final int id;

  @override
  List<Object> get props => [id];
}

class GSCardSubscriptionLoadingState extends BaseGSCardSubscriptionState {
  const GSCardSubscriptionLoadingState({required super.id});
}

class GSCardSubscriptionSuccessState extends BaseGSCardSubscriptionState {
  const GSCardSubscriptionSuccessState({
    required super.id,
    required this.subscription,
  });

  final NodeIdUuid? subscription;

  @override
  List<Object> get props => [...super.props, subscription ?? ''];
}

class GSCardSubscriptionErrorState extends BaseGSCardSubscriptionState {
  const GSCardSubscriptionErrorState({
    required super.id,
    required this.error,
  });

  final String error;

  @override
  List<Object> get props => [...super.props, error];
}

class GSFilterChangedState extends SpatialAnalyticsV2State {
  const GSFilterChangedState({
    required this.filter,
    this.nonce = 0,
  });

  final GSFilterParams filter;
  final int nonce;

  @override
  List<Object> get props => [filter.hashCode, nonce];
}
