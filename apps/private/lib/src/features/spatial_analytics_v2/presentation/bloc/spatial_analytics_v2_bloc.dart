import 'dart:async';
import 'dart:convert';

import 'package:arcgis_maps/arcgis_maps.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/config/app_config/arcgis_config.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/remove_subscription_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/subscription_response.dart';
import 'package:scad_mobile/src/features/notification/domain/repositories/notification_repository_imports.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_domain_access.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter_params.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_indicator.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_indicator_configuration.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_summary.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/poi_item.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/domain/repositories/spatial_analytics_v2_repository.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/sheets/gs_settings_bottom_sheet/chart_settings_switch.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'spatial_analytics_v2_event.dart';
part 'spatial_analytics_v2_state.dart';

class SpatialAnalyticsV2Bloc extends Bloc<SpatialAnalyticsV2Event, SpatialAnalyticsV2State> with ApiCacheMixin {
  SpatialAnalyticsV2Bloc() : super(SpatialAnalyticsV2InitState()) {
    transparencyNotifier = ValueNotifier(HiveUtilsSettings.geoSpatialMaterialTransparency);

    on<GetArcGISTokenEvent>(_onGetArcGISTokenEvent);
    on<GetWebMapEvent>(_onGetWebMapEvent);
    on<GetPOIListEvent>(_onGetPOIListEvent);

    on<GSFilterHistoryEvent>(_onGSFilterHistoryEvent);
    on<GSFilterResetEvent>(_onGSFilterResetEvent);

    on<GetArcGISSummaryEvent>(_onGetArcGISummaryEvent);
    on<GetDomainSummaryEvent>(_onGetDomainSummaryEvent);

    on<GetAllGeoSpatialFilterEvent>(_onGetAllGeoSpatialFilterEvent);
    on<ApplyGSFilterEvent>(_onApplyGSFilterEvent);

    on<ChangeChartTypeEvent>(_onChangeChartTypeEvent);

    on<GetGSCardSubscriptionEvent>(_onGetGSCardSubscriptionEvent);
    on<ToggleGSCardSubscriptionEvent>(_onToggleGSCardSubscriptionEvent);
  }

  final _spatialV2Repository = servicelocator<SpatialAnalyticsV2Repository>();

  late final ValueNotifier<double> transparencyNotifier;

  int selectedDomainId = 1;

  final _chartTypeCache = <int, GSChartType>{};

  GSChartType? getChartOfIndicator(int id) => _chartTypeCache[id];

  GSFilter? allFilter;

  int _filterStackIndex = 0;

  GSFilterParams _filter = GSFilterParams.init();

  GSFilterParams get filter => _filter;

  final _filterConfigStack = [
    GSFilterParams.init(),
  ];

  GSFilterParams get tempFilter => _filterConfigStack[_filterStackIndex];

  bool get canUndo => _filterStackIndex > 0;

  bool get canRedo => _filterStackIndex < (_filterConfigStack.length - 1);

  bool get canReset => tempFilter != GSFilterParams.init();

  List<int> selectedPOIIndices = [];

  final isMapLoading = ValueNotifier(false);

  final hasChartInteractedFilter = ValueNotifier(false);

  void initConfigurations() {
    // ensure filter is loaded. since _buildSelectedLocationInfo() in SpatialAnalyticsV2Screen uses
    // filter is display selected districts and regions.
    add(const GetAllGeoSpatialFilterEvent());

    _filter = GSFilterParams.init();
    _filterConfigStack
      ..clear()
      ..add(_filter);
    _filterStackIndex = 0;

    selectedPOIIndices.clear();
  }

  int get filterChangesCount {
    final isRealEstateDomain = selectedDomainId == 3;

    // For real estate domain, ignore changes in CITIZEN_CODE, GENDER_CODE, MARITAL_CODE, ATTAINMENT_CODE
    // Otherwise ignore changes in BUILDING_TYPE_CODE, BUILDING_USE_CODE, UNITS_TYPE_CODE, UNITS_USE_CODE
    final ignoreKeys = isRealEstateDomain
        ? ['CITIZEN_CODE', 'GENDER_CODE', 'MARITAL_CODE', 'ATTAINMENT_CODE']
        : ['BUILDING_TYPE_CODE', 'BUILDING_USE_CODE', 'UNITS_TYPE_CODE', 'UNITS_USE_CODE'];

    final json = filter.toJson();
    final defaultJson = GSFilterParams.init().toJson();
    return json.entries
        .where(
          (e) => !ignoreKeys.contains(e.key) && jsonEncode(e.value) != jsonEncode(defaultJson[e.key]),
        )
        .length;
  }

  Future<bool> get hasAccessToCommunities async {
    final response = await _spatialV2Repository.getDomainAccess();
    return response.response?.communityAccess ?? false;
  }

  Future<String> getDomainName() async {
    final filter = GSFilterParams.init();
    final response = await _spatialV2Repository.getSummary(filter);
    final index = response.response?.indexWhere((e) => e.domainId == selectedDomainId);
    return response.response?[index ?? 0].domainDisplayName ?? '';
  }

  Future<GSIndicatorConfiguration?> getIconOfIndicatorId(int id) async {
    final response = await _spatialV2Repository.getIcons();
    if (!response.isSuccess) return null;

    final index = response.response?.indexWhere((element) => element.indicatorId == id);
    if (index?.isNegative ?? true) return null;
    return response.response?[index!];
  }

  Future<List<GSIndicatorConfiguration>> _getIndicatorsForDomainId(int domainId) async {
    final response = await _spatialV2Repository.getIcons();
    if (!response.isSuccess) return [];
    return List.from(
      response.response
              ?.where(
                (element) => element.domainId == domainId,
              )
              .toList() ??
          [],
    );
  }

  Future<void> _onGetArcGISTokenEvent(GetArcGISTokenEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    try {
      isMapLoading.value = true;
      emit(FullScreenLoadingState());

      final response = await _spatialV2Repository.getArcGISToken();

      if (response.isSuccess) {
        emit(
          GetArcGISTokenSuccessState(
            token: response.response?.accessToken ?? '',
            license: response.response?.license ?? '',
          ),
        );
      } else {
        isMapLoading.value = false;
        emit(GetArcGISTokenErrorState(error: response.errorMessage));
      }
    } on Exception catch (e, s) {
      AppLog.error(e, s);
      isMapLoading.value = false;
      emit(GetArcGISTokenErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onGetWebMapEvent(GetWebMapEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    try {
      emit(FullScreenLoadingState());
      isMapLoading.value = true;

      final portalUrl = ArcGISConfig.portalUrl;
      final hasCommunityAccess = await hasAccessToCommunities;
      final domain = ArcGISConfig.getDomain(selectedDomainId);
      final mapKey =
          hasCommunityAccess ? ArcGISConfig.getCommunityMapId(domain) : ArcGISConfig.getDistrictMapId(domain);

      final ArcGISMap map = ArcGISMap.withItem(
        PortalItem.withPortalAndItemId(
          portal: Portal(
            Uri.parse(portalUrl),
            connection: PortalConnection.authenticated,
          ),
          itemId: mapKey,
        ),
      );

      map.onLoadStatusChanged.listen((status) {
        if (status == LoadStatus.loaded) {
          Future<void>.delayed(const Duration(seconds: 2)).then(
            (value) => isMapLoading.value = false,
          );
        }
      });

      final baseMapId = HiveUtilsSettings.isLightMode ? ArcGISConfig.lightBaseMapId : ArcGISConfig.darkBaseMapId;

      final baseMap = Basemap.withItem(
        PortalItem.withPortalAndItemId(
          portal: Portal(
            Uri.parse(portalUrl),
            connection: PortalConnection.authenticated,
          ),
          itemId: baseMapId,
        ),
      );

      map.basemap = baseMap;

      await map.load();
      emit(GetWebMapSuccessState(map: map));
    } on Exception catch (e, s) {
      isMapLoading.value = false;
      if (e is ArcGISException) {
        if (/*e.code == 18007 && */ event.retryCount < 3) {
          // 18007 - You do not have permission to access the resource.
          // Web map is loading on second attempt.
          add(GetWebMapEvent(retryCount: event.retryCount + 1));
        } else {
          emit(GetWebMapErrorState(error: e.message));
        }
      } else {
        AppLog.error(e, s);
        emit(GetWebMapErrorState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    }
  }

  Future<POIItemList> _getPoiItems(FeatureTable table) async {
    final cacheKey = getStaticCacheKey('spatial-analytics-v2-poi-list');
    final cache = getCache<List<dynamic>>(cacheKey);

    if (cache != null) {
      return cache
          .map(
            (e) => POIItem.fromJson(e as JSONObject),
          )
          .toList();
    }

    // final QueryParameters p = QueryParameters()..whereClause = '1=1';
    final QueryParameters queryParameters = QueryParameters()..whereClause = "category='POI'";

    final FeatureQueryResult featureQueryResult = await table.queryFeatures(queryParameters);
    final List<Feature> features = featureQueryResult.features().toList();

    await ((table as ServiceFeatureTable?)?..featureRequestMode = FeatureRequestMode.manualCache)
        ?.loadOrRefreshFeatures(features);

    final attributes = features.toList().map((e) => e.attributes).toList()
      ..sort(
        (a, b) => a['parameter'].toString().compareTo(b['parameter'].toString()),
      );
    final items = attributes
        .map(
          (feature) => POIItem(
            nameEn: feature['desc_en'].toString(),
            nameAr: feature['desc_ar'].toString(),
            url: feature['value'].toString(),
            icon: (feature['icon'] as List).cast<int>(),
          ),
        )
        .toList();

    await setCache(key: cacheKey, value: items);
    return items;
  }

  Future<POIIconList> _getPOIIcons() async {
    final response = await _spatialV2Repository.getPOIIcons();
    if (response.isSuccess) {
      return response.response!;
    }
    throw Exception(response.errorMessage);
  }

  Future<void> _onGetPOIListEvent(GetPOIListEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    final FeatureTable table = event.featureTables.first;
    if (table.displayName != 'POI') return;

    emit(GetPOIListLoadingState());

    try {
      final result = await Future.wait([
        _getPOIIcons(),
        _getPoiItems(table),
      ]);

      final icons = result.first as POIIconList;
      final items = result.last as POIItemList;

      emit(
        GetPOIListSuccessState(
          list: items,
          icons: icons,
        ),
      );
    } on Exception catch (e, st) {
      AppLog.error(e, st);
      if (e is ArcGISException) {
        emit(GetPOIListErrorState(error: e.message));
      } else {
        emit(GetPOIListErrorState(error: LocaleKeys.somethingWentWrong.tr()));
      }
    }
  }

  Future<void> _onGSFilterHistoryEvent(GSFilterHistoryEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    if (event.isUndo) {
      if (_filterStackIndex <= 0) return;
      _filterStackIndex--;
    } else {
      if (_filterStackIndex >= (_filterConfigStack.length - 1)) return;
      _filterStackIndex++;
    }

    emit(
      GSFilterChangedState(
        filter: tempFilter,
        nonce: DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  Future<void> _onGSFilterResetEvent(GSFilterResetEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    emit(
      GSFilterChangedState(
        filter: GSFilterParams.init(),
        nonce: DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  Future<void> _onGetAllGeoSpatialFilterEvent(
    GetAllGeoSpatialFilterEvent event,
    Emitter<SpatialAnalyticsV2State> emit,
  ) async {
    try {
      emit(GetAllGSFilterLoadingState());

      final RepoResponse<GSFilter> response = await _spatialV2Repository.getAllFilters();
      if (response.isSuccess) {
        allFilter = response.response;
        final index = _filterConfigStack.indexWhere((element) => element == _filter);
        if (index != -1) {
          _filterStackIndex = index;
        }

        emit(
          GetAllGSFilterSuccessState(filter: _filter),
        );
      } else {
        emit(GetAllGSFilterErrorState(error: response.errorMessage));
      }
    } on Exception {
      emit(
        GetAllGSFilterErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onGetArcGISummaryEvent(GetArcGISSummaryEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    try {
      emit(const GetArcGISSummaryLoadingState());

      final responses = await Future.wait([
        _spatialV2Repository.getSummary(event.filter),
        _spatialV2Repository.getDomainAccess(),
      ]);

      final isAllResponseSuccess = responses.every((e) => e.isSuccess);
      if (!isAllResponseSuccess) {
        final errorMessage = responses.firstWhere((e) => !e.isSuccess).errorMessage;
        return emit(
          GetArcGISSummaryErrorState(error: errorMessage),
        );
      }

      final summaryResponse = responses.first as RepoResponse<GSSummaryList>;
      final domainAccessResponse = responses.last as RepoResponse<GSDomainAccess>;

      final accessibleSummaries = (summaryResponse.response ?? [])
          .where(
            (e) => domainAccessResponse.response?.accessibleDomainIds.contains(e.domainId) ?? false,
          )
          .toList();

      emit(
        GetArcGISSummarySuccessState(summary: accessibleSummaries),
      );
    } on Exception {
      emit(
        GetArcGISSummaryErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<RepoResponse<GSDomainSummary>> _getDomainSummary() async {
    RepoResponse<GSDomainSummary> response;
    switch (selectedDomainId) {
      case 1:
        response = await _spatialV2Repository.getPopulationSummary(_filter);
      case 2:
        response = await _spatialV2Repository.getLaborForceSummary(_filter);
      case 3:
        response = await _spatialV2Repository.getRealEstateSummary(_filter);
      default:
        throw Error();
    }

    return response;
  }

  Future<void> _onGetDomainSummaryEvent(GetDomainSummaryEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    try {
      emit(const GetDomainSummaryLoadingState());

      final response = await _getDomainSummary();
      if (!response.isSuccess) {
        return emit(
          GetDomainSummaryErrorState(error: response.errorMessage),
        );
      }

      final config = await _getIndicatorsForDomainId(selectedDomainId);
      emit(
        GetDomainSummarySuccessState(
          summary: response.response!,
          indicatorConfigs: config,
        ),
      );
    } on Exception {
      emit(
        GetDomainSummaryErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  Future<void> _onApplyGSFilterEvent(ApplyGSFilterEvent event, Emitter<SpatialAnalyticsV2State> emit) async {
    final isSameDomain = selectedDomainId == event.domainId;
    if (!isSameDomain) {
      add(const GetWebMapEvent());
    }
    selectedDomainId = event.domainId;

    final currentFilter = _filterConfigStack[_filterStackIndex];
    if (isSameDomain && currentFilter == event.filter) {
      emit(const ApplyGSFilterAcceptedState());
      return;
    }

    hasChartInteractedFilter.value = false;

    final filter = event.filter;
    if (filter.districtCode.isNotEmpty) {
      // only keep regions of districts that are selected
      final districts = allFilter?.districts.where(
            (e) => filter.districtCode.contains(e.districtCode),
          ) ??
          [];
      final selectedRegionCodes = districts.map((e) => e.regionCode ?? '').toSet();
      filter.regionCode
        ..clear()
        ..addAll(selectedRegionCodes);
    }

    _filterConfigStack
      ..removeRange(_filterStackIndex + 1, _filterConfigStack.length)
      ..add(filter);
    _filterStackIndex = _filterConfigStack.length - 1;
    _filter = tempFilter;

    if (event.shouldHighlightArea && !isMapLoading.value) {
      emit(
        SetMapLayerVisibilityOnFilterState(
          filter: _filter,
          didDomainChange: !isSameDomain,
        ),
      );
    }

    emit(const ApplyGSFilterAcceptedState());
    //  a short delay for bottom sheet transitions
    await Future<void>.delayed(const Duration(milliseconds: 100));

    try {
      emit(const GetDomainSummaryLoadingState());

      final response = await _getDomainSummary();
      if (!response.isSuccess) {
        return emit(
          GetDomainSummaryErrorState(error: response.errorMessage),
        );
      }

      final configs = await _getIndicatorsForDomainId(selectedDomainId);
      if (event.isInteractiveFilter) {
        hasChartInteractedFilter.value = true;
      }
      emit(
        GetDomainSummarySuccessState(
          summary: response.response!,
          indicatorConfigs: configs,
          interactionFromIndicatorId: event.interactionFromIndicatorId,
        ),
      );
    } on Exception {
      emit(
        GetDomainSummaryErrorState(error: LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }

  void _onChangeChartTypeEvent(ChangeChartTypeEvent event, Emitter<SpatialAnalyticsV2State> emit) {
    _chartTypeCache[event.indicatorId] = event.chartType;
    emit(
      GSChartTypeChangedState(
        indicatorId: event.indicatorId,
        chartType: event.chartType,
      ),
    );
  }

  Future<void> _onGetGSCardSubscriptionEvent(
    GetGSCardSubscriptionEvent event,
    Emitter<SpatialAnalyticsV2State> emit,
  ) async {
    emit(
      GSCardSubscriptionLoadingState(id: event.id),
    );

    try {
      final response = await servicelocator<IndicatorCardRepository>().getIndicatorStatus(id: event.id.toString());
      if (!response.isSuccess) {
        emit(
          GSCardSubscriptionErrorState(
            id: event.id,
            error: response.errorMessage,
          ),
        );
        return;
      }

      emit(
        GSCardSubscriptionSuccessState(
          id: event.id,
          subscription: response.response?.subscriptions,
        ),
      );
    } on Exception {
      emit(
        GSCardSubscriptionErrorState(
          id: event.id,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  Future<void> _onToggleGSCardSubscriptionEvent(
    ToggleGSCardSubscriptionEvent event,
    Emitter<SpatialAnalyticsV2State> emit,
  ) async {
    emit(
      GSCardSubscriptionLoadingState(id: event.id),
    );

    const contentType = 'Spatial-Analytics';
    try {
      final repository = servicelocator<NotificationRepository>();
      final uuid = event.uuid;
      final RepoResponse<dynamic> response;
      String? message;

      if (uuid != null) {
        response = await repository.removeSubscription(
          indicatorId: event.id.toString(),
          nodeId: uuid,
        );
        message = (response.response as RemoveSubscriptionResponse).message;
      } else {
        response = await repository.createSubscription(
          indicatorId: event.id.toString(),
          payload: SubscriptionRequest(
            nodeId: event.id.toString(),
            appType: contentType,
            contentType: contentType,
          ),
        );
        message = (response.response as SubscriptionResponse).message;
      }

      if (!response.isSuccess) {
        return emit(
          GSCardSubscriptionErrorState(
            id: event.id,
            error: response.errorMessage,
          ),
        );
      }

      final updatedResponse =
          await servicelocator<IndicatorCardRepository>().getIndicatorStatus(id: event.id.toString());
      if (!updatedResponse.isSuccess) {
        return emit(
          GSCardSubscriptionErrorState(
            id: event.id,
            error: updatedResponse.errorMessage,
          ),
        );
      }

      AppMessage.showOverlayNotificationSuccess(
        title: event.title,
        message: message ?? '',
      );

      return emit(
        GSCardSubscriptionSuccessState(
          id: event.id,
          subscription: updatedResponse.response?.subscriptions,
        ),
      );
    } on Exception {
      emit(
        GSCardSubscriptionErrorState(
          id: event.id,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }
}
