import 'dart:io';
import 'dart:math';

import 'package:arcgis_maps/arcgis_maps.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/config/app_config/secret.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter_params.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/pages/mixin/arcgis_map_interaction_mixin.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/pages/mixin/arcgis_map_types_mixin.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/sheets/gs_filter_bottom_sheet/gs_filter_bottom_sheet.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/sheets/gs_indicator_card_bottom_sheet/gs_indicator_card_bottom_sheet.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/gs_app_bar.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/gs_back_button.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/gs_feature_layer_scale.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/gs_toolbar/gs_toolbar.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'base_state.dart';
part 'mixin/arcgis_measurement_tool_mixin.dart';
part 'mixin/arcgis_selection_tool_mixin.dart';
part 'mixin/persistent_controller_mixin.dart';

@RoutePage()
class SpatialAnalyticsV2Screen extends StatefulWidget {
  const SpatialAnalyticsV2Screen({
    this.initialDomainId = 1,
    super.key,
  });

  final int initialDomainId;

  @override
  State<SpatialAnalyticsV2Screen> createState() => _SpatialAnalyticsV2ScreenState();

  static Future<void> navigate(
    BuildContext context, {
    int initialDomainId = 1, // for v2
    String? initialModuleKey, // for v1
    void Function(bool)? postMyAppButtonTap, // for v1
  }) async {
    if (Secret.isDemo) {
      await AutoRouter.of(context).push(
        SpatialAnalyticsScreenRoute(initialModuleKey: initialModuleKey, postMyAppButtonTap: postMyAppButtonTap),
      );
    } else {
      await AutoRouter.of(context).push(SpatialAnalyticsV2ScreenRoute(initialDomainId: initialDomainId));
    }
  }
}

class _SpatialAnalyticsV2ScreenState extends SpatialBaseState
    with _ArcGISMeasurementToolMixin, _ArcGISSelectionToolMixin, ArcGISMapInteractionMixin, ArcGISMapTypeMixin
    implements ArcGISAuthenticationChallengeHandler {
  @override
  final ArcGISMapViewController mapViewController = ArcGISMapView.createController()
    ..arcGISMap = ArcGISMap.withBasemapStyle(BasemapStyle.arcGISDarkGrayBase);

  String _accessToken = '';

  @override
  void initState() {
    super.initState();

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: HiveUtilsSettings.isLightMode ? Brightness.dark : Brightness.light,
      ),
    );

    context.read<SpatialAnalyticsV2Bloc>()
      ..selectedDomainId = widget.initialDomainId
      ..initConfigurations();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<SpatialAnalyticsV2Bloc>().add(const GetArcGISTokenEvent());
    });
  }

  Widget _buildLayerToggleButton() {
    return PopupMenuButton<String>(
      onSelected: (layerName) async {
        final layer = mapViewController.arcGISMap?.operationalLayers.firstWhere(
          (element) => element.name == layerName,
        ) as FeatureLayer?;
        if (layer == null) return;

        layer.isVisible = !layer.isVisible;
      },
      itemBuilder: (context) {
        final layers = mapViewController.arcGISMap?.operationalLayers ?? <Layer>[];
        return layers
            .map(
              (layer) => PopupMenuItem<String>(
                value: layer.name,
                child: Text(
                  layer.name,
                  style: TextStyle(
                    color: layer.isVisible ? AppColors.blueLightOld : AppColors.grey,
                    fontWeight: layer.isVisible ? FontWeight.w700 : FontWeight.normal,
                  ),
                ),
              ),
            )
            .toList();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return PersistentSheetControllerProvider(
      draggableSheetController: _draggableSheetController,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        floatingActionButton: kDebugMode ? _buildLayerToggleButton() : null,
        body: BlocConsumer<SpatialAnalyticsV2Bloc, SpatialAnalyticsV2State>(
          listenWhen: (_, state) => state is MapState || state is BaseGetPOIListState,
          buildWhen: (_, state) => state is MapState || state is BaseGetPOIListState,
          listener: (context, state) {
            if (state is GetArcGISTokenSuccessState) {
              _accessToken = state.token;
              ArcGISEnvironment.setLicenseUsingKey(state.license);
              ArcGISEnvironment.authenticationManager.arcGISAuthenticationChallengeHandler = this;

              // Workaround for ArcGIS authentication
              // ArcGIS SDK is able to use the access token for portal.scad.gov.ae, but not able to use
              // it for arcgis.scad.gov.ae, Hence the layer loading fails with No Token Provided error.
              //
              // Hence, set the token in header for ArcGIS HttpClient. This is a workaround and still doesn't
              // answers why the issue only occurs outside SCAD network.
              final headers = ArcGISEnvironment.httpClient.customHeaders;
              if (headers == null) {
                ArcGISEnvironment.httpClient.customHeaders = {
                  HttpHeaders.authorizationHeader: 'Bearer $_accessToken',
                };
              } else {
                headers.addAll({
                  HttpHeaders.authorizationHeader: 'Bearer $_accessToken',
                });
              }

              bindMeasurementGraphicsOverlay();
              bindHighlightGraphicsOverlay();
            }

            if (state is GetWebMapSuccessState) {
              mapViewController
                ..arcGISMap = state.map
                ..interactionOptions.rotateEnabled = false
                ..setViewpoint(
                  Viewpoint.withLatLongScale(latitude: 24.400, longitude: 54.375, scale: 220000),
                );

              updateLayerData();
            }

            if (state is SetMapLayerVisibilityOnFilterState) {
              updateLayerData();
              if (state.didDomainChange) {
                clearHighlight();
              } else {
                highlightAreaOnMap(state.filter);
              }
            }

            if (state is GetPOIListErrorState) {
              AppMessage.showOverlayNotificationError(message: state.error);
            }
          },
          builder: (context, state) {
            final hasArcGisMap = mapViewController.arcGISMap != null;
            final hasToken = _accessToken.isNotEmpty;

            return Stack(
              children: [
                if (hasToken && hasArcGisMap)
                  Positioned.fill(
                    bottom: -60,
                    child: ArcGISMapView(
                      controllerProvider: () => mapViewController,
                      onTap: (offset) {
                        if (measurementType == MeasurementType.none) {
                          onMapTapped(offset);
                        }
                      },
                      onMapViewReady: () {
                        AppLog.info('Map view is ready');
                        context.read<SpatialAnalyticsV2Bloc>().add(const GetWebMapEvent());
                      },
                    ),
                  ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const GsAppBar(),
                    if (hasArcGisMap)
                      GSToolBar(
                        geometryAdapter: this,
                        persistentBottomSheetAdapter: this,
                        mapViewController: mapViewController,
                        onAreaSelectionChanged: onAreaSelectionChanged,
                        onFilterTap: () => showHidingPersistentBottomSheet(
                          context,
                          builder: (context) => GSFiltersBottomSheet.show(context),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSelectedLocationInfo(),
                          const SizedBox(height: 8),
                          AnimatedSize(
                            duration: const Duration(milliseconds: 300),
                            alignment: Alignment.topCenter,
                            curve: Curves.fastLinearToSlowEaseIn,
                            child: GsFeatureLayerScale(
                              mapController: mapViewController,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                GSIndicatorBottomSheet(
                  controller: _draggableSheetController,
                ),
                _buildLoadingOrErrorStateOverlay(state),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    ArcGISEnvironment.authenticationManager.arcGISAuthenticationChallengeHandler = null;
    ArcGISEnvironment.authenticationManager.arcGISCredentialStore.removeAll();
    mapViewController.dispose();

    super.dispose();
  }

  @override
  void handleArcGISAuthenticationChallenge(ArcGISAuthenticationChallenge challenge) {
    final TokenInfo? tokenInfo = TokenInfo.create(
      accessToken: _accessToken,
      expirationDate: DateTime.now().add(const Duration(hours: 8)),
      isSslRequired: true,
    );

    if (tokenInfo == null) return;

    final preGeneratedTokenCredential = PregeneratedTokenCredential(
      uri: challenge.requestUri,
      tokenInfo: tokenInfo,
      referer: '',
    );

    challenge.continueWithCredential(preGeneratedTokenCredential);
  }

  Widget _buildLoadingOrErrorStateOverlay(SpatialAnalyticsV2State state) {
    return ValueListenableBuilder(
      valueListenable: context.read<SpatialAnalyticsV2Bloc>().isMapLoading,
      builder: (context, isLoading, child) {
        Widget nonLoadingChild = const SizedBox.shrink();

        if (state is GetArcGISTokenErrorState) {
          nonLoadingChild = ErrorReloadPlaceholder(
            error: state.error,
            onReload: () => context.read<SpatialAnalyticsV2Bloc>().add(const GetArcGISTokenEvent()),
          );
        }

        if (state is GetWebMapErrorState) {
          nonLoadingChild = ErrorReloadPlaceholder(
            error: state.error,
            onReload: () => context.read<SpatialAnalyticsV2Bloc>().add(const GetWebMapEvent()),
          );
        }

        return AnimatedSwitcher(
          reverseDuration: const Duration(milliseconds: 300),
          duration: const Duration(milliseconds: 50),
          transitionBuilder: (child, anim) {
            return FadeTransition(
              opacity: anim,
              child: child,
            );
          },
          child: Builder(
            builder: (context) {
              Widget child;

              if (isLoading) {
                child = Center(
                  child: Image.asset(
                    AppImages.animatedLogoLoading,
                    width: 150,
                    height: 150,
                  ),
                );
              } else {
                if (nonLoadingChild is SizedBox) {
                  //  Everything is successful
                  return nonLoadingChild;
                }

                child = nonLoadingChild;
              }

              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                child: ColoredBox(
                  color: AppColors.scaffoldBackground,
                  child: SafeArea(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const GSBackButton(padding: 24),
                        Expanded(
                          child: Center(child: child),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSelectedLocationInfo() {
    return BlocBuilder<SpatialAnalyticsV2Bloc, SpatialAnalyticsV2State>(
      buildWhen: (prevState, state) => state is ApplyGSFilterAcceptedState,
      builder: (context, state) {
        final filter = context.read<SpatialAnalyticsV2Bloc>().filter;
        final allFilter = context.read<SpatialAnalyticsV2Bloc>().allFilter;
        final regionCodes = filter.regionCode;
        final districtCodes = filter.districtCode;

        List<String> items;

        if (districtCodes.isNotEmpty) {
          final districts = allFilter?.districts.where((e) => districtCodes.contains(e.districtCode)).toList() ?? [];
          items = districts.map((e) => e.displayText ?? '').toList();
        } else {
          final isAbuDhabiEmirateSelected = regionCodes.contains(kAbuDhabiEmirateCode);
          final isAllRegionSelected = allFilter?.regions.every(
                (region) => regionCodes.contains(region.regionCode),
              ) ??
              false;
          if (isAbuDhabiEmirateSelected || isAllRegionSelected) {
            items = [LocaleKeys.spatial_abuDhabiEmirate.tr()];
          } else {
            final regions = allFilter?.regions.where((e) => regionCodes.contains(e.regionCode)).toList() ?? [];
            items = regions.map((e) => e.displayText ?? '').toList();
          }
        }

        String text;
        if (items.length > 5) {
          text = '${items.sublist(0, 3).join(' + ')} + ${items.length - 3} ${LocaleKeys.spatial_more.tr()}';
        } else {
          text = items.join(' + ');
        }

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Icon(
                Icons.location_on_outlined,
                size: 16 * HiveUtilsSettings.textSizeFactor,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                text,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
