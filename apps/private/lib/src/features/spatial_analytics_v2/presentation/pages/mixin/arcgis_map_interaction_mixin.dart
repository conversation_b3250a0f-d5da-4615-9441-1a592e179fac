import 'dart:ui';

import 'package:arcgis_maps/arcgis_maps.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter_params.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/pages/spatial_analytics_v2_screen.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

const _kLayerScales = {
  kRegion: 4622324.434309,
  kDistricts: 288895.277144,
  kCommunities: 72223.819286,
};

mixin ArcGISMapInteractionMixin on SpatialBaseState {
  Future<void> onMapTapped(Offset localPosition) async {
    final bloc = context.read<SpatialAnalyticsV2Bloc>();
    final hasCommunitiesAccess = await bloc.hasAccessToCommunities;

    try {
      final double scale = mapViewController.scale;
      final layers = mapViewController.arcGISMap?.operationalLayers ?? <FeatureLayer>[];

      Layer? selectedFeatureLayer;

      final isCommunityScale = hasCommunitiesAccess && scale < _kLayerScales[kCommunities]!;
      if (isCommunityScale) {
        selectedFeatureLayer = layers.where((e) => e.name == kCommunities).firstOrNull;
      } else if (scale < _kLayerScales[kDistricts]!) {
        selectedFeatureLayer = layers.where((e) => e.name == kDistricts).firstOrNull;
      } else {
        selectedFeatureLayer = layers.where((e) => e.name == kRegion).firstOrNull;
      }

      if (selectedFeatureLayer == null) {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.spatial_noAreaFound.tr());
        return;
      }

      // Perform an identify operation on the feature layer at the tapped location.
      final identifyResult = await mapViewController.identifyLayer(
        selectedFeatureLayer,
        screenPoint: localPosition,
        tolerance: 12,
      );

      if (identifyResult.geoElements.isEmpty) return;

      final feature = identifyResult.geoElements.first as ArcGISFeature;

      final filter = bloc.filter.clone()
        ..regionCode.clear()
        ..districtCode.clear()
        ..communityCode.clear();

      final regionCode = getRegionCodeFromAttributes(selectedFeatureLayer.name, feature.attributes);
      final districtCode = getDistrictCodeFromAttributes(selectedFeatureLayer.name, feature.attributes);
      final communityCode = getCommunityCodeFromAttributes(selectedFeatureLayer.name, feature.attributes);

      if (regionCode != null) {
        filter.regionCode.add(regionCode);
      }
      if (districtCode != null) {
        filter.districtCode.add(districtCode);
      }
      if (communityCode != null) {
        filter.communityCode.add(communityCode);
      }

      bloc.add(
        ApplyGSFilterEvent(
          domainId: bloc.selectedDomainId,
          filter: filter,
        ),
      );
    } on Exception catch (e, s) {
      AppLog.error(e, s);
      AppMessage.showOverlayNotificationError(
        message: LocaleKeys.spatial_unableToIdentifyTheArea.tr(),
      );
    }
  }

  void clearHighlight() => highlightGraphicsOverlay.graphics.clear();

  Future<void> highlightAreaOnMap(GSFilterParams filter) async {
    if (filter.communityCode.isNotEmpty) {
      return _highlightArea(kCommunities, filter.communityCode.toList());
    }

    if (filter.districtCode.isNotEmpty) {
      return _highlightArea(kDistricts, filter.districtCode.toList());
    }

    if (filter.regionCode.isNotEmpty) {
      return _highlightArea(kRegion, filter.regionCode.toList());
    }
  }

  QueryParameters? _getAreaSelectionQueryParams(String layerName, List<dynamic> codes) {
    if (layerName == kRegion) {
      final regionCodes = <String>[];
      if (codes.contains(kAbuDhabiEmirateCode)) {
        regionCodes.addAll(['01', '02', '03']);
      } else {
        final mapCodes = codes
            .map(
              (code) => [kRegionAbuDhabi, kRegionAlAin, kRegionAlDhafra].contains(code)
                  ? code.substring(1).toString()
                  : code.toString(),
            )
            .toList();
        regionCodes.addAll(mapCodes);
      }

      final values = regionCodes.map((code) => "'$code'").join(', ');
      return QueryParameters()..whereClause = 'region_code in ($values)';
    }

    if (layerName == kDistricts) {
      final values = codes.toList().map((code) => "'$code'").join(', ');
      return QueryParameters()..whereClause = 'district_code in ($values)';
    }

    if (layerName == kCommunities) {
      final values = codes.toList().map((code) => "'$code'").join(', ');
      return QueryParameters()..whereClause = 'SCAD_SC_ID in ($values)';
    }

    return null;
  }

  // Highlight by drawing border around the selected area on the map
  Future<void> _highlightArea(String selectedLayerName, List<dynamic> codes) async {
    try {
      final ArcGISMap? map = mapViewController.arcGISMap;
      if (map == null) return;

      highlightGraphicsOverlay.graphics.clear();
      final List<Layer> operationalLayers = map.operationalLayers;

      final layer = operationalLayers.where((e) => e.name == selectedLayerName).firstOrNull as FeatureLayer?;

      if (layer == null) return;

      final queryParameters = _getAreaSelectionQueryParams(selectedLayerName, codes);
      if (queryParameters == null) return;

      final FeatureQueryResult result = await layer.featureTable!.queryFeatures(queryParameters);
      if (result.features().toList().isEmpty) return;

      highlightGraphicsOverlay.graphics.addAll(
        result.features().toList().map(
              (e) => Graphic(
                geometry: e.geometry,
                symbol: highlightFillSymbol,
              ),
            ),
      );

      await mapViewController.setViewpointGeometry(result.features().toList().last.geometry!, paddingInDiPs: 70);

      final Envelope geometryExtent = GeometryEngine.combineExtentsCollection(
        geometries: result.features().map((e) => e.geometry!).toList(),
      ).extent;

      final ArcGISPoint originalCenter = geometryExtent.center;

      final ArcGISPoint newCenter = ArcGISPoint(
        x: originalCenter.x,
        y: originalCenter.y - (geometryExtent.height * 0.5),
        spatialReference: originalCenter.spatialReference,
      );

      final Viewpoint newViewpoint = Viewpoint.fromCenter(
        newCenter,
        scale: mapViewController.scale,
      );

      await mapViewController.setViewpointAnimated(newViewpoint, duration: 1);
    } on Exception catch (e, s) {
      AppLog.error(e, s);
    }
  }
}
