import 'dart:async';
import 'dart:io';

import 'package:arcgis_maps/arcgis_maps.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/config/app_config/arcgis_config.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter_params.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/poi_item.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/pages/spatial_analytics_v2_screen.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/sheets/gs_filter_bottom_sheet/gs_filter_bottom_sheet.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/sheets/gs_indicator_card_bottom_sheet/gs_indicator_card_bottom_sheet.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/glass_material.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/gs_toolbar/dot_density_graphics_mixin.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/image_cache_file.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/file_utils.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/geo_spatial_assets.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'buttons.dart';
part 'gs_toolbar_cubit.dart';
part 'views/gs_measurement_view.dart';
part 'views/gs_poi_list_view.dart';
part 'views/gs_search_view.dart';

enum _MapType {
  heatmap(LocaleKeys.spatial_heatMap, kHeatMapLayerNameKey),
  dotDensity(LocaleKeys.spatial_dotDensity, kDotDensityLayerNameKey),
  thematic(LocaleKeys.spatial_thematicMap, kThematicLayerNameKey);

  const _MapType(this.localeKey, this.key);

  final String localeKey;
  final String key;
}

enum GSToolBarMode {
  tools,
  toolAreaSelection,
  toolMeasureArea,
  toolMeasureDistance,
  search,
  poi,
  none;

  static List<GSToolBarMode> get nonSearchTools => [
        tools,
        toolAreaSelection,
        toolMeasureArea,
        toolMeasureDistance,
        poi,
      ];
}

class GSToolBar extends StatefulWidget {
  const GSToolBar({
    required this.onFilterTap,
    required this.geometryAdapter,
    required this.persistentBottomSheetAdapter,
    required this.onAreaSelectionChanged,
    required this.mapViewController,
    super.key,
  });

  final GSGeometryEditAdapter geometryAdapter;
  final PersistentBottomSheetAdapter persistentBottomSheetAdapter;
  final VoidCallback onFilterTap;
  final Future<void> Function(Geometry) onAreaSelectionChanged;
  final ArcGISMapViewController mapViewController;

  @override
  State<GSToolBar> createState() => _GSToolBarState();
}

class _GSToolBarState extends State<GSToolBar> with DotDensityGraphicsMixin {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  StreamSubscription<dynamic>? _areaSelectionStreamSubscription;

  late final _cubit = GSToolBarCubit(
    persistentBottomSheetAdapter: widget.persistentBottomSheetAdapter,
    geometryAdapter: widget.geometryAdapter,
  );

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _cubit,
      child: BlocBuilder<GSToolBarCubit, GSToolBarMode>(
        builder: (context, viewType) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: viewType == GSToolBarMode.none ? null : () {},
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      _buildMapToolsButton(viewType),
                      const SizedBox(width: 12),
                      _buildMapTypeButton(),
                      const Spacer(),
                      _buildSearchButton(viewType),
                      const SizedBox(width: 12),
                      _buildFilterButton(),
                    ],
                  ),
                  _buildViews(viewType),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMapToolsButton(GSToolBarMode viewType) {
    final isNonSearchToolsOpen = GSToolBarMode.nonSearchTools.contains(viewType);
    if (isNonSearchToolsOpen) {
      return _buildCloseIconButton();
    }

    return GSPrimaryIconButton(
      icon: SvgPicture.asset(
        GeoSpatialAssets.icMapTools,
        height: 20,
        width: 20,
        colorFilter: const ColorFilter.mode(
          AppColors.white,
          BlendMode.srcIn,
        ),
      ),
      onTap: () => _cubit.navigate(context, GSToolBarMode.tools),
      tooltip: LocaleKeys.genAI_tools.tr(),
    );
  }

  Future<void> _zoomOutToShowRegion() async {
    final mapViewController = widget.mapViewController;

    final layers = widget.mapViewController.arcGISMap?.operationalLayers ?? <Layer>[];
    final layer = layers.firstWhere((e) => e.name == kRegion) as FeatureLayer;
    final query = QueryParameters()..whereClause = "region_code in ('01', '02', '03')";
    final FeatureQueryResult result = await layer.featureTable!.queryFeatures(query);

    await mapViewController.setViewpointGeometry(
      result.features().toList().last.geometry!,
      paddingInDiPs: 70,
    );

    final Envelope geometryExtent = GeometryEngine.combineExtentsCollection(
      geometries: result.features().map((e) => e.geometry!).toList(),
    ).extent;

    final ArcGISPoint originalCenter = geometryExtent.center;

    final ArcGISPoint newCenter = ArcGISPoint(
      x: originalCenter.x,
      y: originalCenter.y - (geometryExtent.height * 0.5),
      spatialReference: originalCenter.spatialReference,
    );

    final Viewpoint newViewpoint = Viewpoint.fromCenter(
      newCenter,
      scale: mapViewController.scale,
    );

    await mapViewController.setViewpointAnimated(newViewpoint, duration: 1);
  }

  Widget _buildMapTypeButton() {
    return ValueListenableBuilder(
      valueListenable: isDotDensityLayerLoading,
      builder: (context, isLoading, child) {
        if (isLoading) {
          return const SizedBox.square(
            dimension: 24,
            child: CircularProgressIndicator(),
          );
        }
        return child!;
      },
      child: PopupMenuButton(
        offset: const Offset(0, 48),
        borderRadius: BorderRadius.circular(24),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        color: AppColors.scaffoldBackground.withValues(
          alpha: clampDouble(context.read<SpatialAnalyticsV2Bloc>().transparencyNotifier.value, 0.3, 1),
        ),
        onSelected: (mapType) {
          final layers = widget.mapViewController.arcGISMap?.operationalLayers;
          if (layers == null) return;

          final domainId = context.read<SpatialAnalyticsV2Bloc>().selectedDomainId;
          final domain = ArcGISConfig.getDomain(domainId);

          if (mapType == _MapType.dotDensity) {
            final layer = layers.firstWhere(
              (e) => e.name == ArcGISConfig.getDotDensityName(domain),
            ) as FeatureLayer;
            loadLayer(widget.mapViewController, layer);
          } else {
            clearIfExists();
          }

          final availableLayers = ArcGISConfig.getAvailableMapLayers(domain);
          final layerName = ArcGISConfig.getLayerName(domain, mapType.key);

          if (mapType == _MapType.thematic) {
            _zoomOutToShowRegion();
          }

          for (final layer in layers) {
            if (layer.name == layerName) {
              layer.isVisible = true;
            } else if (availableLayers.contains(layer.name)) {
              layer.isVisible = false;
            }
          }
        },
        itemBuilder: (context) {
          final mapTypes = [_MapType.heatmap, _MapType.thematic];
          return mapTypes
              .map(
                (e) => PopupMenuItem<_MapType>(
                  value: e,
                  child: Text(e.localeKey.tr()),
                ),
              )
              .toList();
        },
        child: GSPrimaryIconButton(
          icon: SvgPicture.asset(
            GeoSpatialAssets.icMapTrifold,
            width: 20,
            height: 20,
            colorFilter: const ColorFilter.mode(AppColors.white, BlendMode.srcIn),
          ),
          tooltip: LocaleKeys.spatial_mapTypes.tr(),
        ),
      ),
    );
  }

  Widget _buildSearchButton(GSToolBarMode viewType) {
    final isSearchOpen = viewType == GSToolBarMode.search;

    if (isSearchOpen) {
      return _buildCloseIconButton();
    }

    return GSPrimaryIconButton(
      icon: SvgPicture.asset(
        GeoSpatialAssets.icSearch,
        height: 20,
        width: 20,
        colorFilter: const ColorFilter.mode(AppColors.white, BlendMode.srcIn),
      ),
      onTap: () => _cubit.navigate(context, GSToolBarMode.search),
      tooltip: LocaleKeys.search.tr(),
    );
  }

  Widget _buildFilterButton() {
    final bloc = context.read<SpatialAnalyticsV2Bloc>();

    return BlocBuilder<SpatialAnalyticsV2Bloc, SpatialAnalyticsV2State>(
      buildWhen: (previousState, currentState) => currentState is ApplyGSFilterAcceptedState,
      builder: (context, state) {
        final count = bloc.filterChangesCount;
        final hasFilterChanged = count > 0;

        return Stack(
          children: [
            GSPrimaryIconButton(
              icon: SvgPicture.asset(
                GeoSpatialAssets.icFilter,
                height: 20,
                width: 20,
                colorFilter: const ColorFilter.mode(AppColors.white, BlendMode.srcIn),
              ),
              onTap: () {
                _cubit.navigate(context, GSToolBarMode.none);
                widget.onFilterTap();
              },
              tooltip: LocaleKeys.filters.tr(),
            ),
            Positioned(
              right: HiveUtilsSettings.isLanguageArabic ? null : 0,
              left: HiveUtilsSettings.isLanguageArabic ? 0 : null,
              child: AnimatedSize(
                duration: const Duration(milliseconds: 300),
                child: hasFilterChanged
                    ? DecoratedBox(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: AppColors.red,
                        ),
                        child: SizedBox.square(
                          dimension: 14,
                          child: FittedBox(
                            child: Text(
                              count.toString(),
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCloseIconButton() {
    return GSPrimaryIconButton(
      icon: SvgPicture.asset(
        GeoSpatialAssets.icCancel,
        height: 12,
        width: 12,
        colorFilter: const ColorFilter.mode(AppColors.white, BlendMode.srcIn),
      ),
      onTap: () => _cubit.navigate(context, GSToolBarMode.none),
      tooltip: LocaleKeys.cancel.tr(),
    );
  }

  Widget _buildViews(GSToolBarMode viewType) {
    Widget child = const SizedBox.shrink();

    switch (viewType) {
      case GSToolBarMode.tools:
        child = _buildViewTools();
      case GSToolBarMode.toolAreaSelection:
      case GSToolBarMode.toolMeasureArea:
      case GSToolBarMode.toolMeasureDistance:
        child = GSMeasurementView(
          type: viewType,
          mapController: widget.mapViewController,
        );
      case GSToolBarMode.search:
        child = const GSSearchView();
      case GSToolBarMode.poi:
        child = GSPOIListView(
          mapViewController: widget.mapViewController,
          onBackTapped: () => _cubit.navigate(context, GSToolBarMode.tools),
          onDoneTapped: () => _cubit.navigate(context, GSToolBarMode.none),
        );
      case GSToolBarMode.none:
    }

    return Flexible(
      child: AnimatedSize(
        alignment: Alignment.topRight,
        curve: Curves.fastLinearToSlowEaseIn,
        duration: const Duration(milliseconds: 200),
        child: viewType == GSToolBarMode.none
            ? const SizedBox.shrink()
            : Padding(
                padding: const EdgeInsets.only(top: 12),
                child: TransparencyListenableGlassMaterial(
                  borderRadius: BorderRadius.circular(
                    viewType == GSToolBarMode.search ? 20 : 10,
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: AnimatedSize(
                      curve: Curves.fastLinearToSlowEaseIn,
                      duration: const Duration(milliseconds: 400),
                      child: child,
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildViewTools() {
    final colorFilter = ColorFilter.mode(
      _isLightMode ? AppColors.greyShade21 : AppColors.white,
      BlendMode.srcIn,
    );

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: GSToolIconButton(
              icon: SvgPicture.asset(
                GeoSpatialAssets.icMapToolAreaSelection,
                colorFilter: colorFilter,
              ),
              onTap: () {
                _cubit.navigate(context, GSToolBarMode.toolAreaSelection);

                final geometryAdapter = widget.geometryAdapter;
                _areaSelectionStreamSubscription = geometryAdapter.geometryEditor.onGeometryChanged.listen((geometry) {
                  if (geometry != null) widget.onAreaSelectionChanged.call(geometry);
                });
                geometryAdapter.startDrawing(MeasurementType.selection);
              },
              tooltip: LocaleKeys.spatial_smallAreaSelection.tr(),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: GSToolIconButton(
              icon: SvgPicture.asset(
                GeoSpatialAssets.icMapToolMeasureArea,
                colorFilter: colorFilter,
              ),
              onTap: () {
                _cubit.navigate(context, GSToolBarMode.toolMeasureArea);
                widget.geometryAdapter.startDrawing(MeasurementType.area);
              },
              tooltip: LocaleKeys.spatial_areaMeasurement.tr(),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: GSToolIconButton(
              icon: SvgPicture.asset(
                GeoSpatialAssets.icMapToolMeasureDistance,
                colorFilter: colorFilter,
              ),
              onTap: () {
                _cubit.navigate(context, GSToolBarMode.toolMeasureDistance);
                widget.geometryAdapter.startDrawing(MeasurementType.distance);
              },
              tooltip: LocaleKeys.spatial_distanceMeasurement.tr(),
            ),
          ),
          // Padding(
          //   padding: const EdgeInsets.all(12),
          //   child: GSMapOverlayToolsButton(
          //     icon: SvgPicture.asset(
          //       GeoSpatialAssets.icPoiLayers,
          //       colorFilter: colorFilter,
          //     ),
          //     onTap: () => _onNavigate(MapOverlayViewType.poi),
          //     tooltip: LocaleKeys.pointOfInterest.tr(),
          //   ),
          // ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _areaSelectionStreamSubscription?.cancel();
    super.dispose();
  }
}
