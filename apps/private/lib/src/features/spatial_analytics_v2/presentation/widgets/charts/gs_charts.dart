import 'dart:math';

import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_indicator.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/extentions/int_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

part 'gs_bar_chart.dart';
part 'gs_chart_legends.dart';
part 'gs_circle_chart.dart';
part 'gs_compare_chart.dart';
part 'gs_growth_chart.dart';
part 'gs_horizontal_bar_chart.dart';
part 'gs_proportional_bar_chart.dart';
part 'gs_value_card.dart';

class GSLegendData {
  GSLegendData(this.label, this.color);

  final String label;
  final Color? color;
}

class GSChartData extends GSLegendData {
  GSChartData(
    super.value,
    this.y,
    this.date,
    this.code,
    super.color,
  );

  String get x => super.label;
  final double y;
  final DateTime date;
  final dynamic code;
}

TooltipBehavior _tooltipBehavior() {
  return TooltipBehavior(
    enable: true,
    tooltipPosition: TooltipPosition.pointer,
    shadowColor: Colors.transparent,
    color: AppColors.blackShade1,
    elevation: 0,
    borderWidth: 0,
    format: 'point.x:point.y',
    textAlignment: ChartAlignment.near,
    textStyle: const TextStyle(color: Colors.white, fontSize: 12),
  );
}
