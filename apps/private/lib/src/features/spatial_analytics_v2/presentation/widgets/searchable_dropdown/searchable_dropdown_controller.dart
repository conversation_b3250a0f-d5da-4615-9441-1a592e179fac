part of 'searchable_dropdown.dart';

/// Controller foe SearchableDropdown widget
///
/// Set available options in the dropdown using [allOptions].
///
/// Use [toggle] to toggle checkbox or radio.
///
/// Finally, call [applyValueChanges] to confirm and apply the selected values.
///
/// The selected values can be accessed from [selectedValueIndexList].
class SearchableDropdownController<T> extends ChangeNotifier {
  // Constructor for single selection mode
  SearchableDropdownController.radio() : isRadio = true;

  // Constructor for multiple selection mode
  SearchableDropdownController.checkbox() : isRadio = false;

  final bool isRadio;

  bool _isExpanded = false;

  bool get isExpanded => _isExpanded;

  set isExpanded(bool value) {
    _isExpanded = value;
    notifyListeners();
  }

  // Stores the list of options
  final allOptions = <T>[];

  //  Index of items that will be displayed in list
  final displayingIndices = <int>[];

  // Stores selected indices for multiple selection mode
  List<int> selectedValueIndexList = [];

  // Temporary list for multiple selections
  final _selectedValueIndexListTemp = <int>[];

  set selectedValueIndexListTemp(List<int> iterable) {
    _selectedValueIndexListTemp
      ..clear()
      ..addAll(iterable);
    notifyListeners();
  }

  List<int> get selectedValueIndexListTemp => _selectedValueIndexListTemp;

  final _searchFilterIndices = <int>[];

  List<int> get sortedSearchFilterIndices {
    if (_searchFilterIndices.length < 7) return _searchFilterIndices;
    return _searchFilterIndices
      ..sort((a, b) {
        final isASelected = _selectedValueIndexListTemp.contains(a);
        final isBSelected = _selectedValueIndexListTemp.contains(b);
        if (isASelected && !isBSelected) return -1;
        if (!isASelected && isBSelected) return 1;
        return 0;
      });
  }

  final TextEditingController _searchController = TextEditingController();

  bool _isVisible = true;

  bool get isVisible => _isVisible;

  set isVisible(bool value) {
    if (value == _isVisible) return;
    _isVisible = value;
    notifyListeners();
  }

  void setOptionsList(Iterable<T> options) {
    _isVisible = options.isNotEmpty;
    allOptions
      ..clear()
      ..addAll(options);

    final indices = List.generate(allOptions.length, (i) => i);
    displayingIndices
      ..clear()
      ..addAll(indices);

    _searchFilterIndices
      ..clear()
      ..addAll(indices);
    notifyListeners();
  }

  void setDisplayingIndices(Iterable<int> indices) {
    displayingIndices
      ..clear()
      ..addAll(indices);

    _searchController.text = '';
    _searchFilterIndices
      ..clear()
      ..addAll(indices);

    notifyListeners();
  }

  List<T> get selectedOptions => allOptions.indexed
      .where(
        (entry) => selectedValueIndexList.any((index) => index == entry.$1),
      )
      .map(
        (entry) => entry.$2,
      )
      .toList();

  List<T> get selectedTemp => allOptions.indexed
      .where(
        (entry) => selectedValueIndexListTemp.any((index) => index == entry.$1),
      )
      .map(
        (entry) => entry.$2,
      )
      .toList();

  // Confirms temporary selections
  void applyValueChanges() => selectedValueIndexList = [...selectedValueIndexListTemp];

  // Checks if an index is selected
  bool isSelected(int index) => selectedValueIndexListTemp.any(
        (e) => e == index,
      );

  // Toggles selection for single or multiple mode
  void toggle(int index) {
    final isSelected = this.isSelected(index);
    if (isRadio) {
      if (!isSelected) {
        _selectedValueIndexListTemp
          ..clear()
          ..add(index);
        notifyListeners();
      }
      return;
    }

    if (isSelected) {
      _selectedValueIndexListTemp.remove(index);
    } else {
      _selectedValueIndexListTemp.add(index);
    }
    notifyListeners();
  }

  void onSearch(String text, bool Function(String q, T item) searchPredicate) {
    final query = text.trim().toLowerCase();
    if (query.isEmpty) {
      _searchFilterIndices
        ..clear()
        ..addAll(displayingIndices);
      notifyListeners();
      return;
    }

    final filteredList = displayingIndices.where(
      (index) => searchPredicate.call(query, allOptions.elementAt(index)),
    );

    _searchFilterIndices
      ..clear()
      ..addAll(filteredList);
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
  }
}
