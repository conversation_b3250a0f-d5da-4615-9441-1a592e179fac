part of 'gs_charts.dart';

class GsHorizontalBarChart extends StatefulWidget {
  const GsHorizontalBarChart({
    required this.data,
    this.valueSuffix = '',
    this.onDataTapped,
    super.key,
  });

  final List<GSChartData> data;
  final String valueSuffix;
  final ValueChanged<GSChartData>? onDataTapped;

  @override
  State<GsHorizontalBarChart> createState() => _GsHorizontalBarChartState();
}

class _GsHorizontalBarChartState extends State<GsHorizontalBarChart> {
  final disabledIndices = <int>[];

  @override
  Widget build(BuildContext context) {
    widget.data.sort(
      (a, b) => b.y.compareTo(a.y),
    );

    final enabledData = List<GSChartData>.unmodifiable(widget.data)
        .reversed
        .indexed
        .where(
          (e) {
            //  For some reason the list is reversed in SfCartesianChart
            //  So reverse the index before checking
            final actualIndex = widget.data.length - 1 - e.$1;
            return !disabledIndices.contains(actualIndex);
          },
        )
        .map((e) => e.$2)
        .toList();

    return Column(
      children: [
        SfCartesianChart(
          tooltipBehavior: _tooltipBehavior(),
          margin: EdgeInsets.zero,
          primaryXAxis: CategoryAxis(
            axisLine: const AxisLine(
              color: AppColors.greyShade17,
              dashArray: [6],
            ),
            majorTickLines: const MajorTickLines(width: 0),
            majorGridLines: const MajorGridLines(width: 0),
            labelsExtent: 10,
            axisLabelFormatter: (details) => ChartAxisLabel('', const TextStyle()),
          ),
          primaryYAxis: NumericAxis(
            axisLine: const AxisLine(width: 0),
            borderColor: Colors.transparent,
            majorTickLines: const MajorTickLines(width: 0),
            majorGridLines: const MajorGridLines(
              color: AppColors.greyShade17,
              dashArray: [6],
            ),
            numberFormat: locale.NumberFormat.compactSimpleCurrency(name: '', decimalDigits: 0),
            rangePadding: ChartRangePadding.round,
          ),
          series: <CartesianSeries<GSChartData, String>>[
            BarSeries<GSChartData, String>(
              name: '',
              dataSource: enabledData,
              pointColorMapper: (GSChartData data, _) => data.color,
              xValueMapper: (GSChartData data, _) => data.x,
              yValueMapper: (GSChartData data, _) => data.y,
              animationDuration: 0,
              width: 0.6,
              spacing: 0.3,
              onPointTap: (details) {
                final callback = widget.onDataTapped;
                if (callback == null) return;

                final pointIndex = details.pointIndex;
                if (pointIndex == null) return;
                final data = enabledData.elementAt(pointIndex);
                callback.call(data);
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        GSChartLegends(
          data: widget.data,
          disabledIndices: disabledIndices,
          onLegendTapped: (int index) => setState(() {
            if (disabledIndices.contains(index)) {
              disabledIndices.remove(index);
            } else {
              disabledIndices.add(index);
            }
          }),
        ),
      ],
    );
  }
}
