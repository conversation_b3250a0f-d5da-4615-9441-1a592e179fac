part of 'gs_charts.dart';

class GS<PERSON>rowthChart extends StatelessWidget {
  const GSGrowthChart({
    required this.indicators,
    super.key,
  });

  final GSDomainSummary indicators;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      alignment: WrapAlignment.center,
      runSpacing: 12,
      spacing: 14,
      children: List.generate(
        indicators.length,
        (index) => _buildGrowthCard(index),
      ),
    );
  }

  Widget _buildGrowthCard(int index) {
    final indicator = indicators.elementAt(index);
    final isPositiveTrend = (indicator.value ?? 0) >= 0;
    final color = isPositiveTrend ? AppColors.green : AppColors.red;

    final label = indicator.label;
    final value = (indicator.value ?? 0).toStringAsFixed(2);

    const minWidth = 135.0;
    const maxWidth = 150.0;

    return Container(
      decoration: BoxDecoration(
        color: HiveUtilsSettings.isLightMode
            ? AppColors.greyShade15_1.withValues(alpha: 0.64)
            : AppColors.blueGreyShade3.withValues(alpha: 0.64),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 10),
      constraints: const BoxConstraints(
        minWidth: minWidth,
        maxWidth: maxWidth,
      ),
      child: Column(
        children: [
          if (label != null && label.isNotEmpty) ...[
            Text(
              label,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 6),
          ],
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              RotatedBox(
                quarterTurns: isPositiveTrend ? 0 : 2,
                child: SvgPicture.asset(
                  AppImages.icTrendingUp,
                  width: 16,
                  colorFilter: ColorFilter.mode(
                    color,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const SizedBox(width: 6),
              Directionality(
                textDirection: TextDirection.ltr,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
