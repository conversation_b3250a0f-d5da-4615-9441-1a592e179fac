part of 'gs_charts.dart';

class GSCompareSeries {
  GSCompareSeries({
    required this.legend,
    required this.color,
    this.data = const [],
  });

  final String legend;
  final Color color;
  final List<GSCompareData> data;
}

class GSCompareData {
  GSCompareData({
    required this.date,
    required this.value,
  });

  final DateTime date;
  final double value;
}

class GsCompareColumnChart extends StatefulWidget {
  const GsCompareColumnChart({
    required this.indicators,
    this.valueSuffix = '',
    super.key,
  });

  final GSDomainSummary indicators;
  final String valueSuffix;

  @override
  State<GsCompareColumnChart> createState() => _GsCompareColumnChartState();
}

class _GsCompareColumnChartState extends State<GsCompareColumnChart> {
  final disabledIndices = <int>[];
  final data = <GSCompareSeries>[];

  @override
  void initState() {
    super.initState();
    _prepareData();
  }

  // WHY? Cause some year may not have data and this cause discrepancy in chart
  // so make sure all time series of compare has at least an empty value.
  //
  // Refer Issue: https://scad.atlassian.net/browse/BM-387
  void _prepareData() {
    final chartColor = HiveUtilsSettings.isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    // Step 1: Collect all unique labels (legends) and unique dates (time series)
    final Set<String> legends = {};
    final Set<DateTime> dates = {};

    for (final indicator in widget.indicators) {
      final label = indicator.label ?? '';
      final date = indicator.date;
      if (label.isNotEmpty) {
        legends.add(label);
        dates.add(date);
      }
    }

    final sortedDates = dates.toList()..sort();

    // Step 2: Group indicator values by legend and date
    final Map<String, Map<DateTime, double>> valueMap = {};
    for (final indicator in widget.indicators) {
      final label = indicator.label ?? '';
      final date = indicator.date;
      final value = indicator.value ?? 0;

      if (label.isEmpty) continue;

      valueMap.putIfAbsent(label, () => {});
      valueMap[label]![date] = value;
    }

    // Step 3: Create seriesData with zero filling for missing time points
    for (final legend in legends) {
      final dataPoints = <GSCompareData>[];

      for (final date in sortedDates) {
        final value = valueMap[legend]?[date] ?? 0;
        dataPoints.add(GSCompareData(date: date, value: value));
      }

      final color = chartColor[data.length % chartColor.length];
      data.add(
        GSCompareSeries(
          legend: legend,
          color: color,
          data: dataPoints,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final enabledData = List<GSCompareSeries>.unmodifiable(data)
        .indexed
        .where((e) => !disabledIndices.contains(e.$1))
        .map((e) => e.$2)
        .toList();

    final isLightMode = HiveUtilsSettings.isLightMode;

    final maxAndMin = setMaxAndMinValueColumnChart(enabledData);

    return Column(
      children: [
        SizedBox(
          height: 300,
          width: double.infinity,
          child: SfCartesianChart(
            trackballBehavior: TrackballBehavior(
              enable: true,
              activationMode: ActivationMode.singleTap,
              tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
              shouldAlwaysShow: true,
              lineWidth: 0.5,
              lineDashArray: const [4, 2],
              markerSettings: const TrackballMarkerSettings(
                markerVisibility: TrackballVisibilityMode.hidden,
                borderColor: Colors.black,
              ),
            ),
            margin: EdgeInsets.zero,
            primaryXAxis: DateTimeAxis(
              majorTickLines: const MajorTickLines(width: 0),
              majorGridLines: const MajorGridLines(width: 0),
              interval: 1,
              edgeLabelPlacement: EdgeLabelPlacement.hide,
              dateFormat: locale.DateFormat('yyyy'),
              maximum: maxAndMin['max'],
              minimum: maxAndMin['min'],
              intervalType: DateTimeIntervalType.years,
              axisLabelFormatter: (axisLabelRenderArgs) => ChartAxisLabel(
                axisLabelRenderArgs.text,
                TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 10,
                ),
              ),
            ),
            primaryYAxis: NumericAxis(
              axisLine: const AxisLine(width: 0),
              borderColor: Colors.transparent,
              majorTickLines: const MajorTickLines(width: 0),
              numberFormat: locale.NumberFormat.compactSimpleCurrency(name: '', decimalDigits: 0),
            ),
            series: enabledData
                .map(
                  (indicator) => ColumnSeries<GSCompareData?, DateTime>(
                    name: '',
                    dataSource: indicator.data,
                    pointColorMapper: (GSCompareData? data, _) => indicator.color,
                    xValueMapper: (GSCompareData? data, _) => data?.date,
                    yValueMapper: (GSCompareData? data, _) => data?.value,
                    animationDuration: 300,
                    spacing: 0.2,
                    emptyPointSettings: EmptyPointSettings(
                      mode: EmptyPointMode.zero,
                      color: indicator.color,
                    ),
                  ),
                )
                .toList(),
          ),
        ),
        const SizedBox(height: 16),
        _buildLegends(),
      ],
    );
  }

  Widget _buildLegends() {
    return Column(
      spacing: 8,
      children: data.indexed
          .map(
            (e) => _buildLegend(e.$1, e.$2),
          )
          .toList(),
    );
  }

  Widget _buildLegend(int index, GSCompareSeries data) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final isLegendDisabled = disabledIndices.contains(index);

    return InkWell(
      onTap: () {
        if (widget.indicators.length - disabledIndices.length == 1 && !disabledIndices.contains(index)) {
          AppMessage.showOverlayNotificationError(
            message: LocaleKeys.spatial_atLeastOneLegendShouldBeEnabled.tr(),
          );
          return;
        }

        setState(() {
          if (disabledIndices.contains(index)) {
            disabledIndices.remove(index);
          } else {
            disabledIndices.add(index);
          }
        });
      },
      borderRadius: BorderRadius.circular(4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: 10 * (HiveUtilsSettings.textSizeFactor - 0.7),
            ),
            child: Icon(
              Icons.circle,
              size: 14,
              color: data.color,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              data.legend,
              style: TextStyle(
                color: !isLightMode ? AppColors.greyShade4 : null,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                decoration: isLegendDisabled ? TextDecoration.lineThrough : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, DateTime> setMaxAndMinValueColumnChart(List<GSCompareSeries> chartDataList) {
    final now = DateTime.now().abs();

    try {
      final initial = chartDataList.firstOrNull?.data.first.date ?? now;
      DateTime ogMax = initial;
      DateTime ogMin = initial;

      for (final collection in chartDataList) {
        for (final element in collection.data) {
          final date = element.date;
          if (date.compareTo(ogMax) > 0) ogMax = date;
          if (date.compareTo(ogMin) < 0) ogMin = date;
        }
      }

      DateTime max = ogMax.copyWith();
      DateTime min = ogMin.copyWith();

      min = min.previousYear;
      max = max.nextYear;

      return {'max': max, 'min': min, 'ogMax': ogMax, 'ogMin': ogMin};
    } on Exception {
      return {'max': now, 'min': now};
    }
  }
}
