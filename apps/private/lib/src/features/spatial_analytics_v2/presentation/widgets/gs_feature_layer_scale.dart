import 'dart:async';

import 'package:arcgis_maps/arcgis_maps.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/config/app_config/arcgis_config.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/glass_material.dart';
import 'package:scad_mobile/src/utils/app_utils/string_utils.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

// record with named fields
typedef _LayerViewStateChangedEvent = ({Layer layer, LayerViewState layerViewState});

class GsFeatureLayerScale extends StatefulWidget {
  const GsFeatureLayerScale({
    required this.mapController,
    super.key,
  });

  final ArcGISMapViewController mapController;

  @override
  State<GsFeatureLayerScale> createState() => _GsFeatureLayerScaleState();
}

class _GsFeatureLayerScaleState extends State<GsFeatureLayerScale> {
  late final StreamSubscription<_LayerViewStateChangedEvent> _subscription;

  final isRtl = HiveUtilsSettings.isLanguageArabic;

  Layer? _visibleLayer;

  @override
  void initState() {
    super.initState();
    _subscription = widget.mapController.onLayerViewStateChanged.listen(_onLayerViewStateChanged);
    _handleLayer();
  }

  Layer? _getVisibleLayer() {
    final layers = widget.mapController.arcGISMap?.operationalLayers;
    if (layers == null) return null;

    final domainId = context.read<SpatialAnalyticsV2Bloc>().selectedDomainId;
    final domain = ArcGISConfig.getDomain(domainId);
    final types = ArcGISConfig.getAvailableMapLayers(domain);
    final index = layers.indexWhere(
      (layer) => types.contains(layer.name) && layer.isVisible,
    );
    if (index == -1) return null;

    return layers.elementAt(index);
  }

  String? _getLayerType(Layer layer) {
    if (layer is! FeatureLayer) return null;

    final rendererJson = layer.renderer?.toJson();
    if (rendererJson == null) return null;

    final type = rendererJson['type'] as String?;
    if (type == 'heatmap') return 'heatmap';

    if (type == 'classBreaks') return 'thematic';

    final dotSize = rendererJson['dotSize'] as num?;
    final dotValue = rendererJson['dotValue'] as num?;
    if (dotSize != null && dotValue != null) return 'dot-density';

    return null;
  }

  void _handleLayer() {
    final layer = _getVisibleLayer();
    setState(() => _visibleLayer = layer);
  }

  void _onLayerViewStateChanged(_LayerViewStateChangedEvent event) => _handleLayer();

  @override
  Widget build(BuildContext context) {
    final layer = _visibleLayer;
    if (layer == null || layer is! FeatureLayer) {
      return const SizedBox.shrink();
    }

    final layerType = _getLayerType(layer);
    if (layerType == null) {
      return const SizedBox.shrink();
    }

    final renderer = layer.renderer?.toJson();
    if (renderer == null) {
      return const SizedBox.shrink();
    }

    switch (layerType) {
      case 'heatmap':
        return _buildHeatmapScale(renderer);
      case 'dot-density':
        return _buildDotDensityScale(renderer);
      case 'thematic':
        return _buildThematicScale(renderer);
    }
    return const SizedBox.shrink();
  }

  Widget _buildHeatmapScale(JSONObject renderer) {
    final colorStops = renderer['colorStops'] as List? ?? [];
    if (colorStops.isEmpty) return const SizedBox.shrink();

    final colors = <Color>[];
    final stops = <double>[];

    for (final item in colorStops) {
      final rgba = List<int>.from(item['color'] as List? ?? []);
      final stop = item['ratio'] as double?;
      if (rgba.length != 4 || stop == null) continue;

      final color = Color.fromARGB(rgba[3], rgba[0], rgba[1], rgba[2]);
      colors.add(color);
      stops.add(stop);
    }

    return _buildExpandableContainer(
      child: Row(
        children: [
          Text(
            LocaleKeys.spatial_low.tr(),
            style: const TextStyle(fontSize: 10),
          ),
          Container(
            height: 4,
            width: stops.length * 5,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              gradient: LinearGradient(
                begin: isRtl ? Alignment.centerRight : Alignment.centerLeft,
                end: isRtl ? Alignment.centerLeft : Alignment.centerRight,
                stops: stops.toList(),
                colors: colors.toList(),
              ),
            ),
          ),
          Text(
            LocaleKeys.spatial_high.tr(),
            style: const TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  Widget _buildDotDensityScale(JSONObject renderer) {
    final attributes = (renderer['attributes'] as List?)?.firstOrNull as Map?;
    if (attributes == null) {
      return const SizedBox.shrink();
    }

    final label = attributes['label'] as String?;
    final value = renderer['dotValue'] as num?;
    final rgba = List<int>.from(attributes['color'] as List? ?? []);
    if (value == null || rgba.length != 4) {
      return const SizedBox.shrink();
    }

    final color = Color.fromARGB(rgba[3], rgba[0], rgba[1], rgba[2]);

    return _buildExpandableContainer(
      label: label,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 6,
            width: 6,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: color,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '1 ${LocaleKeys.spatial_dot.tr()} = $value',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThematicScale(JSONObject renderer) {
    final classBreakInfos = renderer['classBreakInfos'] as List? ?? [];
    if (classBreakInfos.isEmpty) {
      return const SizedBox.shrink();
    }

    final breaks = <({String label, Color fillColor, Color strokeColor})>[];

    try {
      for (final breakInfo in classBreakInfos) {
        final symbolLayer = breakInfo['symbol']['symbol']?['symbolLayers'] as List? ?? [];
        if (symbolLayer.length != 2) continue;

        String? label = breakInfo['label'] as String?;
        if (label == null) continue;

        final strokeRGBA = List<int>.from(symbolLayer.firstOrNull?['color'] as List? ?? []);
        final fillRGBA = List<int>.from(symbolLayer.lastOrNull?['color'] as List? ?? []);
        if (fillRGBA.length != 4 || strokeRGBA.length != 4) continue;

        final fillColor = Color.fromARGB(fillRGBA[3], fillRGBA[0], fillRGBA[1], fillRGBA[2]);
        final strokeColor = Color.fromARGB(strokeRGBA[3], strokeRGBA[0], strokeRGBA[1], strokeRGBA[2]);

        final List<String> labelParts = label.split('-');

        label = ShortNumberPipe.transform(
              number: labelParts.firstOrNull.toString().trim(),
              angularPipeFormat: '1.0-2',
            ) ??
            '';
        if (labelParts.length > 1) {
          label += ' - ${ShortNumberPipe.transform(
                number: labelParts.lastOrNull.toString().trim(),
                angularPipeFormat: '1.0-2',
              ) ?? ''}';
        }

        final b = (label: label, fillColor: fillColor, strokeColor: strokeColor);
        breaks.add(b);
      }
    } on Exception {
      return const SizedBox.shrink();
    }

    final labelStart = breaks.first.label.split(' - ').firstOrNull ?? '';
    final labelEnd = breaks.last.label.split(' - ').lastOrNull ?? '';

    return _buildExpandableContainer(
      child: Row(
        children: [
          Text(
            labelStart,
            style: const TextStyle(fontSize: 10),
          ),
          Container(
            height: 4,
            width: breaks.length * 12,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(6),
              gradient: LinearGradient(
                begin: isRtl ? Alignment.centerRight : Alignment.centerLeft,
                end: isRtl ? Alignment.centerLeft : Alignment.centerRight,
                colors: breaks.map((e) => e.fillColor).toList(),
              ),
            ),
          ),
          Text(
            labelEnd,
            style: const TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableContainer({
    required Widget child,
    String? label,
  }) {
    final hasValidLabel = label != null && label.isNotEmpty;
    String text = label ?? '';
    if (hasValidLabel) {
      text = text.replaceAll('_', ' ');
    }

    return TransparencyListenableGlassMaterial(
      color: HiveUtilsSettings.isLightMode ? null : AppColors.black.withAlpha(136),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      borderRadius: BorderRadius.circular(8),
      child: IntrinsicWidth(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (hasValidLabel)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 4),
                      child: Text(
                        text,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.fastLinearToSlowEaseIn,
              child: Column(
                children: [
                  if (hasValidLabel) const SizedBox(height: 8),
                  child,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
