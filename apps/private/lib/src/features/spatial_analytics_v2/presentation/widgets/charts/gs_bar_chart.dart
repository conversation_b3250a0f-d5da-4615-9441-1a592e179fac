part of 'gs_charts.dart';

class GSBarChart extends StatelessWidget {
  const GSBarChart({
    required this.data,
    this.valueSuffix = '',
    this.sortData = true,
    super.key,
  });

  final List<GSChartData> data;
  final String valueSuffix;
  final bool sortData;

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;

    if (sortData) {
      data.sort(
        (a, b) => (b.y * 100).toInt() - (a.y * 100).toInt(),
      );
    }

    final double maxPercentage = data.map((e) => e.y).reduce(
          (a, b) => a > b ? a : b,
        );

    return LayoutBuilder(
      builder: (context, constraints) {
        final double availableWidth = constraints.maxWidth - 60;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: data.map((e) {
            final barWidth = maxPercentage != 0 ? (e.y / maxPercentage) * availableWidth : 0.0;
            final x = e.x;
            final y = locale.NumberFormat.compactSimpleCurrency(name: '', decimalDigits: 0).format(e.y);

            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Tooltip(
                message: '$x: $y$valueSuffix',
                triggerMode: TooltipTriggerMode.tap,
                constraints: const BoxConstraints(minHeight: 10),
                textStyle: TextStyle(
                  color: isLightMode ? AppColors.white : AppColors.greyShade1,
                  fontSize: 12,
                ),
                decoration: BoxDecoration(
                  color: isLightMode ? AppColors.black : AppColors.blackShade9,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      x,
                      style: TextStyle(
                        color: isLightMode ? AppColors.grey : AppColors.greyShade1,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Row(
                      spacing: 8,
                      children: [
                        Flexible(
                          child: Container(
                            width: max(0, barWidth),
                            height: 8,
                            margin: const EdgeInsets.only(top: 3),
                            decoration: BoxDecoration(
                              color: e.color,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ),
                        Text(
                          '$y$valueSuffix',
                          style: TextStyle(
                            color: isLightMode ? AppColors.grey : AppColors.greyShade1,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
