import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/primary_svg_icon_button.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_filter_params.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/models/gs_summary.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/glass_material.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/widgets/searchable_dropdown/searchable_dropdown.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/geo_spatial_assets.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'gs_filter_controllers.mixin.dart';

class GSFiltersBottomSheet extends StatefulWidget {
  const GSFiltersBottomSheet._();

  static PersistentBottomSheetController show(BuildContext context) {
    return showBottomSheet(
      context: context,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.sizeOf(context).height * .8,
      ),
      builder: (context) => const GSFiltersBottomSheet._(),
    );
  }

  @override
  State<GSFiltersBottomSheet> createState() => _GSFiltersBottomSheetState();
}

class _GSFiltersBottomSheetState extends State<GSFiltersBottomSheet> with GSFilterDropdownControllerMixin {
  final _hasAccessToCommunities = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    filterContext = context;
    context.read<SpatialAnalyticsV2Bloc>()
      ..add(GetArcGISSummaryEvent(filter: GSFilterParams.init()))
      ..add(const GetAllGeoSpatialFilterEvent());

    context.read<SpatialAnalyticsV2Bloc>().hasAccessToCommunities.then(
          (value) => _hasAccessToCommunities.value = value,
        );

    isRealEstateDomain = context.read<SpatialAnalyticsV2Bloc>().selectedDomainId == 3;
  }

  void _onDomainChanged(GSSummary domain) => setState(
        () => isRealEstateDomain = domain.domainId == 3,
      );

  @override
  Widget build(BuildContext context) {
    return TransparencyListenableGlassMaterial(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      child: BlocConsumer<SpatialAnalyticsV2Bloc, SpatialAnalyticsV2State>(
        buildWhen: (_, state) => state is BaseGetAllGSFilterState,
        listenWhen: (_, state) =>
            state is BaseGetArcGISSummaryState ||
            state is BaseGetAllGSFilterState ||
            state is ApplyGSFilterAcceptedState ||
            state is GSFilterChangedState,
        listener: (context, state) {
          if (state is ApplyGSFilterAcceptedState) {
            Navigator.pop(context);
          }

          if (state is GetArcGISSummarySuccessState) {
            final summaries = List<GSSummary>.from(state.summary)
              ..sort(
                (a, b) => a.domainId?.compareTo(b.domainId ?? 0) ?? 0,
              );
            final selectedDomainId = context.read<SpatialAnalyticsV2Bloc>().selectedDomainId;
            final index = summaries.indexWhere(
              (element) => element.domainId == selectedDomainId,
            );
            if (index != -1) {
              domainController.toggle(index);
            }
            domainController.setOptionsList(summaries);
          }

          if (state is GetAllGSFilterSuccessState) {
            loadDataIntoControllers(state.filter);
          }

          if (state is GSFilterChangedState) {
            initializeFilterValues(state.filter);
          }
        },
        builder: (context, state) {
          if (state is GetAllGSFilterLoadingState || state is! BaseGetAllGSFilterState) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is GetAllGSFilterErrorState) {
            return Center(
              child: ErrorReloadPlaceholder(error: state.error),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              Padding(
                padding: const EdgeInsets.all(16),
                child: _buildTitleBar(),
              ),
              Flexible(
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    SearchableDropdown<GSSummary>(
                      key: const Key('domain-dropdown'),
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                      label: LocaleKeys.domain.tr(),
                      controller: domainController,
                      displayText: (value) => value.domainDisplayName,
                      onChanged: _onDomainChanged,
                    ),
                    const Divider(height: 1, color: AppColors.greyShade1),
                    SearchableDropdown<GSRegion>(
                      key: const Key('region-dropdown'),
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                      label: LocaleKeys.spatial_regions.tr(),
                      controller: regionsController,
                      displayText: (value) => value.displayText ?? '',
                      onChanged: onRegionChanged,
                    ),
                    SearchableDropdown<GSDistrict>(
                      key: const Key('district-dropdown'),
                      label: LocaleKeys.spatial_district.tr(),
                      controller: districtsController,
                      displayText: (value) => value.displayText ?? '',
                      onChanged: (_) => onRegionOrDistrictChanged(),
                      maxExpandedHeight: 300,
                      isSearchEnabled: true,
                    ),
                    ValueListenableBuilder(
                      valueListenable: _hasAccessToCommunities,
                      builder: (context, hasAccess, child) {
                        if (hasAccess) return child!;
                        return const SizedBox(height: 16);
                      },
                      child: SearchableDropdown<GSCommunity>(
                        key: const Key('communities-dropdown'),
                        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                        label: LocaleKeys.spatial_communities.tr(),
                        controller: communitiesController,
                        displayText: (value) => value.displayText ?? '',
                        maxExpandedHeight: 300,
                        isSearchEnabled: true,
                      ),
                    ),
                    const Divider(height: 1, color: AppColors.greyShade1),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(
                        LocaleKeys.spatial_compare.tr(),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SearchableDropdown<GSQuarter>(
                      key: const Key('quarterly-dropdown'),
                      label: LocaleKeys.spatial_yearly.tr(),
                      controller: quartersController,
                      displayText: (value) => value.displayText ?? '',
                    ),
                    const SizedBox(height: 16),
                    const Divider(height: 1, color: AppColors.greyShade1),
                    AnimatedSize(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.fastLinearToSlowEaseIn,
                      child: isRealEstateDomain ? _buildRealEstateDomain() : _buildNonRealEstateDomains(),
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Divider(height: 1, color: AppColors.greyShade1),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size.fromHeight(43),
                    shape: RoundedRectangleBorder(
                      side: BorderSide(color: AppColors.blue),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () => applyFilterChanges(context),
                  child: Text(
                    LocaleKeys.apply.tr(),
                    style: const TextStyle(color: AppColors.white, fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: TextButton(
                  style: TextButton.styleFrom(
                    minimumSize: const Size.fromHeight(43),
                    shape: RoundedRectangleBorder(
                      side: BorderSide(color: AppColors.blue),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    LocaleKeys.cancel.tr(),
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: AppColors.blue),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTitleBar() {
    final gsBloc = context.read<SpatialAnalyticsV2Bloc>();

    return BlocBuilder<SpatialAnalyticsV2Bloc, SpatialAnalyticsV2State>(
      buildWhen: (_, state) => state is GSFilterChangedState,
      builder: (context, state) {
        return Row(
          children: [
            Expanded(
              child: Text(
                LocaleKeys.filters.tr(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            PrimarySvgIconButton(
              iconPath: GeoSpatialAssets.icUndo,
              backgroundColor: Colors.transparent,
              iconColor: AppColors.blueLightOld,
              enabled: gsBloc.canUndo,
              onTap: () => gsBloc.add(
                const GSFilterHistoryEvent(isUndo: true),
              ),
            ),
            const SizedBox(width: 8),
            PrimarySvgIconButton(
              iconPath: GeoSpatialAssets.icRedo,
              backgroundColor: Colors.transparent,
              iconColor: AppColors.blueLightOld,
              enabled: gsBloc.canRedo,
              onTap: () => gsBloc.add(
                const GSFilterHistoryEvent(isUndo: false),
              ),
            ),
            const SizedBox(width: 8),
            PrimarySvgIconButton(
              iconPath: GeoSpatialAssets.icReset,
              backgroundColor: Colors.transparent,
              iconColor: AppColors.blueLightOld,
              onTap: () => gsBloc.add(
                const GSFilterResetEvent(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNonRealEstateDomains() {
    return Column(
      children: [
        if (citizensController.isVisible)
          SearchableDropdown<GSCitizen>(
            key: const Key('citizenship-dropdown'),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            label: LocaleKeys.spatial_citizenship.tr(),
            controller: citizensController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (gendersController.isVisible)
          SearchableDropdown<GSGender>(
            key: const Key('gender-dropdown'),
            label: LocaleKeys.spatial_gender.tr(),
            controller: gendersController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (maritalController.isVisible)
          SearchableDropdown<GSMarital>(
            key: const Key('marital-dropdown'),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            label: LocaleKeys.spatial_maritalStatus.tr(),
            controller: maritalController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (attainmentController.isVisible)
          SearchableDropdown<GSAttainment>(
            key: const Key('attainment-dropdown'),
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
            label: LocaleKeys.spatial_education.tr(),
            controller: attainmentController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (householdsController.isVisible) ...[
          const Divider(height: 1, color: AppColors.greyShade1),
          SearchableDropdown<GSHousehold>(
            key: const Key('household-dropdown'),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            label: LocaleKeys.spatial_householdType.tr(),
            controller: householdsController,
            displayText: (value) => value.displayText ?? '',
          ),
        ],
      ],
    );
  }

  Widget _buildRealEstateDomain() {
    return Column(
      children: [
        if (buildingTypeController.isVisible)
          SearchableDropdown<GSBuildingsType>(
            key: const Key('buildings-type-dropdown'),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            label: LocaleKeys.spatial_buildingByType.tr(),
            controller: buildingTypeController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (buildingUseController.isVisible)
          SearchableDropdown<GSBuildingsUse>(
            key: const Key('buildings-use-dropdown'),
            label: LocaleKeys.spatial_buildingByUse.tr(),
            controller: buildingUseController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (unitsUseController.isVisible)
          SearchableDropdown<GSUnitsUse>(
            key: const Key('units-use-dropdown'),
            label: LocaleKeys.spatial_unitsByUse.tr(),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            controller: unitsUseController,
            displayText: (value) => value.displayText ?? '',
          ),
        if (unitsTypeController.isVisible)
          SearchableDropdown<GSUnitsType>(
            key: const Key('units-type-dropdown'),
            label: LocaleKeys.spatial_unitsByType.tr(),
            controller: unitsTypeController,
            displayText: (value) => value.displayText ?? '',
          ),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    disposeAllControllers();
  }
}
