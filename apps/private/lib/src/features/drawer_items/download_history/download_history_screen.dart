import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path/path.dart' as path;
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/common/widgets/appbar/common_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/utils/app_utils/app_permission.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/app_utils/file_utils.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class DownloadHistoryScreen extends StatefulWidget {
  const DownloadHistoryScreen({super.key});

  @override
  State<DownloadHistoryScreen> createState() => _DownloadHistoryScreenState();
}

class _DownloadHistoryScreenState extends State<DownloadHistoryScreen> {
  final isLightMode = HiveUtilsSettings.isLightMode;
  bool _isLoading = true;
  List<String> _downloadList = [];

  Future<void> _getFileHistory() async {
    final Directory directory = Directory(FileUtils.appDownloadsDirectoryPath);
    if (!directory.existsSync()) {
      directory.createSync(recursive: true);
    }
    final List<FileSystemEntity> entities = await directory.list(recursive: true).toList();
    _downloadList = entities.whereType<File>().map((e) => e.path).toList();

    _downloadList.sort((file1, file2) {
      return FileStat.statSync(file2).changed.compareTo(FileStat.statSync(file1).changed);
    });

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (Platform.isAndroid) {
        final bool hasPermission =
            await AppPermissions.checkPermissions(context, [AppPermission.manageExternalStorage]);
        if (!hasPermission) {
          if (mounted) {
            unawaited(context.maybePop());
          }
          return;
        }
      }

      await _getFileHistory();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            CommonAppBar(title: LocaleKeys.downloadHistory.tr()),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _downloadList.isEmpty
                      ? const Center(child: NoDataPlaceholder())
                      : ListView.builder(
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(16),
                          itemCount: _downloadList.length,
                          itemBuilder: _listItem,
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _listItem(BuildContext context, int index) {
    final String filePath = _downloadList[index];

    final String title = path.basename(filePath);
    final String downloadType =
        path.dirname(filePath).replaceFirst(FileUtils.appDownloadsDirectoryPath, '').replaceAll('/', '');
    final String dateTime = FileStat.statSync(filePath).changed.toString().formatDateTimeForChat();

    Color iconColor = Colors.grey;
    final String extension = path.extension(filePath).toUpperCase().replaceAll('.', '');

    switch (extension) {
      case 'PDF':
        iconColor = AppColors.redShade1;
      case 'DOC':
      case 'DOCX':
        iconColor = AppColors.blueShade39;
      case 'PNG':
        iconColor = AppColors.violet;
      case 'XLSX':
        iconColor = AppColors.green;
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        boxShadow: AppBox.shadow(),
      ),
      margin: const EdgeInsets.symmetric(vertical: 6),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            DownloadHelper.openFile(filePath);
          },
          borderRadius: BorderRadius.circular(10),
          child: Container(
            padding: const EdgeInsets.all(15),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: SizedBox(
                    height: 30,
                    width: 30,
                    child: Stack(
                      children: [
                        SvgPicture.asset(AppImages.icFilePlaceholder),
                        Positioned(
                          top: 14.5,
                          left: 2.5,
                          child: Container(
                            width: 22,
                            height: 11,
                            decoration: BoxDecoration(
                              color: iconColor,
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: Center(
                              child: Text(
                                extension,
                                style: const TextStyle(
                                  height: 0,
                                  fontSize: 6,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        title,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.s14w5cBlack.copyWith(
                          color: isLightMode ? AppColors.blueGreyShade1 : AppColors.white,
                        ),
                      ),
                      if (downloadType.isNotEmpty)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Tooltip(
                                message: downloadType,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color:
                                        (isLightMode ? AppColors.blueShade40 : AppColors.white).withValues(alpha: .14),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  margin: EdgeInsets.only(
                                    top: 8,
                                    left: HiveUtilsSettings.isLanguageArabic ? 8 : 0,
                                    right: HiveUtilsSettings.isLanguageEnglish ? 8 : 0,
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  child: Text(
                                    downloadType,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: AppTextStyles.s12w4cGreyShade4.copyWith(
                                      color: isLightMode ? AppColors.blueLightOld : AppColors.whiteShade2,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Flexible(
                              child: Text(
                                dateTime,
                                textDirection: TextDirection.ltr,
                                style: AppTextStyles.s12w4cGreyShade4,
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
