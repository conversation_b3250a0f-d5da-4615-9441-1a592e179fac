import 'package:get_it/get_it.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/repositories/indicator_card_repository_impl.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';
import 'package:scad_mobile/src/features/authentication/data/repositories/auth_repo_impl.dart';
import 'package:scad_mobile/src/features/authentication/domain/repositories/auth_repository_imports.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/logout/logout_bloc.dart';
import 'package:scad_mobile/src/features/authentication/presentation/bloc/reset_password/reset_password_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/repositories/chat_with_sme_repo_implementation.dart';
import 'package:scad_mobile/src/features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/details_page/common_features/insights/data/repositories/insights_repo_impl.dart';
import 'package:scad_mobile/src/features/details_page/common_features/insights/domain/repositories/insights_repository_imports.dart';
import 'package:scad_mobile/src/features/details_page/common_features/insights/presentation/bloc/insights_bloc.dart';
import 'package:scad_mobile/src/features/details_page/compare/data/repositories/compare_details_repo_impl.dart';
import 'package:scad_mobile/src/features/details_page/compare/domain/repositories/compare_details_repository.dart';
import 'package:scad_mobile/src/features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/repositories/compute_details_repo_impl.dart';
import 'package:scad_mobile/src/features/details_page/compute/domain/repositories/compute_details_repository.dart';
import 'package:scad_mobile/src/features/details_page/insights_discovery/data/repositories/insights_discovery_details_repo_impl.dart';
import 'package:scad_mobile/src/features/details_page/insights_discovery/domain/repositories/insights_discovery_details_repository_imports.dart';
import 'package:scad_mobile/src/features/details_page/insights_discovery/presentation/bloc/insights_discovery_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/official_experimental/presentation/bloc/official_experimental_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/bloc/scenario_forecast_details_bloc.dart';
import 'package:scad_mobile/src/features/domains/data/repositories/domains_repo_impl.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/repositories/about_repo_impl.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/data/repositories/feedback_repo_impl.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/domain/repositories/feedback_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/feedback/presentation/bloc/feedback_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/data/repositories/glossary_repo_implementation.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/domain/repositories/glossary_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/glossary/presentation/bloc/glossary_bloc.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/data/repositories/t_and_c_repo_impl.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/domain/repositories/t_and_c_repository_imports.dart';
import 'package:scad_mobile/src/features/drawer_items/terms_and_conditions/presentation/bloc/t_and_c_bloc.dart';
import 'package:scad_mobile/src/features/gen_ai/data/repositories/gen_ai_repository_impl.dart';
import 'package:scad_mobile/src/features/gen_ai/domain/repositories/gen_ai_repository.dart';
import 'package:scad_mobile/src/features/gen_ai/presentation/bloc/gen_ai_chat_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repo_impl.dart';
import 'package:scad_mobile/src/features/home/<USER>/repositories/home_repository_imports.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/data/repositories/myapps_repo_impl.dart';
import 'package:scad_mobile/src/features/my_apps/domain/repositories/myapps_repository_imports.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/notification/data/repositories/notifications_repo_impl.dart';
import 'package:scad_mobile/src/features/notification/domain/repositories/notification_repository_imports.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/onboarding/data/repositories/interest_domain_repo_impl.dart';
import 'package:scad_mobile/src/features/onboarding/domain/repositories/interest_domains_repository_imports.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/bloc/interest_domain_bloc.dart';
import 'package:scad_mobile/src/features/products/data/repositories/products_repo_impl.dart';
import 'package:scad_mobile/src/features/products/domain/repositories/products_repository_imports.dart';
import 'package:scad_mobile/src/features/products/presentation/bloc/products_bloc.dart';
import 'package:scad_mobile/src/features/search/data/repositories/search_repo_impl.dart';
import 'package:scad_mobile/src/features/search/domain/repositories/search_repository_imports.dart';
import 'package:scad_mobile/src/features/search/presentation/bloc/search_bloc.dart';
import 'package:scad_mobile/src/features/settings/data/repositories/language_update/language_update_repo_impl.dart';
import 'package:scad_mobile/src/features/settings/data/repositories/notification_setting/notification_setting_repo_impl.dart';
import 'package:scad_mobile/src/features/settings/data/repositories/password_reset/password_reset_repo_impl.dart';
import 'package:scad_mobile/src/features/settings/data/repositories/profile_update/profile_update_repo_impl.dart';
import 'package:scad_mobile/src/features/settings/data/repositories/settings/setting_repository_impl.dart';
import 'package:scad_mobile/src/features/settings/domain/repositories/language_update/language_update_repository_imports.dart';
import 'package:scad_mobile/src/features/settings/domain/repositories/notification_setting/notification_setting_repository_imports.dart';
import 'package:scad_mobile/src/features/settings/domain/repositories/password_reset/password_reset_repository_imports.dart';
import 'package:scad_mobile/src/features/settings/domain/repositories/profile_update/profile_update_repository_imports.dart';
import 'package:scad_mobile/src/features/settings/domain/repositories/settings/setting_repository_imports.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics/data/repositories/spatial_analytics_repo_impl.dart';
import 'package:scad_mobile/src/features/spatial_analytics/domain/repositories/spatial_analytics_imports.dart';
import 'package:scad_mobile/src/features/spatial_analytics/presentation/bloc/spatial_analytics_bloc.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/data/repositories/spatial_analytics_v2_repository_impl.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/domain/repositories/spatial_analytics_v2_repository.dart';
import 'package:scad_mobile/src/features/spatial_analytics_v2/presentation/bloc/spatial_analytics_v2_bloc.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

final servicelocator = GetIt.instance;

Future<void> initializeBlocs() async {
  servicelocator
    ///  Globally Shared
    ..registerSingleton<AppRouter>(AppRouter())
    ..registerLazySingleton<HttpServiceRequests>(HttpServiceRequests.new)

    /// Authentication
    ..registerFactory<AuthenticationBloc>(AuthenticationBloc.new)
    ..registerFactory<ResetPasswordBloc>(ResetPasswordBloc.new)
    ..registerLazySingleton<AuthRepository>(AuthRepositoryImpl.new)
    ..registerFactory<LogoutBloc>(LogoutBloc.new)

    ..registerFactory<DomainsBloc>(() => DomainsBloc())
    ..registerLazySingleton<DomainsRepository>(DomainsRepositoryImpl.new)
    ..registerFactory<SearchBloc>(() => SearchBloc())
    ..registerLazySingleton<SearchRepository>(SearchRepositoryImpl.new)
    ..registerFactory<SpatialAnalyticsBloc>(() => SpatialAnalyticsBloc())
    ..registerLazySingleton<SpatialAnalyticsRepository>(SpatialAnalyticsRepositoryImpl.new)
    ..registerFactory<ChatWithSmeBloc>(() => ChatWithSmeBloc())
    ..registerLazySingleton<ChatWithSmeRepository>(ChatWithSmeRepoImplementation.new)
    ..registerFactory<HomeBloc>(() => HomeBloc(servicelocator()))
    ..registerLazySingleton<HomeRepository>(HomeRepositoryImpl.new)
    ..registerFactory<IndicatorCardBloc>(IndicatorCardBloc.new)
    ..registerLazySingleton<IndicatorCardRepository>(IndicatorCardImpl.new)
    ..registerFactory<AboutAppBloc>(() => AboutAppBloc(servicelocator()))
    ..registerLazySingleton<AboutAppRepository>(AboutAppRepositoryImpl.new)
    ..registerFactory<TAndCBloc>(() => TAndCBloc(servicelocator()))
    ..registerLazySingleton<TAndCRepository>(TAndCRepositoryImpl.new)
    ..registerFactory<FeedbackBloc>(FeedbackBloc.new)
    ..registerLazySingleton<FeedbackRepository>(FeedbackRepositoryImpl.new)
    ..registerLazySingleton<PasswordResetRepository>(PasswordResetRepositoryImpl.new)
    ..registerFactory<InterestDomainBloc>(InterestDomainBloc.new)
    ..registerLazySingleton<InterestDomainRepository>(InterestDomainRepositoryImpl.new)
    ..registerFactory<SettingBloc>(SettingBloc.new)
    ..registerLazySingleton<NotificationSettingRepository>(NotificationSettingRepositoryImpl.new)
    ..registerFactory<ProductsBloc>(ProductsBloc.new)
    ..registerLazySingleton<ProductsRepository>(ProductsRepositoryImpl.new)
    ..registerFactory<GlossaryBloc>(() => GlossaryBloc(servicelocator()))
    ..registerLazySingleton<GlossaryRepository>(GlossaryRepoImplementation.new)
    ..registerFactory<MyAppsBloc>(MyAppsBloc.new)
    ..registerLazySingleton<MyAppsRepository>(MyAppsRepositoryImpl.new)
    ..registerLazySingleton<ProfileUpdateRepository>(NameUpdateRepositoryImpl.new)
    ..registerLazySingleton<LanguageUpdateRepository>(LanguageUpdateRepositoryImpl.new)
    ..registerFactory<ForgotPasswordBloc>(() => ForgotPasswordBloc(servicelocator()))
    ..registerFactory<LazyIndicatorListViewBloc>(LazyIndicatorListViewBloc.new)
    ..registerFactory<NotificationBloc>(NotificationBloc.new)
    ..registerLazySingleton<NotificationRepository>(NotificationRepositoryImpl.new)
    ..registerLazySingleton<SettingRepository>(SettingRepositoryImpl.new)

    ///  Gen AI Repositories
    ..registerFactory<GenAiChatBloc>(GenAiChatBloc.new)
    ..registerLazySingleton<GenAiRepository>(GenAiRepositoryImpl.new)

    ///  Details Page Bloc and Repositories
    ..registerFactory<InsightsDiscoveryDetailsBloc>(InsightsDiscoveryDetailsBloc.new)
    ..registerLazySingleton<InsightsDiscoveryDetailsRepository>(InsightsDiscoveryDetailsRepoImpl.new)
    ..registerFactory<ScenarioForecastDetailsBloc>(ScenarioForecastDetailsBloc.new)
    ..registerFactory<CompareDetailsBloc>(CompareDetailsBloc.new)
    ..registerLazySingleton<CompareDetailsRepository>(CompareDetailsRepoImpl.new)
    ..registerFactory<OfficialExperimentalDetailsBloc>(OfficialExperimentalDetailsBloc.new)
    ..registerLazySingleton<ComputeDetailsRepository>(ComputeDetailsRepositoryImpl.new)

    ///  Details page Insights
    ..registerFactory<InsightsBloc>(InsightsBloc.new)
    ..registerLazySingleton<InsightsRepository>(InsightsRepoImpl.new)

    ///  Geo Spatial V2
    ..registerFactory<SpatialAnalyticsV2Bloc>(SpatialAnalyticsV2Bloc.new)
    ..registerLazySingleton<SpatialAnalyticsV2Repository>(SpatialAnalyticsV2RepositoryImpl.new);
}
