import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AppSlidingTabItem {
  AppSlidingTabItem({
    required this.label,
    this.iconUrl,
    this.object,
    this.onTap,
    this.assetPath,
    this.iconBuilder,
  });

  final String label;
  final String? iconUrl;
  final dynamic object;
  final String? assetPath;
  final Future<void> Function()? onTap;
  final Widget Function(BuildContext, bool isSelected)? iconBuilder;
}

class AppSlidingTab extends StatefulWidget {
  const AppSlidingTab({
    required this.onTabChange,
    required this.tabs,
    super.key,
    this.initialTabIndex = 0,
    this.horizontalPadding = 16,
    this.tabController,
    this.pageController,
    this.userGuideStepsKeyList = const [],
    this.isScrollable = true,
    this.overrideTabOnTap = true,
  });

  final void Function(int index) onTabChange;
  final List<AppSlidingTabItem> tabs;
  final int initialTabIndex;
  final double horizontalPadding;
  final TabController? tabController;
  final PageController? pageController;
  final List<GlobalKey> userGuideStepsKeyList;
  final bool isScrollable;
  final bool overrideTabOnTap;

  @override
  State<AppSlidingTab> createState() => _AppSlidingTabState();
}

class _AppSlidingTabState extends State<AppSlidingTab> with TickerProviderStateMixin {
  late TabController tabController;
  ValueNotifier<int> currentIndex = ValueNotifier(0);

  final descriptions = {
    LocaleKeys.dashboards.tr(): LocaleKeys.tableauDashboardGuideDesc.tr(),
    LocaleKeys.publications.tr(): LocaleKeys.publicationsGuideDesc.tr(),
    LocaleKeys.webReports.tr(): LocaleKeys.webReportGuideDesc.tr(),
    LocaleKeys.scenarioDrivers.tr(): LocaleKeys.scenarioDriversGuideDesc.tr(),
    LocaleKeys.insightsDiscovery.tr(): LocaleKeys.insightsDiscoveryGuideDesc.tr(),
    LocaleKeys.forecast.tr(): LocaleKeys.forecastGuideDesc.tr(),
    'Forecasts': LocaleKeys.forecastGuideDesc.tr(),
    LocaleKeys.reports.tr(): LocaleKeys.reportsGuideDesc.tr(),
    'اكتشاف الرؤى': LocaleKeys.scenarioDriversGuideDesc.tr(),
    'محركات السيناريوهات': LocaleKeys.scenarioDriversGuideDesc.tr(),
    LocaleKeys.geoSpatial.tr(): LocaleKeys.geoSpatialGuideDesc.tr(),
    LocaleKeys.spatial_spatialStatistics.tr(): LocaleKeys.geoSpatialGuideDesc.tr(),
    LocaleKeys.newsletters.tr(): LocaleKeys.newslettersGuideDesc.tr(),
  };

  @override
  void initState() {
    super.initState();
    tabController = widget.tabController ??
        TabController(
          length: widget.tabs.length,
          vsync: this,
          initialIndex: widget.initialTabIndex,
        );
    currentIndex.value = widget.initialTabIndex;
    tabController.addListener(_onChange);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        widget.onTabChange(widget.initialTabIndex);
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
      }
    });
  }

  @override
  void dispose() {
    tabController.removeListener(_onChange);
    super.dispose();
  }

  void _onChange() {
    if ((HiveUtilsSettings.getUserGuideStatus() != UserGuides.Products) &&
        widget.tabs[tabController.index].onTap != null) {
      tabController.animateTo(currentIndex.value, duration: Duration.zero);
    } else {
      currentIndex.value = tabController.index;
    }
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final isLightMode = HiveUtilsSettings.isLightMode;

    return ValueListenableBuilder(
      valueListenable: currentIndex,
      builder: (context, b, c) {
        return TabBar(
          splashFactory: NoSplash.splashFactory,
          overlayColor: WidgetStateProperty.resolveWith(
            (states) {
              return states.contains(WidgetState.focused) ? null : Colors.transparent;
            },
          ),
          isScrollable: widget.isScrollable,
          tabAlignment: widget.isScrollable ? TabAlignment.start : null,
          labelPadding: EdgeInsets.zero,
          padding: EdgeInsets.zero,
          controller: tabController,
          onTap: (i) async {
            if (widget.overrideTabOnTap && widget.tabs[i].onTap != null) {
              unawaited(widget.tabs[i].onTap?.call());
            } else {
              tabController.animateTo(i);
              currentIndex.value = i;
              widget.onTabChange(i);
            }
          },
          dividerColor: Colors.transparent,
          indicator: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(30),
          ),
          tabs: List.generate(
            widget.tabs.length,
            (index) {
              return Container(
                padding: const EdgeInsets.only(left: 6),
                child: AnimatedContainer(
                  key: widget.userGuideStepsKeyList.isNotEmpty ? UniqueKey() : null,
                  margin: index == 0
                      ? (rtl
                          ? EdgeInsets.only(right: widget.horizontalPadding)
                          : EdgeInsets.only(left: widget.horizontalPadding))
                      : index == widget.tabs.length - 1
                          ? (rtl
                              ? EdgeInsets.only(left: widget.horizontalPadding)
                              : EdgeInsets.only(right: widget.horizontalPadding))
                          : const EdgeInsets.only(left: 2),
                  duration: Duration(
                    milliseconds: widget.userGuideStepsKeyList.isNotEmpty ? 50 : 200,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: currentIndex.value == index ? AppColors.blueLightOld : Colors.transparent,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14,
                    vertical: 6,
                  ),
                  child: widget.userGuideStepsKeyList.isNotEmpty
                      ? IntroWidget(
                          stepKey: widget.userGuideStepsKeyList[index],
                          // getDesiredKey(index, widget.userGuideStepsKeyList),
                          stepIndex: index + 1,
                          totalSteps: widget.userGuideStepsKeyList.length,
                          title: widget.tabs[index].label,
                          description: descriptions[widget.tabs[index].label] ?? '',
                          onNext: () {
                            widget.pageController?.jumpToPage(index + 1);
                            Future.delayed(const Duration(milliseconds: 500), () {
                              setState(() {});
                            });
                          },
                          onPrevious: () {
                            widget.pageController?.jumpToPage(index - 1);
                            Future.delayed(const Duration(milliseconds: 500), () {
                              setState(() {});
                            });
                          },
                          arrowAlignment: Alignment.topRight,
                          crossAxisAlignment: index == 0
                              ? CrossAxisAlignment.start
                              : index == widget.userGuideStepsKeyList.length - 1
                                  ? CrossAxisAlignment.end
                                  : CrossAxisAlignment.center,
                          targetBorderRadius: 50,
                          arrowPadding: EdgeInsets.only(
                            right: index >= 1 || index < widget.userGuideStepsKeyList.length - 1
                                ? MediaQuery.sizeOf(context).width * 0.08
                                : MediaQuery.sizeOf(context).width * 0.06,
                            left: MediaQuery.sizeOf(context).width * 0.16,
                            bottom: 10,
                          ),
                          targetPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          child: _buildTabTile(index, rtl, isLightMode),
                        )
                      : _buildTabTile(index, rtl, isLightMode),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTabTile(int index, bool rtl, bool isLightMode) {
    final item = widget.tabs.elementAt(index);
    final hasIcon = item.iconUrl != null || item.assetPath != null || item.iconBuilder != null;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (hasIcon) _buildIcon(index, item),
        Text(
          item.label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: currentIndex.value == index ? FontWeight.w500 : FontWeight.w400,
            color: currentIndex.value == index
                ? AppColors.white
                : isLightMode
                    ? AppColors.blueGreyShade2
                    : AppColors.greyShade4,
          ),
        ),
      ],
    );
  }

  Widget _buildIcon(int index, AppSlidingTabItem item) {
    final isRtl = DeviceType.isDirectionRTL(context);
    final isLightMode = HiveUtilsSettings.isLightMode;

    Widget? child;
    final iconBuilder = item.iconBuilder;
    if (iconBuilder != null) {
      final isSelected = currentIndex.value == index;
      child = iconBuilder(context, isSelected);
    }

    final url = item.iconUrl;
    if (url != null) {
      child = SvgPicture.network(
        item.iconUrl!,
        width: 18,
        height: 18,
        colorFilter: ColorFilter.mode(
          currentIndex.value == index
              ? AppColors.white
              : isLightMode
                  ? AppColors.blueTitleText
                  : AppColors.greyShade1,
          BlendMode.srcIn,
        ),
      );
    }

    final asset = item.assetPath;
    if (asset != null) {
      child = SvgPicture.asset(
        item.assetPath!,
        width: 18,
        height: 18,
        colorFilter: ColorFilter.mode(
          currentIndex.value == index
              ? AppColors.white
              : isLightMode
                  ? AppColors.blueTitleText
                  : AppColors.greyShade1,
          BlendMode.srcIn,
        ),
      );
    }

    if (child == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(
        right: isRtl ? 0 : 12,
        left: isRtl ? 12 : 0,
      ),
      child: child,
    );
  }
}
