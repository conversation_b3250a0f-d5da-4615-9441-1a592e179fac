part of '../app_drawer_part.dart';

final appDrawerController = AppDrawerController();

class AppDrawer extends StatefulWidget {
  const AppDrawer({
    required this.child,
    super.key,
    this.disabledGestures = true,
  });

  final Widget child;
  final bool disabledGestures;

  @override
  AppDrawerState createState() => AppDrawerState();
}

class AppDrawerState extends State<AppDrawer> with TickerProviderStateMixin {
  double openRatio = 0.68;
  double openScale = 0.68;
  Duration animationDuration = const Duration(milliseconds: 300);
  bool rtlOpening = false;

  AnimationController? _animationController;

  late Animation<double> _drawerScaleAnimation;
  late Animation<Offset> _childSlideAnimation;
  late Animation<double> _childScaleAnimation;
  late Animation<Decoration> _childDecorationAnimation;

  late double _offsetValue;
  late Offset _freshPosition;

  bool _captured = false;
  Offset _startPosition = Offset.zero;
  String? profileNetworkUrl;

  ValueNotifier<bool> isEnglish = ValueNotifier(true);
  final List<Locale> languages = [
    const Locale('en', ''),
    const Locale('ar', ''),
  ];
  final logoutBloc = LogoutBloc();

  @override
  void initState() {
    super.initState();

    openRatio = DeviceType.isTabDevice() ? 0.4 : 0.68;

    profileNetworkUrl ??= HiveUtilsSettings.getUsersProfilePic();
    _initControllers();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final oneTimeCheck = HiveUtilsSettings.box.get('one-time-check', defaultValue: null);
        if (oneTimeCheck == null) {
          context.read<SettingBloc>().add(
                LanguageUpdateEvent(
                  languageUpdateRequest: LanguageUpdateRequest(
                    name: HiveUtilsSettings.appLanguage,
                    deviceRegId: HiveUtilsPersistent.getDeviceToken(),
                  ),
                ),
              );
          context.read<SettingBloc>().add(const DefaultSettingLoadEvent());

          final int selected = languages.indexWhere(
            (element) => element.languageCode == context.locale.languageCode,
          );
          isEnglish.value = selected == 0;
          HiveUtilsSettings.box.put('one-time-check', 1);
        } else {
          isEnglish.value = HiveUtilsSettings.isLanguageEnglish;
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _animationController = null;
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AppDrawer oldWidget) {
    _initControllers();

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    rtlOpening = DeviceType.isDirectionRTL(context);

    return Material(
      color: isLightMode ? AppColors.blueShade13 : AppColors.drawerDark,
      child: Container(
        decoration: BoxDecoration(
          gradient: isLightMode
              ? const LinearGradient(
                  colors: [
                    AppColors.blueGradientShade3,
                    AppColors.blueGradientShade4,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              : const LinearGradient(
            colors: [
              AppColors.blueGradientShade3,
              AppColors.blueGradientShade4,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: GestureDetector(
          onHorizontalDragStart:
              widget.disabledGestures ? null : _handleDragStart,
          onHorizontalDragUpdate:
              widget.disabledGestures ? null : _handleDragUpdate,
          onHorizontalDragEnd:
              widget.disabledGestures ? null : _handleDragEnd,
          onHorizontalDragCancel:
              widget.disabledGestures ? null : _handleDragCancel,
          child: Stack(
            children: [
              // widget.backdrop,
              Align(
                alignment:
                    rtlOpening ? Alignment.centerRight : Alignment.centerLeft,
                child: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BlocConsumer<SettingBloc, SettingState>(
                        listener: (context, state) {
                          if (state is ProfilePicSuccessState) {
                            profileNetworkUrl = state.response.profilePic;
                          } else if (state is DefaultSettingSuccessState) {
                            profileNetworkUrl =
                                state.defaultSettingResponse.profilePic;
                          } else if (state is SettingSuccessState) {
                            final BuildContext ctx = servicelocator<AppRouter>()
                                .navigatorKey
                                .currentContext!;

                            ctx
                                .read<DomainsBloc>()
                                .add(const DomainsInitEvent());
                          }
                        },
                        builder: (context, state) {
                          return Container(
                            padding: const EdgeInsets.all(24),
                            child: IntrinsicHeight(
                              child: Row(
                                children: [
                                  Container(
                                    height: 60,
                                    width: 60,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                      color: AppColors.blueShade4,
                                      shape: BoxShape.circle,
                                    ),
                                    child: profileNetworkUrl != null &&
                                            profileNetworkUrl!.isNotEmpty
                                        ? ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(180),
                                            child: Image.network(
                                              profileNetworkUrl!,
                                              fit: BoxFit.cover,
                                              height: 50,
                                              width: 50,
                                            ),
                                          )
                                        : SvgPicture.asset(
                                            AppImages.icAvatarPlaceholderMale,
                                            height: 60,
                                            width: 60,
                                          ),
                                  ),
                                  const SizedBox(
                                    width: 16,
                                  ),
                                  Expanded(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        Text(
                                          HiveUtilsAuth.getUserName ?? '-',
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            color: AppColors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            height: 0,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          HiveUtilsAuth.getUserRole ?? '',
                                          style: const TextStyle(
                                            color: AppColors.white,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            height: 0,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  Material(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(30),
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(30),
                                      onTap: appDrawerController.hideDrawer,
                                      child: Container(
                                        width: 30,
                                        height: 30,
                                        margin: const EdgeInsets.all(8),
                                        padding: const EdgeInsets.all(7),
                                        decoration: BoxDecoration(
                                          color: isLightMode ? AppColors.white :AppColors.blueShade22,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        child: SvgPicture.asset(
                                          colorFilter:   isLightMode ? null :
                                          const ColorFilter.mode(
                                             AppColors.white,
                                            BlendMode.srcIn
                                          ),
                                          AppImages.icArrowsLeft,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                      Expanded(
                        child: FractionallySizedBox(
                          widthFactor: openRatio + .1,
                          child: ScaleTransition(
                            scale: _drawerScaleAnimation,
                            alignment: rtlOpening
                                ? Alignment.centerLeft
                                : Alignment.centerRight,
                            child: RepaintBoundary(
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.stretch,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        // child: widget.drawer,
                                        child: ListView(
                                          shrinkWrap: true,
                                          children: [
                                            _drawerItem(
                                              AppImages.icHome2,
                                              LocaleKeys.home.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).replaceAll(
                                                  [HomeNavigationRoute()],
                                                  updateExistingRoutes: false,
                                                ).then((value) {
                                                  context.read<HomeBloc>().add(NavToHomeEvent());
                                                });
                                              },
                                            ),
                                            if (!isDemoMode)
                                              _drawerItem(
                                                AppImages.icGlossary,
                                                LocaleKeys.glossary.tr(),
                                                isLightMode,
                                                onTap: () {
                                                  _removeFragmentRoutes();
                                                  AutoRouter.of(context).push(
                                                    GlossaryScreenRoute(),
                                                  );
                                                },
                                              ),
                                            if (!isDemoMode)
                                              _drawerItem(
                                                AppImages.icUserGuide,
                                                LocaleKeys.userGuide.tr(),
                                                isLightMode,
                                                onTap: () {
                                                  _removeFragmentRoutes();
                                                  AutoRouter.of(context).push(
                                                    const UserGuideScreenRoute(),
                                                  );
                                                },
                                              ),
                                            _drawerItem(
                                              AppImages.icDownload,
                                              LocaleKeys.downloadHistory.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(const DownloadHistoryScreenRoute());
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icFeedback,
                                              LocaleKeys.giveYourFeedback
                                                  .tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const FeedbackScreenRoute(),
                                                );
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icTerms,
                                              LocaleKeys.termsAndConditions
                                                  .tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  TermsAndConditionsScreenRoute(),
                                                );
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icContactUs,
                                              LocaleKeys.contactSCAD.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const ContactUsScreenRoute(),
                                                );
                                              },
                                            ),
                                            _drawerItem(
                                              AppImages.icDevice,
                                              LocaleKeys.aboutThisApp.tr(),
                                              isLightMode,
                                              onTap: () {
                                                _removeFragmentRoutes();
                                                AutoRouter.of(context).push(
                                                  const AboutThisAppScreenRoute(),
                                                );
                                              },
                                            ),
                                            const SizedBox(height: 12),
                                            themeSwitch(),
                                            const SizedBox(height: 20),
                                            languageSwitch(context),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      bottom: 20,
                                    ),
                                    child: IntrinsicHeight(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              onTap: () async {
                                                appDrawerController
                                                    .hideDrawer();
                                                await Future.delayed(
                                                    const Duration(
                                                      milliseconds: 350,
                                                    ), () {
                                                  _removeFragmentRoutes();
                                                  AutoRouter.of(
                                                    context,
                                                  ).push(
                                                    const SettingsScreenRoute(),
                                                  );
                                                });
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(12),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .center,
                                                  children: [
                                                    SvgPicture.asset(
                                                      AppImages.icSettings,
                                                      width: 30,
                                                      height: 30,
                                                      colorFilter:
                                                          const ColorFilter.mode(
                                                            AppColors
                                                                .white,
                                                        BlendMode.srcIn,
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width: 10,
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                        LocaleKeys.settings
                                                            .tr(),
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: const TextStyle(
                                                          color:
                                                              AppColors.white,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          height: 0,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          const VerticalDivider(
                                            width: 1,
                                            indent: 10,
                                            endIndent: 10,
                                            color: Color(0xff99BBE2),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              onTap: () {
                                                appDrawerController
                                                    .hideDrawer();
                                                Future.delayed(
                                                    const Duration(
                                                      milliseconds: 350,
                                                    ), () {
                                                  logoutBloc
                                                      .add(OnLogoutEvent());
                                                  HiveUtilsAuth.logout();
                                                });
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(12),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .center,
                                                  children: [
                                                    RotatedBox(
                                                      quarterTurns: DeviceType
                                                              .isDirectionRTL(
                                                        context,
                                                      )
                                                          ? 2
                                                          : 0,
                                                      child: SvgPicture.asset(
                                                        AppImages.icLogOut,
                                                        width: 25,
                                                        height: 25,
                                                        colorFilter:
                                                            const ColorFilter.mode(
                                                              AppColors
                                                                  .white,
                                                          BlendMode.srcIn,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width: 10,
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                        LocaleKeys.logout
                                                            .tr(),
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: const TextStyle(
                                                          color:
                                                              AppColors.white,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          height: 0,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SlideTransition(
                position: _childSlideAnimation,
                textDirection:
                    rtlOpening ? TextDirection.rtl : TextDirection.ltr,
                child: ScaleTransition(
                  scale: _childScaleAnimation,
                  child: Builder(
                    builder: (_) {
                      final childStack = Stack(
                        children: [
                          RepaintBoundary(
                            child: widget.child,
                          ),
                          ValueListenableBuilder<DrawerState>(
                            valueListenable: appDrawerController,
                            builder: (_, value, __) {
                              if (!value.visible) {
                                return const SizedBox();
                              }

                              return Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: appDrawerController.hideDrawer,
                                  highlightColor: Colors.transparent,
                                  child: Container(),
                                ),
                              );
                            },
                          ),
                        ],
                      );

                      return AnimatedBuilder(
                        animation: _childDecorationAnimation,
                        builder: (_, child) {
                          return Container(
                            clipBehavior: Clip.antiAlias,
                            decoration: _childDecorationAnimation.value,
                            child: child,
                          );
                        },
                        child: childStack,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget themeSwitch() {
    final isLightMode = HiveUtilsSettings.isLightMode;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 26),
      child: Row(
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(50),
            onTap: () async {
              final nextThemeMode = HiveUtilsSettings.isLightMode ? ThemeMode.dark : ThemeMode.light;
              await HiveUtilsSettings.setThemeMode(nextThemeMode);

              unawaited(
                AutoRouter.of(context).replaceAll([
                  HomeNavigationRoute(
                    key: Key(HiveUtilsSettings.boxStatus()),
                  ),
                ]),
              );
            },
            child: Container(
              width: 121,
              height: 41,
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: isLightMode ? AppColors.white : AppColors.blueTitleText,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x146F7A9C),
                    blurRadius: 50,
                    offset: Offset(0, 35),
                    spreadRadius: -23,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Positioned(
                    left: 5,
                    top: 3,
                    child: Container(
                      width: 56,
                      height: 35,
                      decoration: ShapeDecoration(
                        color: isLightMode
                            ? AppColors.blueLight
                            : Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: Center(
                        child: FittedBox(
                          child: SizedBox(
                            height: 22,
                            width: 22,
                            child: SvgPicture.asset(
                              AppImages.icLightOutline,
                              colorFilter: const ColorFilter.mode(
                                AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 61,
                    top: 2,
                    child: Container(
                      width: 58,
                      height: 37,
                      decoration: ShapeDecoration(
                        color: isLightMode
                            ? Colors.transparent
                            : const Color(0xFF2587FC),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: Center(
                        child: FittedBox(
                          child: SizedBox(
                            height: 22,
                            width: 22,
                            child: SvgPicture.asset(
                              AppImages.icDarkOutline,
                              colorFilter: ColorFilter.mode(
                                (isLightMode
                                    ? AppColors.grey
                                    : AppColors.white),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isSwitchingLanguage = false;

  Widget languageSwitch(BuildContext context) {
    if (isDemoMode) return const SizedBox();

    final isLightMode = HiveUtilsSettings.isLightMode;

    return ValueListenableBuilder(
      valueListenable: isEnglish,
      builder: (context, b, w) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 26),
          child: Row(
            children: [
              InkWell(
                borderRadius: BorderRadius.circular(50),
                onTap: () async {
                  if (_isSwitchingLanguage) return;

                  final BuildContext ctx =
                      servicelocator<AppRouter>().navigatorKey.currentContext!;
                  _isSwitchingLanguage = true;
                  try {
                    // isEnglish.value = !isEnglish.value;

                    ctx.read<SettingBloc>().add(
                          LanguageUpdateEvent(
                            languageUpdateRequest: LanguageUpdateRequest(
                              name: languages[!isEnglish.value ? 0 : 1]
                                  .languageCode,
                              deviceRegId: HiveUtilsPersistent.getDeviceToken(),
                            ),
                          ),
                        );
                    ctx.read<DomainsBloc>().add(const DomainsInitEvent());
                  } catch (e, s) {
                    Completer<dynamic>().completeError(e, s);
                  }
                },
                child: Container(
                  width: 121,
                  height: 41,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color:
                        isLightMode ? AppColors.white : AppColors.blueTitleText,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x146F7A9C),
                        blurRadius: 50,
                        offset: Offset(0, 35),
                        spreadRadius: -23,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        left: 5,
                        top: 3,
                        child: Container(
                          width: 56,
                          height: 35,
                          decoration: ShapeDecoration(
                            color: isEnglish.value
                                ? isLightMode
                                    ? AppColors.blueLight
                                    : const Color(0xFF2587FC)
                                : Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: Center(
                            child: FittedBox(
                              child: SizedBox(
                                height: 12,
                                width: 18,
                                child: SvgPicture.asset(
                                  AppImages.icEnAlt,
                                  colorFilter: ColorFilter.mode(
                                    isEnglish.value
                                        ? AppColors.white
                                        : (isLightMode
                                            ? AppColors.grey
                                            : AppColors.white),
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        left: 61,
                        top: 2,
                        child: Container(
                          width: 58,
                          height: 37,
                          decoration: ShapeDecoration(
                            color: isEnglish.value
                                ? Colors.transparent
                                : isLightMode
                                    ? AppColors.blueLight
                                    : const Color(0xFF2587FC),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: Center(
                            child: FittedBox(
                              child: SizedBox(
                                height: 22,
                                width: 14,
                                child: SvgPicture.asset(
                                  AppImages.icArAlt,
                                  colorFilter: ColorFilter.mode(
                                    isEnglish.value
                                        ? (isLightMode
                                            ? AppColors.grey
                                            : AppColors.white)
                                        : AppColors.white,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // return Row(
    //   children: [
    //     Padding(
    //       padding: const EdgeInsets.all(26),
    //       child: AppDrawerSwitch(
    //         value: HiveUtilsSettings.getAppLanguage() == 'en',
    //         textOn: '',
    //         textOff: '',
    //         iconOn: Container(
    //           width: 60,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: SvgPicture.asset(
    //             AppImages.icEn,
    //             colorFilter: ColorFilter.mode(
    //               AppColors.white,
    //               BlendMode.srcIn,
    //             ),
    //           ),
    //         ),
    //         iconOff: Container(
    //           width: 60,
    //           height: 36,
    //           padding: const EdgeInsets.all(9),
    //           decoration: const ShapeDecoration(
    //             color: Color(0xFF2587FC),
    //             shape: OvalBorder(),
    //           ),
    //           child: SvgPicture.asset(
    //             AppImages.icAr,
    //             colorFilter: ColorFilter.mode(
    //               AppColors.white,
    //               BlendMode.srcIn,
    //             ),
    //           ),
    //         ),
    //         onTap: (bool isEnglish) async {
    //           if (mounted) {
    //             await context.setLocale(languages[isEnglish ? 0 : 1]);
    //             // appDrawerController.hideDrawer();
    //             context.read<SettingBloc>().add(
    //                   LanguageUpdateEvent(
    //                     languageUpdateRequest: LanguageUpdateRequest(
    //                       name: languages[isEnglish ? 0 : 1].languageCode,
    //                       deviceRegId: HiveUtilsPersistent.getDeviceToken(),
    //                     ),
    //                   ),
    //                 );
    //             context.read<DomainsBloc>().add(const DomainsInitEvent());
    //           }
    //         },
    //       ),
    //     ),
    //   ],
    // );
  }

  void _initControllers() {
    appDrawerController
      ..removeListener(_handleControllerChanged)
      ..addListener(_handleControllerChanged);
    try {
      _animationController = AnimationController(
        vsync: this,
        value: appDrawerController.value.visible ? 1 : 0,
      );

      _animationController?.reverseDuration =
          _animationController?.duration = animationDuration;
    } catch (e) {
      // error
    }

    final parentAnimation = CurvedAnimation(
      curve: Curves.easeInOut,
      parent: _animationController!,
    );

    _drawerScaleAnimation = Tween<double>(
      begin: 0.75,
      end: 1,
    ).animate(parentAnimation);

    _childSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(openRatio, 0),
    ).animate(parentAnimation);

    _childScaleAnimation = Tween<double>(
      begin: 1,
      end: openScale,
    ).animate(parentAnimation);

    _childDecorationAnimation = DecorationTween(
      begin: const BoxDecoration(),
      end: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(16)),
        //   boxShadow: [
        //   BoxShadow(
        //     color: Colors.red.withValues(alpha: 0.01),
        //     spreadRadius: 1,
        //     blurRadius: 0.1,
        //     offset: Offset(-20, 10), // changes position of shadow
        //   ),
        // ],
      ),
    ).animate(parentAnimation);
  }

  void _handleControllerChanged() {
    if (_animationController == null) return;
    appDrawerController.value.visible
        ? _animationController?.forward()
        : _animationController?.reverse();
  }

  void _handleDragStart(DragStartDetails details) {
    _captured = true;
    _startPosition = details.globalPosition;
    _offsetValue = _animationController!.value;
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_captured) return;

    final screenSize = MediaQuery.of(context).size;

    _freshPosition = details.globalPosition;

    final diff = (_freshPosition - _startPosition).dx;

    _animationController!.value = _offsetValue +
        (diff / (screenSize.width * openRatio)) * (rtlOpening ? -1 : 1);
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_captured) return;

    _captured = false;

    if (_animationController!.value >= 0.5) {
      if (appDrawerController.value.visible) {
        _animationController!.forward();
      } else {
        appDrawerController.showDrawer();
      }
    } else {
      if (!appDrawerController.value.visible) {
        _animationController!.reverse();
      } else {
        appDrawerController.hideDrawer();
      }
    }
  }

  void _handleDragCancel() {
    _captured = false;
  }

  Widget _drawerItem(
    String icon,
    String title,
    bool isLightMode, {
    VoidCallback? onTap,
  }) {
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: () async {
        appDrawerController.hideDrawer();
        await Future.delayed(
          const Duration(
            milliseconds: 350,
          ),
          onTap,
        );
      },
      child: Container(
        margin: const EdgeInsets.all(24),
        width: double.maxFinite,
        height: 25,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              width: 25,
              height: 25,
              colorFilter: const ColorFilter.mode(
                AppColors.white,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 10),
            Flexible(
              child: Text(
                title,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: isLightMode ? AppColors.white : AppColors.greyShade8,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  height: 0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _removeFragmentRoutes() {
    AutoRouter.of(context).removeWhere(
      (route) =>
          route.name == GlossaryScreenRoute.name ||
          route.name == UserGuideScreenRoute.name ||
          route.name == FeedbackScreenRoute.name ||
          route.name == TermsAndConditionsScreenRoute.name ||
          route.name == ContactUsScreenRoute.name ||
          route.name == AboutThisAppScreenRoute.name ||
          route.name == SettingsScreenRoute.name ||
          route.name == DownloadHistoryScreenRoute.name,
    );

    if(![SettingsScreenRoute.name, AppUpdateScreenRoute.name].contains(context.router.stack.last.name)) {
      AppUpdate.check(context);
    }
  }
}
