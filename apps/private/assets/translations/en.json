{"all": "All", "clearAll": "Clear All", "language": "Language", "recentSearches": "Recent Searches", "searchHere": "Search", "suggestedIndicators": "Suggested Indicators", "password": "Password", "searchResults": "Search Results", "settings": "Settings", "notification": "Notification", "keyIndicators": "Key Indicators", "keyFiguresAtAGlance": "Key Figures at a glance", "statisticalDomains": "Domains", "somethingWentWrong": "Something went wrong", "chatThreads": "Chat threads", "mostPopularFAQ": "Most popular FAQ’s", "domains": "Domains", "general": "General", "selectDomain": "Select Domain", "noDataAvailable": "No data available!", "noData": "No Data Found", "send": "Send", "enter": "Enter", "forgotPassword": "Forgot Password", "emailId": "Email ID", "hamburgerMenu": "<PERSON><PERSON>", "hamburgerMenuDesc": "Your one-stop source for help and app-relevant info.", "recommendedForYou": "For You", "recommendedForYouDesc": "Discover your recommended domains, dynamically syncing to the latest updates from your preferred sources.", "addIcons": "Add Icons", "addIconsDesc": "Tap the notification bell and the plus sign to get all notifications for this indicator and to add it to your personalized set.", "expand": "Expand", "expandDesc": "Expand for comprehensive details about the indicator, where you can apply various filters, and download data in both PDF and Excel formats.", "backToUserGuide": "User guide", "of": "of", "steps": "steps", "next": "Next", "search": "Search", "forecast": "Forecast", "scenarioDrivers": "Scenario Drivers", "downloadAs": "Download As", "changeDataFrequency": "Change Data Frequency", "changeDataPresentation": "Change Data Presentation", "compareIndicators": "Compare Indicators", "computeData": "Compute Data", "termsAndConditions": "Terms & Conditions", "iAgreeTo": "I agree to", "updatedOn": "Updated on:", "source": "Source:", "glossary": "Glossary", "nationalAccounts": "National Account", "whatIfScenario": "What-if Sc<PERSON><PERSON><PERSON>", "changeDrivers": "Change Drivers", "back": "Back", "filters": "Filters", "searchApps": "Search Apps", "addApps": "Add Apps", "myApps": "My Apps", "youDontHaveAnyAppsAdded": "You don't have any apps added", "chooseYourDomains": "Choose your domains", "createStartNewCollection": "Create/ Start New Collection", "saveTo": "Save to", "done": "Done", "startANewChat": "Start a new chat", "startAddingApps": "Start adding your apps! Click on the + button.", "youDontHaveAnyConversationsStartedYet": "You haven’t initiated any conversations yet", "askUs": "Ask Us", "contactSCAD": "Contact Us", "whatINeedToHighlight": "What I need to highlight:", "whatIsItRegarding": "What is it regarding?", "pleaseExplain": "Please Explain", "filePickerPlaceholder": "JPEG, PNG Files Lesser than 500kb", "attachments": "Attachments", "submit": "Submit", "feedback": "<PERSON><PERSON><PERSON>", "feedbackOrComments": "Feedback/ Comments (Optional)", "feedbackSuccess": "Your feedback has been submitted successfully", "feedbackSuccessThanks": "Thank you so much for taking the time to give your valuable feedback!", "backToHome": "Back to Home", "retypePassword": "Confirm Password", "currentPassword": "Current Password", "newPassword": "New Password", "cancel": "Cancel", "inAppNotifications": "In-app Notifications", "notificationByEmail": "Email Notifications", "notifications": "Notifications", "clear": "Clear", "disseminationProducts": "Products", "signedInAs": "Signed in as", "submitANewQuestion": "Submit a new question", "questionThreads": "Question threads", "ticketID": "Ticket ID", "you": "You", "SME": "SME", "reference": "Reference", "changeDomain": "Change Domain", "changeTheme": "Change Theme", "changeSubTheme": "Change Sub Theme", "addSubjectLine": "Add subject line", "selectCategoryYouWantToTalkAbout": "Select Category you want to talk about", "submitYourQuery": "Start your Query", "select": "Select", "download": "Download", "loginTagLine": "Driven by data for a better tomorrow", "loginWithEmail": "Login with Your Official Email ID", "governmentEmail": "Email", "forgotPasswordQue": "Forgot Password?", "privacyPolicy": "Privacy Policy", "otherLoginOptions": "Other login options", "login": "Log in", "chooseYourInterests": "Choose your interests", "interestDesc": "Select statistical domains and get tailored results that match your preferences.", "userGuide": "User Guide", "giveYourFeedback": "<PERSON><PERSON><PERSON>", "termsOfUseEMPPrivacyPolicy": "Terms of Use & Privacy Policy", "aboutThisApp": "About This App", "dark": "Dark", "light": "Light", "skip": "<PERSON><PERSON>", "continueButton": "Continue", "logout": "Logout", "newCollection": "New Collection", "myDashboards": "My Dashboards", "products": "Products", "home": "Home", "textSize": "Text Size", "myDashboard": "My Dashboard", "lineChart": "Line Chart", "barChart": "Bar Chart", "tableView": "Table View", "treeMap": "Tree Map", "summation": "Summation", "subtraction": "Subtraction", "multiplication": "Multiplication", "division": "Division", "compute": "Compute", "apply": "Apply", "areYouSure": "Are you sure?", "clearDataWarning": "Once the data is cleared, it cannot be undone. Do you really want to continue?", "proceed": "Proceed", "downloadPDF": "Download PDF", "downloadImage": "Download Image", "modifyDrivers": "Modify any drivers below to view changes in the projection", "continueAction": "Continue", "selectDomains": "Please select minimum 3 domains", "exploreNow": "Explore Now", "compare": "Compare", "updateCollection": "Update collection", "save": "Save", "update": "Update", "collectionUpdated": "Collection updated successfully", "collectionCreated": "Collection created successfully", "enterCollectionName": "Enter Collection Name", "duplicateCollectionName": "New collection name is same as the previous name", "pleaseWait": "Please wait while loading the collections", "user": "User", "noDataFound": "No Data Found", "inputOfficialEmail": "Please input your official email id to login", "inputPassword": "Please input password", "errorLoadingData": "Error loading data. Please try again.", "forYou": "For You", "edit": "Edit", "delete": "Delete", "passwordReset": "Password Reset", "passwordResetSuccess": "Password reset successfully. Please login using your new password", "statisticsInterest": "Statistics you’re interested in", "selectSuggestion": "Please select a suggestion to highlight", "selectTopic": "Please select a topic", "enterExplanation": "Please enter an explanation", "selectFileSize": "Please select a file size less than 500kb", "writeFeedback": "Feel free to share your thoughts with us (optional).", "unitOfMeasurement": "Unit of measurement", "enterHere": "Enter here", "enterNewIndicatorName": "Enter name of new computed indicator", "selectOtherIndicators": "Select other indicators", "setUpPassword": "Set Up Password", "setUpPasswordDesc": "To set up your new password, enter and re-enter your password and add the 6-digit OTP sent to your registered email address.", "forgotPasswordDesc": "Enter your registered email address below to reset your password and receive the 6-digit OTP to complete setting up your changed password.", "askForHelp": "How can we help you today?", "metaData": "<PERSON><PERSON><PERSON>", "reset": "Reset", "reload": "Reload", "computation": "Computation", "selectFirstIndicator": "Select first indicator", "invalidEmail": "Invalid email", "create": "Create", "resendOTP": "Resend OTP", "enterTheOtpSentTo": "Enter the OTP sent to", "reEnterPassword": "Re-Enter Password", "passwordAlreadySet": "Password has already been set. Proceed with login", "otpSentForPasswordSetting": "O<PERSON> has been sent to your email for setting the password", "upTo": "Up to", "downTo": "Down to", "takeATour": "Explore", "dashboards": "Dashboards", "onboarding1": "Welcome to Bayaan, where data meets discovery! Explore countless statistical indicators, Uncover insights, analyze trends, and utilize powerful tools at your fingertips.", "onboarding2": "Discover different areas like economy, population, and more. Find various indicators easily. Get insights tailored to your interests.", "onboarding3": "Your personalized hub for statistical exploration! Here, you can create custom folders to organize your favorite indicators for easy access. ", "onboarding4": "Your hub for statistical tools. Discover Dashboard, Scenario Drivers, Web Reports, Publications, GeoSpatial, and more. From trend analysis to geographic insights, unlock the power of data effortlessly.", "contactUsSuccess": "Your request has been submitted successfully", "indicators": "Indicators", "publications": "Publications", "downloading": "Downloading", "webReports": "Web Reports", "insightsDiscovery": "Insights Discovery", "reports": "Reports", "geoSpatial": "Geo Spatial", "forecasts": "Forecasts", "fileDownloaded": "File Downloaded", "fileIsSavedTo": "File is saved to:", "sensitiveInformation": "Sensitive Information: Data is available utmost confidentiality.", "copyright": "Copyright ©2023 Statistics Center Abu Dhabi. All Rights Reserved", "compareIndicatorsResult": "Compare Indicators - Result", "awaitingResponseFromSme": "Awaiting response from SME", "chatEnded": "<PERSON><PERSON> Ended", "enterYourFeedback": "Enter your feedback", "feedbackFieldError": "Help us improve! Share your feedback with us so we can better serve you.", "seeMore": "See More", "seeLess": "See Less", "collectionError": "Collection with the same name is already existing", "emptyCollectionNameError": "Please enter collection name", "computeValidation": "Enter name of new computed indicator", "inactiveProfileStatusMessage": "You account is currently Inactive, please contact administrator", "chatThreadsGuideDesc": "View all the chat threads to engage in conversations, respond to messages, move to the chat detail page and easily keep track of the flow of communication.", "startChatGuideDesc": "To initiate a new chat, click here and provide details like domain, theme, sub-theme, and subject line on the screen that pops up.", "mostPopularFAQGuideDesc": "Explore the top FAQs based on user interest and discover the key information at a glance. FAQs are categorised into general and domain-specific, and you can switch between them to find the most popular questions.", "addAppsGuideDesc": "Search for indicators or choose the domains to navigate to the detail page of the list of indicators.", "createNewCollection": "Create New Collection", "createNewCollectionGuideDesc": "Enter and save the new collection name to create a new collection and add indicators according to your choice.", "changeDataFrequencyGuideDesc": "You can choose between 'Annually' or 'Quarterly' to change how the data is shown over time. Based on the chosen data frequency, you can explore data trends over different time intervals with flexibility in analyzing patterns.", "changeDataPresentationGuideDesc": "Customize the visual representation of data to a suitable view based on your analytical preferences. You can switch between various presentation formats: Bar Chart, Line Chart, and Table View.", "compareDataGuideDesc": "Assess the performance of one indicator to another by comparing two indicators within the same domain. Draw correlations, identify trends, and comprehensively understand how multiple indicators interact within a specified context.", "computeDataGuideDesc": "Unlock data manipulation tools by performing calculations such as summation, subtraction, multiplication, and division on selected indicators.", "chatOption": "Chat Option", "chatOptionGuideDesc": "Navigate effortlessly to the chat screen to engage in real-time interactions.", "downloadAsGuideDesc": "Export the displayed data in PDF, XLS and image formats. Choose the format that best suits your sharing, presenting or further analysis requirements.", "compareIndicatorLabel": "Enter name for new compared indicator", "compareIndicatorError": "Please enter name for new compared indicator", "searchQueryError": "Kindly provide a search query to receive results", "indicatorComparisonError": "Indicator comparison failed!", "globalSearch": "Global Search", "globalSearchGuideDesc": "View the search results for any information in the application.", "notificationsGuideDesc": "View all the subscribed notifications in the application.", "tableauDashboardGuideDesc": "Dashboard is used for summarizing key insights and information regarding a specific topic in both light and dark themes across English and Arabic.", "webReportGuideDesc": "Web Report is an interactive record presented through the Web. It is embedded in SCAD website which is related to each sub-domain in the Domain Statistics.", "insightsDiscoveryGuideDesc": "Insights Discovery is an analytical app that allows the user to discover insights by dissecting the trend lines across different dimensions.", "scenarioDriversGuideDesc": "Scenario Driver is an analytical app which gives end users new insights from traditional data and the ability to change a number of variables that have been determined to have an impact on the chosen indicator.", "forecastGuideDesc": "Forecast Indicators are developed via Data Science where the driver variables have been identified, and the size of their impact calculated on a chosen indicator.", "reportsGuideDesc": "\"Bayaan Reports\" is a comprehensive feature that generates in-depth analyses and visual summaries based on the integrated data. These reports offer valuable insights, trends, and simulations, aiding decision-makers in policy formulation, resource allocation, and strategy development.", "publicationsGuideDesc": "Publications are PDF files made to communicate with the public that can be downloaded by the end user.", "geoSpatialGuideDesc": "This is a custom product for translating key geospatial insights to the end user.", "indicatorNameMetaInfo": "Indicator name", "indicatorDescriptionMetaInfo": "Indicator description", "dataSourcesMetaInfo": "Data sources", "statisticalCalculationMethodMetaInfo": "Statistical calculation method", "unknownErrorOccurred": "Unknown error occurred", "read": "Read", "unread": "Unread", "last10Days": "Last 10 days", "lastMonth": "Last month", "sameIndicatorComparisonError": "This indicator has already been chosen for comparison", "filterEmptyMessage": "Data not available in the selected filter combination", "termsAndConditionsWarning": "Kindly acknowledge to the terms & conditions below", "image": "Image", "pdf": "PDF", "excel": "Excel", "helpUsHint": "Help us to improve", "experimentalStatistics": "Experimental Statistics", "themeRequired": "Theme is required!", "domainRequired": "Domain is required!", "subThemeRequired": "Sub theme is required!", "subjectRequired": "Subject line is required!", "officialStatistics": "Official Statistics", "INDICATOR_ID": "Indicator Id", "RUN_SEQ_ID": "Run Sequence Id", "RUN_DT": "Run Date", "LABEL": "Label", "VALUE": "Value", "VALUE_LL": "Value Lower Limit", "VALUE_UL": "Value Upper Limit", "UNIT": "Unit", "OBS_DT": "Observation Date", "OPT": "Forecast Flag", "TYPE": "Type", "OIL_NONOIL": "Category", "SECTOR": "Sector", "INDUSTRY": "Industry", "PARAMETER_COMBO_ID": "Parameter Combo Id", "SECTOR_AR": "Sector Arabic", "VALUE_FORECAST": "Forecasted Value", "OBS_DT_CUR": "Real Observation Date", "VALUE_PERC_ECO": "Value Percentage", "VALUE_CURRENT": "Real Value", "CHANGE": "Change", "CHANGE_PY": "Change Percentage", "LANGUAGE_CD": "Language Type", "uaePassButtonText": "Continue with UAE PASS", "feedbackSubmittedSuccessfully": "<PERSON><PERSON><PERSON> submitted successfully", "changeProfilePicture": "Change Profile Picture", "deleteCurrentPicture": "Delete current picture", "showLess": "Show less", "showMore": "Show more", "permissionDenied": "Permission Denied", "openAppSettingsForPermission": "Some of the app permissions are not granted. Please grant access in the app settings.", "scenarioDriver": "Scenario Drivers", "forcastHome": "Forecasts", "insightsDiscoverys": "Insights Discovery", "emirati": "Emirati", "nonEmirati": "Non-Emirati", "totalPopulation": "Total Population", "byNationality": "By Nationality", "byGender": "By Gender", "gender": "Gender", "nationality": "Nationality", "byNationals": "By Nationals", "byDistricts": "By Districts", "genderMale": "Male", "genderFemale": "Female", "populationOverview": "Population Overview", "householdPopulation": "Household Population", "populationDotRatio": "1 Dot = 100 People", "pointOfInterest": "Point of Interest", "distanceMeasurement": "Distance Measurement", "areaMeasurement": "Area Measurement", "distance": "Distance", "theme": "Theme", "subTheme": "Sub Theme", "methodology": "Methodology", "district": "District", "population": "Population", "area": "Area", "byEthnicity": "By Ethnicity", "byBedrooms": "By Bedrooms", "realEstate": "Real Estate", "flatTransactions": "Flat Transactions", "monthWiseAvgSum": "Month Wise Avg/Sum", "perimeter": "Perimeter", "measurement": "Measurement", "newMeasurement": "New Measurement", "smallAreaSelection": "Small Area Selection", "sumSellingPrice": "<PERSON><PERSON>", "avgSellingPrice": "Avg <PERSON>", "spatialAnalysis": "Spatial Analysis", "bySpecialization": "By Specialization", "byJobVsVacancies": "By Job vs Vacancies", "byVacancies": "By Vacancies", "workExperience": "Work Experience", "jobSeekers": "Job Seekers", "experience": "Experience", "totalNumbers": "Total Numbers", "averageSale": "Average Sale", "transactionsValue": "Transactions Value", "jobSeekersByYearsOfExperience": "Job Seekers - By Years of Experience", "jobVacanciesByHighDegreeSpecialization": "Job Vacancies - By High Degree Specialization", "jobSeekersVsJobVacancies": "Job Seekers vs Job Vacancies", "jobSeekersHighDegreeSpecialization": "Job Seekers - High Degree Specialization", "averageSellingPriceByBedroomType": "Average Selling Price by Bedroom Type", "monthlyAverageOrSumOfTransactions": "Monthly Average/Sum of Transactions", "averageSaleByDistrict": "Average Sale by District", "abuDhabi": "Abu Dhabi", "experimental": "Experimental", "experimentalDescription": "The Experimental Statistics module in our platform provides indicators derived from the Insights and Foresights Platform (Bayaan) data.", "official": "Official", "officialDescription": "These are key official indicators which are published and approved by SCAD Statisticians and these indicators are from 6 main statistics from SCAD.", "myPanel": "My Panel", "Ab": "Ab", "theFirstIndicatorIsrequired": "The first indicator is required", "atLeastOneSecondIndicatorShouldBeSelected": "At least one second indicator should be selected", "computeDataBadge": "Compute data", "compareStatistics": "Compare Statistics", "onboarding5": "Your personalized portal for tailored assistance! Select your domain, theme, and sub-theme, then attach any relevant files to your question. Our dedicated team is here to provide customized responses promptly", "onboardingTitle1": "2,700+ statistics to explore", "onboardingTitle2": "Domain", "onboardingTitle3": "My Panel", "onboardingTitle4": "Products", "onboardingTitle5": "Ask Us", "unableToProcessTheRequest": "Unable to process the request", "unknownResponse": "Unknown response", "requestedResourceIsMissing": "Requested resource is missing", "sessionExpired": "Session expired. Please login to continue", "unauthorized": "Unauthorized", "badRequest": "Bad Request", "badResponse": "Bad Response", "networkError": "Network error", "requestHasBeenTimedOut": "Request has been timed out", "badGatewayError": "Bad Gateway Error", "serviceUnavailableError": "Service Unavailable Error", "youRequirePermissionToAccessThisModule": "You require permission to access this module", "Monthly": "Monthly", "Quarterly": "Quarterly", "Yearly": "Yearly", "indicatorDownloadRestricted": "To ensure confidentiality, the download option of this indicator is restricted.", "vacancies": "Vacancies", "seekers": "Seekers", "unableToDownloadFile": "Unable to download file", "noResults": "No results", "pleaseEnterAMessageOrAttachAFile": "Please enter a message or attach a file", "unableToAddMapLayer": "Unable to add map layer", "unableToApplyLicense": "Unable to apply license", "unableToClearMapLayers": "Unable to clear Map Layers", "unableToClearPOILayers": "Unable to clear POI Layers", "unableToGetFeatureQueryResult": "Unable to get Feature Query Result", "unableToSelectMapLayer": "Unable to select map layer", "unableToLoadLayer": "Unable to load layer", "unableToLoadMapLayer": "Unable to load map layer", "selectFeatureFailed": "Select feature failed", "unableToCompleteTheOperation": "Unable to complete the operation", "unableToUpdateLayer": "Unable to update layer", "unableToLoadMapData": "Unable to load map data", "unableToLoadDistrictLayer": "Unable to load district layer", "unableToLoadPOILayer": "Unable to load POI layer", "unableToQuery": "Unable to query", "cannotRedo": "Cannot redo", "cannotUndo": "Cannot undo", "unableToRemoveMapLayer": "Unable to remove map layer", "errorRetrievingGeometry": "Error retrieving geometry", "notAValidSelection": "Not a valid selection", "unableToInitializeMap": "Unable to initialize map", "specialization": "Specialization", "continueWithEmail": "Continue with email", "makeEmailAsDefaultLogin": "Make email as default login", "continue1": "Continue", "didNotReceiveTheCode": "Didn't receive the code?", "pleaseInputTheOtp": "Please input the otp", "executiveInsightsReport": "Executive Insights Report", "welcomeToAskUs": "Welcome to Ask Us!", "welcomeToAskUsLine1": "Ask indicator related questions. Get immediate and accurate assistance by our experts.", "welcomeToAskUsLine2": "Upload images to support your questions.", "clickHere": "Click Here", "dataQueryLinkPopupMessage": "Please note that this chat is intended for your clarifications. If you have a data request, please ", "recent": "Recent", "buildingByUse": "Building by use", "buildingByType": "Building by type", "unitsByType": "Units by type", "housingUnit": "Housing unit", "noOfUnits": "Number of Units", "numberOfbBuildings": "Number of Buildings", "smeCreateChatWarningMessage": "You are asking a question in the same domain in which you are assigned.", "comparingTo": "Comparing to", "searchInPublications": "Search in Publications", "sort": "Sort", "domain": "Domain", "issueDate": "Issue Date", "title": "Title", "category": "Category", "year": "Year", "loggingIn": "Logging In..", "appUpdate": "App Update", "releaseDate": "Release Date", "currentVersion": "Current Version", "newVersion": "New Version", "updateNow": "Update Now", "installNow": "Install Now", "checkForUpdates": "Check For Update", "whatsNew": "What’s New!", "yourVersionIsUpToDate": "Your version is up to date", "newUpdateAvailable": "New Update Available!", "updateMessage": "Discover the latest features and improvements in our newest update. Your app just got better!", "pleaseWaitWhileDownloadingTheUpdate": "Please wait while downloading the update.", "pleaseInstallTheUpdate": "Please install the update", "governmentAffairs": "Government Affairs", "trustedZone": "Trusted Zone", "entitiesZone": "Entities Zone", "newsletters": "Newsletters", "newslettersGuideDesc": "A periodic publication that provides updates, news, and insights related to statistical research, methods, applications, and trends of a certain topic.", "open": "Open", "closed": "Closed", "feelFreeToDescribeQuery": "Feel free to describe your query.", "noCompatibleAppFound": "No compatible app found", "fileNotFound": "File not found", "preview": "Preview", "unsupportedFileType": "Unsupported file type", "close": "Close", "askUsGuideContent": "Please click Like if you’re satisfied with the SME’s response to end the chat, or click Dislike if you’re not satisfied to continue the conversation.", "satisfied": "Satisfied", "dissatisfied": "Dissatisfied", "thankYouFeedback": "We’re glad you’re satisfied with the response. <PERSON>lick ‘Close’ to end the chat.", "notSatisfiedResponse": "We’re sorry that you are not satisfied with the response. Please provide more details on what you need, and we’ll continue assisting you.", "downloadHistory": "Download History", "loadMore": "Load More", "indicator": "Indicator", "spatial": {"regions": "Regions", "district": "District", "communities": "Communities", "citizenship": "Citizenship", "gender": "Gender", "maritalStatus": "Marital Status", "householdType": "Household Type", "compare": "Compare", "quarterly": "Quarterly", "yearly": "Yearly", "education": "Education", "searchArea": "Search area", "spatialStatistics": "Spatial Statistics", "spatialStatsDescription": "The Geospatial Map offers a dynamic visual representation of the Abu Dhabi Emirate.", "opacity": "Background Opacity", "chartType": "Chart Type", "barChart": "Bar Chart", "pieChart": "Pie Chart", "doughnutChart": "Doughnut Chart", "horizontalBarChart": "Horizontal Bar Chart", "explore": "Explore", "unableToUpdateHeatmapLayer": "Unable to update heatmap layer", "unableToHighlightArea": "Unable to highlight area", "noAreaFound": "No area found", "unableToIdentifyTheArea": "Unable to identify the area", "atLeastOneLegendShouldBeEnabled": "At least one legend should be enabled", "modifyFilters": "Modify filters", "buildingByUse": "Building by use", "buildingByType": "Building by type", "unitsByType": "Units by type", "unitsByUse": "Units by use", "employees": "Employees", "residents": "Residents", "buildings": "Buildings", "heatMap": "Heat Map", "dotDensity": "Dot Density Map", "thematicMap": "Thematic Map", "noData": "No Data Found", "distanceMeasurement": "Distance Measurement", "areaMeasurement": "Area Measurement", "smallAreaSelection": "Small Area Selection", "mapTypes": "Map Types", "area": "Area", "perimeter": "Perimeter", "distance": "Distance", "pointOfInterest": "Point of Interest", "cannotRedo": "Cannot redo", "cannotUndo": "Cannot undo", "abuDhabiEmirate": "Abu Dhabi Emirate", "more": "more", "low": "Low", "high": "High", "dot": "Dot"}, "insight": {"insights": "Insights", "addNewInsight": "Add New Insight", "editInsight": "Edit Insight", "manageInsights": "Manage Insights", "approvedInsights": "Approved Insights", "awaitingApproval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "drafts": "Drafts", "remove": "Remove", "submitForApproval": "Submit for Approval", "saveAsDraft": "Save as Draft", "approve": "Approve", "reject": "Reject", "requestEdit": "Request Edit", "reason": "Reason", "typeHere": "Type Here", "enterMessage": "Enter message", "insightShouldNotBeEmpty": "Insight should not be empty", "invalidInsight": "Invalid insight", "invalidMessage": "Invalid message", "areYouSureYouWantToDeleteThisInsight": "Are you sure you want to delete this insight?", "maximumLimitOfCharactersAllowed": "Maximum limit of ({NUM}/500) characters allowed", "approverHasRequestedToEditThisInsight": "Approver has requested to edit this insight.", "insightInfo": "Chart insights refers to the interpretation and understanding of patterns, trends, and key information derived from visual data representations like graph or charts.", "manageInsightInfo": "Click the three dots (•••) in the top-right corner to bulk select insights and either approve or reject them. You can also manage each insight individually using the three dots on its respective card.", "more": "more"}, "genAI": {"bayaanAI": "Bayaan AI", "askBayaan": "Empowering you for a better tomorrow", "general": "General", "text": "Text", "chart": "Chart", "threads": "Threads", "recentThreads": "Recent Threads", "regenerateResponse": "Regenerate Response", "aiTools": "AI Tools", "message": "Message", "tools": "Tools", "copy": "Copy", "regenerate": "Regenerate", "topThreads": "Top Threads", "today": "Today", "yesterday": "Yesterday", "sevenDaysAgo": "Since 7 days", "lastMonth": "Last month", "bar": "Bar", "line": "Line", "table": "Table", "allPrompts": "All prompts", "viewAllPrompts": "View Example Prompts", "beta": "Beta", "feedback": "<PERSON><PERSON><PERSON>", "giveFeedback": "<PERSON>", "generalFeedbackHint": "Tell us what went well or what could be improved", "askSomething": "Ask something", "howSatisfiedWithBayaan": "How satisfied are you with the Bayaan AI platform?", "letUsKnowHowToImprove": "Let us know how we can improve", "disclaimer": "Bayaan AI may occasionally make mistakes. Users are advised to verify critical details independently.", "bayaanAiSettings": "Bayaan AI Settings", "sourceOfData": "Source of Data", "scadOnly": "SCAD Only", "scadWithPublic": "SCAD with Public", "lengthOfResponse": "Length of Response:", "short": "Short", "detailed": "Detailed", "version": "Version", "dataSource": "Data Source", "welcomeToBayaanAI": "Welcome to Bayaan AI", "yourIntelligentAssistantForInsightsChooseADomainForPromptSuggestion": "Your intelligent assistant for insights. Choose a domain for prompt suggestion.", "generatedResponsesMayContainErrorsConfirmImportantInfo": "Generated responses may contain errors. Confirm important info", "reason": "Reason", "promptAssistance": "Prompt Assistance", "savedChats": "Saved chats", "chatHistory": "Chat history", "previous30Days": "Previous 30 days", "chatSelectionHint": "Press and hold a chat to pin or delete it.", "pin": "<PERSON>n", "unpin": "Unpin", "delete": "Delete", "selectDataSource": "Select data source", "selectDataSourceDescription": "Choose whether you want insights from SCAD data only, or include public data sources for a broader analysis.", "responseLength": "Response length", "responseLengthDescription": "Choose whether you prefer brief, to-the-point answers or detailed, comprehensive explanations."}}