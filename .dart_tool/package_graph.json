{"roots": ["bayaan", "excel_view", "flutter_mute", "lean_file_picker", "root_jailbreak_sniffer", "scad_mobile", "scad_mobile_public", "sound_mode", "uae_pass_flutter"], "packages": [{"name": "bayaan", "version": "0.0.0", "dependencies": ["flutter_local_notifications", "go_router"], "devDependencies": ["flutter_lints", "very_good_analysis"]}, {"name": "scad_mobile", "version": "1.1.2+2044", "dependencies": ["apk_installer", "app_links", "audioplayers", "auto_route", "carousel_slider", "cart_stepper", "control_style", "crypto", "cryptography", "device_info_plus", "dio", "dotted_border", "dropdown_button2", "easy_localization", "equatable", "excel", "excel_view", "expandable", "firebase_analytics", "firebase_core", "firebase_messaging", "firebase_remote_config", "flutter", "flutter_bloc", "flutter_chat_bubble", "flutter_foreground_task", "flutter_inset_box_shadow", "flutter_markdown", "flutter_mute", "flutter_pdfview", "flutter_rating_bar", "flutter_sliding_up_panel", "flutter_svg", "flutter_typeahead", "flutter_udid", "flutter_widget_from_html_core", "flutter_xlider", "get_it", "google_fonts", "hive_flutter", "http", "http_certificate_pinning", "http_parser", "image", "image_picker", "intl", "jovial_svg", "jwt_decoder", "lean_file_picker", "local_auth", "lottie", "open_filex", "overlay_support", "package_info_plus", "path", "path_provider", "permission_handler", "pinput", "real_volume", "root_jailbreak_sniffer", "screenshot", "scrollable_positioned_list", "shimmer", "shorebird_code_push", "sliding_up_panel2", "smooth_page_indicator", "sound_mode", "syncfusion_flutter_charts", "syncfusion_flutter_core", "syncfusion_flutter_datagrid", "syncfusion_flutter_pdf", "syncfusion_flutter_sliders", "syncfusion_flutter_treemap", "syncfusion_flutter_xlsio", "timeline_tile", "uae_pass_flutter", "url_launcher", "uuid", "webview_flutter"], "devDependencies": ["auto_route_generator", "build_runner", "flutter_test"]}, {"name": "scad_mobile_public", "version": "1.0.11+27", "dependencies": ["audioplayers", "auto_route", "carousel_slider", "cart_stepper", "device_info_plus", "dio", "dotted_border", "dropdown_button2", "easy_localization", "equatable", "excel", "expandable", "firebase_analytics", "firebase_core", "firebase_remote_config", "flutter", "flutter_bloc", "flutter_chat_bubble", "flutter_inset_box_shadow", "flutter_mute", "flutter_pdfview", "flutter_rating_bar", "flutter_sliding_up_panel", "flutter_svg", "flutter_typeahead", "flutter_udid", "flutter_widget_from_html_core", "flutter_xlider", "get_it", "google_fonts", "hive_flutter", "http_certificate_pinning", "http_parser", "image", "intl", "jwt_decoder", "local_auth", "lottie", "open_filex", "overlay_support", "package_info_plus", "path_provider", "permission_handler", "pinput", "real_volume", "root_jailbreak_sniffer", "screenshot", "scrollable_positioned_list", "shimmer", "sliding_up_panel2", "smooth_page_indicator", "sound_mode", "syncfusion_flutter_charts", "syncfusion_flutter_core", "syncfusion_flutter_datagrid", "syncfusion_flutter_pdf", "syncfusion_flutter_sliders", "syncfusion_flutter_treemap", "syncfusion_flutter_xlsio", "timeline_tile", "url_launcher", "webview_flutter"], "devDependencies": ["auto_route_generator", "build_runner", "flutter_test"]}, {"name": "excel_view", "version": "0.0.1", "dependencies": ["excel", "flutter", "provider"], "devDependencies": ["flutter_test"]}, {"name": "flutter_mute", "version": "0.0.4", "dependencies": ["flutter"], "devDependencies": ["flutter_test"]}, {"name": "lean_file_picker", "version": "0.0.2", "dependencies": ["flutter"], "devDependencies": ["flutter_test"]}, {"name": "root_jailbreak_sniffer", "version": "1.0.6", "dependencies": ["flutter", "plugin_platform_interface"], "devDependencies": ["flutter_test"]}, {"name": "sound_mode", "version": "3.0.0", "dependencies": ["flutter"], "devDependencies": ["flutter_test"]}, {"name": "uae_pass_flutter", "version": "0.0.3", "dependencies": ["flutter", "plugin_platform_interface"], "devDependencies": ["flutter_test"]}, {"name": "very_good_analysis", "version": "7.0.0", "dependencies": []}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "go_router", "version": "15.1.3", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "flutter_local_notifications", "version": "19.4.1", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "provider", "version": "6.1.5+1", "dependencies": ["collection", "flutter", "nested"]}, {"name": "excel", "version": "4.0.6", "dependencies": ["archive", "collection", "equatable", "xml"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "auto_route_generator", "version": "10.2.3", "dependencies": ["analyzer", "auto_route", "build", "build_runner", "code_builder", "dart_style", "glob", "lean_builder", "path", "source_gen"]}, {"name": "auto_route", "version": "10.1.2", "dependencies": ["collection", "flutter", "meta", "path", "web"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "firebase_remote_config", "version": "5.5.0", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_remote_config_platform_interface", "firebase_remote_config_web", "flutter"]}, {"name": "audioplayers", "version": "6.5.0", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "real_volume", "version": "1.0.9", "dependencies": ["flutter"]}, {"name": "http_certificate_pinning", "version": "3.0.1", "dependencies": ["dio", "flutter", "http"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "flutter_udid", "version": "4.0.0", "dependencies": ["crypto", "flutter"]}, {"name": "open_filex", "version": "4.7.0", "dependencies": ["ffi", "flutter"]}, {"name": "flutter_sliding_up_panel", "version": "2.1.1", "dependencies": ["flutter"]}, {"name": "flutter_typeahead", "version": "5.2.0", "dependencies": ["flutter", "flutter_keyboard_visibility", "pointer_interceptor"]}, {"name": "device_info_plus", "version": "11.5.0", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "permission_handler", "version": "12.0.1", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "lottie", "version": "3.3.0", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "webview_flutter", "version": "4.13.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "cart_stepper", "version": "4.3.0", "dependencies": ["flutter", "intl"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "screenshot", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "image", "version": "4.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "flutter_pdfview", "version": "1.4.1+1", "dependencies": ["flutter"]}, {"name": "syncfusion_flutter_datagrid", "version": "29.2.11", "dependencies": ["collection", "flutter", "syncfusion_flutter_core"]}, {"name": "syncfusion_flutter_treemap", "version": "29.2.11", "dependencies": ["flutter", "syncfusion_flutter_core"]}, {"name": "syncfusion_flutter_core", "version": "29.2.11", "dependencies": ["flutter", "vector_math"]}, {"name": "syncfusion_flutter_pdf", "version": "29.2.11", "dependencies": ["convert", "crypto", "flutter", "http", "intl", "syncfusion_flutter_core", "xml"]}, {"name": "syncfusion_flutter_charts", "version": "29.2.11", "dependencies": ["flutter", "intl", "syncfusion_flutter_core", "vector_math"]}, {"name": "syncfusion_flutter_xlsio", "version": "29.2.11", "dependencies": ["archive", "crypto", "flutter", "image", "intl", "jiffy", "syncfusion_officecore", "xml"]}, {"name": "syncfusion_flutter_sliders", "version": "29.2.11", "dependencies": ["flutter", "intl", "syncfusion_flutter_core"]}, {"name": "scrollable_positioned_list", "version": "0.3.8", "dependencies": ["collection", "flutter"]}, {"name": "flutter_inset_box_shadow", "version": "1.0.8", "dependencies": ["flutter"]}, {"name": "smooth_page_indicator", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "carousel_slider", "version": "5.1.1", "dependencies": ["flutter"]}, {"name": "overlay_support", "version": "2.1.0", "dependencies": ["async", "flutter"]}, {"name": "flutter_chat_bubble", "version": "2.0.2", "dependencies": ["flutter"]}, {"name": "timeline_tile", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "sliding_up_panel2", "version": "3.3.0+1", "dependencies": ["flutter"]}, {"name": "dropdown_button2", "version": "2.3.9", "dependencies": ["flutter", "meta"]}, {"name": "expandable", "version": "5.0.1", "dependencies": ["flutter"]}, {"name": "dotted_border", "version": "2.1.0", "dependencies": ["flutter", "path_drawing"]}, {"name": "pinput", "version": "5.0.1", "dependencies": ["flutter", "universal_platform"]}, {"name": "flutter_widget_from_html_core", "version": "0.16.1", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "flutter_rating_bar", "version": "4.0.1", "dependencies": ["flutter"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "firebase_analytics", "version": "11.6.0", "dependencies": ["firebase_analytics_platform_interface", "firebase_analytics_web", "firebase_core", "firebase_core_platform_interface", "flutter"]}, {"name": "firebase_core", "version": "3.15.2", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "easy_localization", "version": "3.0.8", "dependencies": ["args", "easy_logger", "flutter", "flutter_localizations", "intl", "path", "shared_preferences"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "flutter_xlider", "version": "3.5.0", "dependencies": ["flutter"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "google_fonts", "version": "6.3.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "flutter_bloc", "version": "9.1.1", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "jwt_decoder", "version": "2.0.1", "dependencies": []}, {"name": "local_auth", "version": "2.3.0", "dependencies": ["flutter", "local_auth_android", "local_auth_darwin", "local_auth_platform_interface", "local_auth_windows"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "get_it", "version": "8.2.0", "dependencies": ["async", "collection", "meta"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "arcgis_maps", "version": "200.8.0+4672", "dependencies": ["async", "convert", "crypto", "device_info_plus", "dio", "dio_cache_interceptor", "ffi", "fixnum", "flutter", "flutter_secure_storage", "flutter_web_auth_2", "geolocator", "haptic_feedback", "http", "http_cache_drift_store", "http_parser", "intl", "logger", "package_config", "package_info_plus", "path", "path_provider", "pointycastle", "sqlite3", "sqlite3_flutter_libs", "url_launcher"]}, {"name": "cryptography", "version": "2.7.0", "dependencies": ["collection", "crypto", "ffi", "js", "meta", "typed_data"]}, {"name": "flutter_foreground_task", "version": "9.1.0", "dependencies": ["flutter", "platform", "plugin_platform_interface", "shared_preferences"]}, {"name": "control_style", "version": "0.1.0", "dependencies": ["flutter"]}, {"name": "jovial_svg", "version": "1.1.27", "dependencies": ["args", "collection", "flutter", "http", "jovial_misc", "meta", "vector_math", "xml"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "shorebird_code_push", "version": "2.0.4", "dependencies": ["ffi", "meta"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "flutter_markdown", "version": "0.7.7+1", "dependencies": ["flutter", "markdown", "meta", "path"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "apk_installer", "version": "0.0.4", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "image_picker", "version": "1.2.0", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "app_links", "version": "6.4.1", "dependencies": ["app_links_linux", "app_links_platform_interface", "app_links_web", "flutter"]}, {"name": "firebase_messaging", "version": "15.2.10", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.1.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.2", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "7.4.5", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "lean_builder", "version": "0.1.0-alpha.11", "dependencies": ["_fe_analyzer_shared", "analyzer", "ansicolor", "args", "collection", "dart_style", "frontend_server_client", "glob", "<PERSON><PERSON><PERSON><PERSON>", "lints", "meta", "path", "source_span", "stack_trace", "test", "watcher", "xxh3", "yaml"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "firebase_remote_config_web", "version": "1.8.9", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_remote_config_platform_interface", "flutter", "flutter_web_plugins"]}, {"name": "firebase_remote_config_platform_interface", "version": "2.0.0", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "audioplayers_windows", "version": "4.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "5.1.1", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "audioplayers_platform_interface", "version": "7.1.1", "dependencies": ["collection", "flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "4.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "6.3.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "5.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "pointer_interceptor", "version": "0.10.1+2", "dependencies": ["flutter", "flutter_web_plugins", "pointer_interceptor_ios", "pointer_interceptor_platform_interface", "pointer_interceptor_web"]}, {"name": "flutter_keyboard_visibility", "version": "6.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_platform_interface", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows", "meta"]}, {"name": "win32_registry", "version": "2.1.0", "dependencies": ["ffi", "meta", "win32"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "13.0.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "webview_flutter_wkwebview", "version": "3.23.0", "dependencies": ["flutter", "meta", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.14.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.10.1", "dependencies": ["flutter", "meta", "webview_flutter_platform_interface"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.18", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "syncfusion_officecore", "version": "29.2.11", "dependencies": ["flutter", "syncfusion_flutter_core"]}, {"name": "jiffy", "version": "6.4.3", "dependencies": ["intl", "quiver"]}, {"name": "path_drawing", "version": "1.0.1", "dependencies": ["flutter", "meta", "path_parsing", "vector_math"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "firebase_analytics_web", "version": "0.5.10+16", "dependencies": ["_flutterfire_internals", "firebase_analytics_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "firebase_analytics_platform_interface", "version": "4.4.3", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "easy_logger", "version": "0.0.2", "dependencies": ["flutter"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "bloc", "version": "9.0.0", "dependencies": ["meta"]}, {"name": "vector_graphics_compiler", "version": "1.1.19", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "local_auth_windows", "version": "1.0.11", "dependencies": ["flutter", "local_auth_platform_interface"]}, {"name": "local_auth_platform_interface", "version": "1.0.10", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "local_auth_darwin", "version": "1.6.0", "dependencies": ["flutter", "intl", "local_auth_platform_interface"]}, {"name": "local_auth_android", "version": "1.0.52", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "intl", "local_auth_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "sqlite3_flutter_libs", "version": "0.5.39", "dependencies": ["flutter"]}, {"name": "sqlite3", "version": "2.9.0", "dependencies": ["collection", "ffi", "meta", "path", "typed_data", "web"]}, {"name": "pointycastle", "version": "4.0.0", "dependencies": ["collection", "convert"]}, {"name": "logger", "version": "2.6.1", "dependencies": ["meta"]}, {"name": "http_cache_drift_store", "version": "7.0.0", "dependencies": ["drift", "http_cache_core", "path"]}, {"name": "haptic_feedback", "version": "0.5.1+1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "geolocator", "version": "14.0.2", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_linux", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "flutter_web_auth_2", "version": "4.1.0", "dependencies": ["desktop_webview_window", "flutter", "flutter_web_auth_2_platform_interface", "flutter_web_plugins", "path_provider", "url_launcher", "web", "window_to_front"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "dio_cache_interceptor", "version": "4.0.3", "dependencies": ["dio", "http_cache_core"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "jovial_misc", "version": "0.9.2", "dependencies": ["async", "collection", "convert", "meta"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "image_picker_windows", "version": "0.2.2", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.11.0", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.13", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.1.0", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.13+1", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "app_links_web", "version": "1.0.4", "dependencies": ["app_links_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "app_links_platform_interface", "version": "2.0.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "app_links_linux", "version": "1.0.3", "dependencies": ["app_links_platform_interface", "flutter", "gtk"]}, {"name": "firebase_messaging_web", "version": "3.10.10", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.10", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "built_value", "version": "8.11.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "_fe_analyzer_shared", "version": "82.0.0", "dependencies": ["meta"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.0", "dependencies": ["collection", "logging", "path", "stream_transform", "vm_service", "watcher"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "xxh3", "version": "1.2.0", "dependencies": []}, {"name": "_flutterfire_internals", "version": "1.3.59", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "pointer_interceptor_web", "version": "0.10.3", "dependencies": ["flutter", "flutter_web_plugins", "plugin_platform_interface", "pointer_interceptor_platform_interface", "web"]}, {"name": "pointer_interceptor_platform_interface", "version": "0.10.0+1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "pointer_interceptor_ios", "version": "0.10.1", "dependencies": ["flutter", "plugin_platform_interface", "pointer_interceptor_platform_interface"]}, {"name": "flutter_keyboard_visibility_windows", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_keyboard_visibility_macos", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_linux", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "drift", "version": "2.28.1", "dependencies": ["async", "collection", "convert", "meta", "path", "sqlite3", "stack_trace", "stream_channel", "web"]}, {"name": "http_cache_core", "version": "1.1.1", "dependencies": ["collection", "string_scanner", "uuid"]}, {"name": "geolocator_linux", "version": "0.2.3", "dependencies": ["dbus", "flutter", "geoclue", "geolocator_platform_interface", "gsettings", "package_info_plus"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "5.0.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "window_to_front", "version": "0.0.3", "dependencies": ["flutter"]}, {"name": "flutter_web_auth_2_platform_interface", "version": "4.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "desktop_webview_window", "version": "0.2.3", "dependencies": ["flutter", "path"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "gtk", "version": "2.1.0", "dependencies": ["ffi", "flutter", "meta"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "coverage", "version": "1.15.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "gsettings", "version": "0.2.8", "dependencies": ["dbus", "xdg_directories"]}, {"name": "geoclue", "version": "0.1.1", "dependencies": ["dbus", "meta"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}], "configVersion": 1}